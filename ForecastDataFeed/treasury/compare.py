import os
from datetime import datetime
import pytz
import pandas as pd

from shared_compare import Compare

# loading variables from .env file
from dotenv import load_dotenv
load_dotenv() 

# Initialize logger
from logger import setup_logger
logger = setup_logger()

download_path = os.path.join(os.getenv("DOWNLOAD_PATH"), "treasury")


if __name__== "__main__":  
     try:  
          compare_instance = Compare(
               old_filename="treasury_changes.csv", 
               new_filename="Opportunity Data.xls",
               csv_file="extracted_data.csv",
               key="Specific Id", 
               download_path=os.path.join(os.getenv("DOWNLOAD_PATH"), "treasury")
               )
          compare_instance.convert_xls_to_csv()
          compare_instance.compare_files()
          
          logger.info("Compare for Treasury successfull...")
     except Exception as e:
            error_message = str(e)
            logger.error(f"Compare for Treasury failed: {error_message}")
  
  