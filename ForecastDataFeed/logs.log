2024-12-03 00:57:02,163 - INFO - /Users/<USER>/Documents/open source/procurement-forecast/usaid/main.py - Starting USAID scraping execution
2024-12-03 00:57:16,142 - INFO - /Users/<USER>/Documents/open source/procurement-forecast/usaid/main.py - Scraped USAID successfully...
2024-12-03 00:57:16,671 - INFO - /Users/<USER>/Documents/open source/procurement-forecast/usaid/compare.py - Compare for USAID successfull...
2024-12-03 00:57:17,496 - INFO - /Users/<USER>/Documents/open source/procurement-forecast/homeland/main.py - Starting Homeland scraping execution
2024-12-03 00:57:33,072 - INFO - /Users/<USER>/Documents/open source/procurement-forecast/homeland/main.py - Scraped Homeland successfully...
2024-12-03 00:57:33,534 - INFO - /Users/<USER>/Documents/open source/procurement-forecast/homeland/compare.py - Compare for Homeland successfull...
2024-12-03 00:57:33,958 - INFO - /Users/<USER>/Documents/open source/procurement-forecast/agric/main.py - Starting Agric scraping execution
2024-12-03 00:58:09,575 - INFO - /Users/<USER>/Documents/open source/procurement-forecast/agric/main.py - Successfully downloaded forecasts for Department of Agriculture...
2024-12-03 00:58:32,700 - INFO - /Users/<USER>/Documents/open source/procurement-forecast/agric/compare.py - Compare for Agriculture successfull...
2024-12-03 00:58:33,363 - INFO - /Users/<USER>/Documents/open source/procurement-forecast/treasury/main.py - Starting Treasury scraping execution
2024-12-03 00:58:46,416 - INFO - /Users/<USER>/Documents/open source/procurement-forecast/treasury/main.py - Scraped Treasury successfully...
2024-12-03 00:58:46,691 - INFO - /Users/<USER>/Documents/open source/procurement-forecast/treasury/compare.py - Compare for Treasury successfull...
