import os 
import pandas as pd
from bs4 import BeautifulSoup

class Compare:
     def __init__(self, old_filename, new_filename, key, download_path, csv_file=None):
        self.old_filename = old_filename
        self.new_filename = new_filename
        self.csv_file = csv_file if csv_file is not None else ""
        self.key = key
        self.download_path = download_path

     def create_new_file(self, changes):
         changes.to_csv(os.path.join(self.download_path, self.old_filename), index=False)


     def compare_files(self):
          file = os.path.join(self.download_path, self.new_filename)
          old_file = os.path.join(self.download_path, self.old_filename)
          changes = pd.DataFrame()  # Create an empty DataFrame
          data = pd.DataFrame()  # Create an empty DataFrame
          
          if not os.path.exists(file):
               print(f"Error: The file '{file}' does not exist.")
          else:
               data = pd.read_csv(file, encoding='ISO-8859-1')
               if not os.path.exists(old_file):
                    print("No old file to compare to, good to go!")
                    # Function to create file that would be sent to database, also paste into old file always used for comparison
                    self.create_new_file(data)
                    return
               else: 
                    old_data = pd.read_csv(old_file, encoding='ISO-8859-1')
                    print(len(old_data), len(data))
               

          # Loop through each row in the new data
          for index, row in data.iterrows():
               plan_id = row[self.key]
               
               # Check if the plan_id exists in old_data
               if plan_id in old_data[self.key].values:
                    # Get the old row corresponding to the plan_id
                    old_row = old_data[old_data[self.key] == plan_id].iloc[0]
                    
                    # Check for changes in any field
                    if not row.equals(old_row):
                         changes = pd.concat([changes, pd.DataFrame([row])], ignore_index=True)  # Updated to use pd.concat
               else:
                    # If the plan_id does not exist in old_data, add the row to changes
                    changes = pd.concat([changes, pd.DataFrame([row])], ignore_index=True)  # Updated to use pd.concat

          print(f"Changes detected: {len(changes)}")
          
          # if there are changes, create a new file, and store it
          if len(changes) > 0:
               self.create_new_file(changes)
                  
               
     def convert_xls_to_csv(self):
          file = os.path.join(self.download_path, self.new_filename)
          to_csv_file = os.path.join(self.download_path, self.csv_file)
          
          with open(file,'r') as firstfile: 
               count = 0;
               # read content from first file 
               for line in firstfile: 
                    if '<table' in line:
                        soup = BeautifulSoup(line, 'html.parser')
                        rows = []
                        for row in soup.find_all('tr'):
                              cells = [td.text for td in row.find_all('th')]
                              if cells:  # Only add rows that have data
                                rows.append(cells)
                        
                        # Create a DataFrame and save to CSV
                        df = pd.DataFrame(rows[1:], columns=rows[0])
                        df.to_csv(to_csv_file, index=False)
                        print("Data extracted and saved to 'extracted_data.csv' successfully.")
                        count += 1
          # change new filename for comaprison to csv file just downloaded
          self.new_filename = self.csv_file 
          return
          

