# All selenium packages
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys 
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# Import os
import os

# Import time
import time

from dotenv import load_dotenv
load_dotenv()

# Initialize logger
from logger import setup_logger
logger = setup_logger()


# US Agency for International Development

# Set download path of international file
download_path = os.path.join(os.getenv("DOWNLOAD_PATH"), "usaid")

# Create directory if it does not exist
os.makedirs(download_path, exist_ok=True)

# set chromeoptions to download files to the set directory
chrome_options = webdriver.ChromeOptions()
prefs = {'download.default_directory' : download_path, 'profile.default_content_setting_values.automatic_downloads': 1}
chrome_options.add_experimental_option('prefs', prefs)
chrome_options.add_experimental_option('prefs', prefs)
chrome_options.add_argument('--headless')
chrome_options.add_argument('--no-sandbox')
chrome_options.add_argument('--disable-dev-shm-usage')
chrome_browser = os.getenv("CHROME_BROWSER")
if not chrome_browser:
    raise EnvironmentError("CHROME_BROWSER environment variable is not set. Please add the path to the Chrome Testing app in the .env file.")
chrome_options.binary_location = chrome_browser


driver = webdriver.Chrome(options=chrome_options
                          )
def scrapewebsite():
     logger.info("Starting USAID scraping execution")
     driver.get("https://www.usaid.gov/business-forecast")
     
     forecasts = WebDriverWait(driver, 15).until(
               EC.visibility_of_element_located((By.XPATH, "//a[@data-entity-uuid='edd560bd-425b-417e-ac9c-864b99a980c3']"))
               )
     forecasts.click()
     
     # Click on download CSV file
     download_button = WebDriverWait(driver, 15).until(
               EC.visibility_of_element_located((By.XPATH, "//div[@class='csv-feed views-data-export-feed']"))
               )
     download_button.click()
     
     time.sleep(5)
     
     driver.quit()
     
     

if __name__== "__main__":
     try:
          scrapewebsite()
          logger.info("Scraped USAID successfully...")
     except Exception as e:
          error_message = str(e)
          logger.error(f"Scraper for USAID failed: {error_message}")
  