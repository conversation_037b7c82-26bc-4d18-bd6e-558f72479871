import os
from datetime import datetime
import pytz
import pandas as pd

from shared_compare import Compare

# Initialize logger
from logger import setup_logger
logger = setup_logger()


# loading variables from .env file
from dotenv import load_dotenv
load_dotenv() 


download_path = os.path.join(os.getenv("DOWNLOAD_PATH"), "usaid")

if __name__== "__main__":
      try:
            # Get today's date in Central Time
            central_tz = pytz.timezone('America/Chicago')
            today_date = datetime.now(central_tz).strftime('%Y-%m-%d')
            
            # New filename of the newly downloaded file
            filename = f"usaid-business-forecast-{today_date}.csv"

            
            compare_instance = Compare(
                  old_filename="usaid_changes.csv", 
                  new_filename=filename,
                  key="A&A Plan Id", 
                  download_path=os.path.join(os.getenv("DOWNLOAD_PATH"), "usaid")
                  )
            compare_instance.compare_files()
            logger.info("Compare for USAID successfull...")
      except Exception as e:
            error_message = str(e)
            logger.error(f"Compare for USAID failed: {error_message}")
  