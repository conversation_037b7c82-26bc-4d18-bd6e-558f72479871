# Public Forecast List          Acquisition Planning Forecast System

import os
from datetime import datetime
import pytz
import pandas as pd

from shared_compare import Compare

# loading variables from .env file
from dotenv import load_dotenv
load_dotenv() 

# Initialize logger
from logger import setup_logger
logger = setup_logger()


# It is going to be an inherited class for comparison when done
if __name__== "__main__":
     try:
          compare_instance = Compare(
               old_filename="homeland_changes.csv", 
               new_filename="Public Forecast List          Acquisition Planning Forecast System.csv", 
               key="APFS Number", 
               download_path=os.path.join(os.getenv("DOWNLOAD_PATH"), "homeland")
               )
          compare_instance.compare_files()
          logger.info("Compare for Homeland successfull...")
     except Exception as e:
          error_message = str(e)
          logger.error(f"Compare for Homeland failed: {error_message}")
  
  