# Web Scraper for Procurement Forecasts

This script is designed to scrape opportunities forecast from the website [Acquisition.gov](https://www.acquisition.gov/procurement-forecasts).

## Requirements

- Python 3.x
- Selenium
- Requests

## Getting Started

To get started with this project, follow the steps below:

1. **Install Requirements**: 
   Download the required packages by running the following command in your terminal:
   ```
   pip install -r requirements.txt
   ```

2. **Set Environment Variables**:
   You need to set the following environment variables for the scraper to work:
   - `DOWNLOAD_PATH`: Specify the folder to download the csv files to.

   You can set these variables in your terminal or by creating a `.env` file in the project directory with the following format:
   ```
   DOWNLOAD_PATH=/path/to/table/folder
   ```

---
## USAID Scraping
1. **Run the Scripts**:
   To obtain the latest forecast, execute the following command in your terminal:
   ```
   python -m  homeland.main
   ```
   This script will fetch the most recent procurement forecast data.

2. **Generate the Changes File**:
   After running the first script, you need to generate the `usaid_changes.csv` file. To do this, run the following command:
   ```
   python -m  homeland.compare
   ```
   This script will compare the newly fetched data with the previous data and create the `usaid_changes.csv` file, which can then be added to the database.

---
## Run all scrape and compare files
**Run All Scripts**:
   There is a `runall.sh` file available to run all the scrape and compare files for all forecasts in one go.