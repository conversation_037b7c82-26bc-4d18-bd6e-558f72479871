#!/bin/bash

rm -f logs.log

# Run the USAID main script to fetch the latest forecast data
python3 -m  usaid.main

# Generate the changes file by comparing the newly fetched data
python3 -m  usaid.compare

# Run the Homeland main script to scrape the website
python3 -m  homeland.main

# Generate the changes file by comparing the newly fetched data
python3 -m  homeland.compare

# Run the Homeland main script to scrape the website
python3 -m  agric.main

# Generate the changes file by comparing the newly fetched data
python3 -m  agric.compare

# Run the Homeland main script to scrape the website
python3 -m  treasury.main

# Generate the changes file by comparing the newly fetched data
python3 -m  treasury.compare
