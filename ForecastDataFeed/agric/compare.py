import os
from datetime import datetime
import pytz
import pandas as pd

from shared_compare import Compare

# loading variables from .env file
from dotenv import load_dotenv
load_dotenv() 

# Initialize logger
from logger import setup_logger
logger = setup_logger()


download_path = os.path.join(os.getenv("DOWNLOAD_PATH"), "agric")


if __name__== "__main__":    
      try:
            compare_instance = Compare(
                  old_filename="agric_changes.csv", 
                  new_filename="Opportunities.csv",
                  key="ID", 
                  download_path=os.path.join(os.getenv("DOWNLOAD_PATH"), "agric")
                  )
            compare_instance.compare_files()
            logger.info("Compare for Agriculture successfull...")
      except Exception as e:
            error_message = str(e)
            logger.error(f"Compare for Agriculture failed: {error_message}")
  
  