{"cells": [{"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["import os\n", "from datetime import datetime\n", "import pytz\n", "import pandas as pd\n", "import pandas as pd\n", "# Install BeautifulSoup using pip\n", "from bs4 import BeautifulSoup\n", "import xml.etree.ElementTree as ET\n", "import csv\n", "from io import StringIO\n"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["import os \n", "import pandas as pd\n", "\n", "class Compare:\n", "    def __init__(self, old_filename, new_filename, key, download_path):\n", "        self.old_filename = old_filename\n", "        self.new_filename = new_filename\n", "        self.key = key\n", "        self.download_path = download_path\n", "\n", "    def create_new_file(self, changes):\n", "         changes.to_csv(os.path.join(self.download_path, self.old_filename), index=False)\n", "\n", "\n", "    def compare_files(self):\n", "          file = os.path.join(self.download_path, self.new_filename)\n", "          old_file = os.path.join(self.download_path, self.old_filename)\n", "          changes = pd.DataFrame()  # Create an empty DataFrame\n", "          data = pd.DataFrame()  # Create an empty DataFrame\n", "          \n", "          if not os.path.exists(file):\n", "               print(f\"Error: The file '{file}' does not exist.\")\n", "          else:\n", "               data = pd.read_csv(file, encoding='ISO-8859-1')\n", "               if not os.path.exists(old_file):\n", "                    print(\"No old file to compare to, good to go!\")\n", "                    # Function to create file that would be sent to database, also paste into old file always used for comparison\n", "                    self.create_new_file(data)\n", "                    return\n", "               else: \n", "                    old_data = pd.read_csv(old_file, encoding='ISO-8859-1')\n", "                    print(len(old_data), len(data))\n", "               \n", "\n", "          # Loop through each row in the new data\n", "          for index, row in data.iterrows():\n", "               plan_id = row[self.key]\n", "               \n", "               # Check if the plan_id exists in old_data\n", "               if plan_id in old_data[self.key].values:\n", "                    # Get the old row corresponding to the plan_id\n", "                    old_row = old_data[old_data[self.key] == plan_id].iloc[0]\n", "                    \n", "                    # Check for changes in any field\n", "                    if not row.equals(old_row):\n", "                         changes = pd.concat([changes, pd.DataFrame([row])], ignore_index=True)  # Updated to use pd.concat\n", "               else:\n", "                    # If the plan_id does not exist in old_data, add the row to changes\n", "                    changes = pd.concat([changes, pd.DataFrame([row])], ignore_index=True)  # Updated to use pd.concat\n", "\n", "          print(f\"Changes detected: {len(changes)}\")\n", "          \n", "          # if there are changes, create a new file, and store it\n", "          if len(changes) > 0:\n", "               self.create_new_file(changes)\n", " \n", "                   \n", "    def convert_xls_to_csv(self):\n", "          file = os.path.join(self.download_path, self.new_filename)\n", "          \n", "          with open(file,'r') as firstfile: \n", "               count = 0;\n", "               # read content from first file \n", "               for line in firstfile: \n", "                    if '<table' in line:\n", "                        soup = BeautifulSoup(line, 'html.parser')\n", "                        rows = []\n", "                        for row in soup.find_all('tr'):\n", "                              cells = [td.text for td in row.find_all('th')]\n", "                              if cells:  # Only add rows that have data\n", "                                rows.append(cells)\n", "                        \n", "                        # Create a DataFrame and save to CSV\n", "                        df = pd.DataFrame(rows[1:], columns=rows[0])\n", "                        df.to_csv(os.path.join(os.getenv(\"DOWNLOAD_PATH\"), \"treasury\", \"extracted_data.csv\"), index=False)\n", "                        print(\"Data extracted and saved to 'extracted_data.csv' successfully.\")\n", "                        count += 1\n", "                    \n", "               print(f\"\\n total lines: {count}\")\n", "                    \n", "          # self.extract_table_to_csv(file, os.path.join(self.download_path, 'extracted_file.csv'))\n", "          \n", "          return\n", "          "]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 Line :<table><style>table, th, td {    border: 1px solid black;    border-collapse: collapse;}</style><tr><th style=\"background-color:#3b76b4;\">Bureau</th><th style=\"background-color:#3b76b4;\">Type of Requirement</th><th style=\"background-color:#3b76b4;\">Status</th><th style=\"background-color:#3b76b4;\">ShopCart/req</th><th style=\"background-color:#3b76b4;\">Place of Performance</th><th style=\"background-color:#3b76b4;\">Award Type</th><th style=\"background-color:#3b76b4;\">Contract Type</th><th style=\"background-color:#3b76b4;\">Bureau Point of Contact</th><th style=\"background-color:#3b76b4;\">Program Office Point of Contact</th><th style=\"background-color:#3b76b4;\">NAICS</th><th style=\"background-color:#3b76b4;\">PSC</th><th style=\"background-color:#3b76b4;\">Contract Number</th><th style=\"background-color:#3b76b4;\">Agency</th><th style=\"background-color:#3b76b4;\">Program Office</th><th style=\"background-color:#3b76b4;\">Estimated Total Contract Value</th><th style=\"background-color:#3b76b4;\">Type of Small Business Set-aside</th><th style=\"background-color:#3b76b4;\">Small Business Set-aside</th><th style=\"background-color:#3b76b4;\">Competition</th><th style=\"background-color:#3b76b4;\">Extent Competed</th><th style=\"background-color:#3b76b4;\">Listing Id Type</th><th style=\"background-color:#3b76b4;\">Specific Id</th><th style=\"background-color:#3b76b4;\">Projected Award FY_Qtr</th><th style=\"background-color:#3b76b4;\">Projected Contract Vehicle</th><th style=\"background-color:#3b76b4;\">Project Period of Performance Start</th><th style=\"background-color:#3b76b4;\">Project Period of Performance End</th><th style=\"background-color:#3b76b4;\">Project Period of Performance Close Date</th><th style=\"background-color:#3b76b4;\">Last Modified Date</th></tr><tr><th style=\"background-color:silver;\">ALCOHOL AND TOBACCO TAX AND TRADE BUREAU</th><th>Recompete</th><th>Market Research</th><th>TTB-OCIO-0002</th><th>MD, United States</th><th>Order (under IDC, BPA, BOA, FSS, or GWAC)</th><th>Fixed-Price (see FAR 16.2)</th><th>undefined</th><th>undefined</th><th>541519-Other Computer Related Services</th><th>7J20-IT AND TELECOM - SECURITY AND COMPLIANCE PRODUCTS (HARDWARE AND PERPETUAL LICENSE SOFTWARE)</th><th>20341424F00013</th><th>Treasury</th><th>OFFICE OF ASST ADMIN INFO RES/CIO</th><th>> $250K to < or = $750K</th><th></th><th>Yes</th><th>Yes</th><th>Full and Open Competition with Set-aside After Exclusion of Sources (see FAR 6.2)</th><th>Current Contract Number</th><th>20341424F00013</th><th>FY 2025 Q2</th><th>NASA SEWP (NASA)</th><th>2025-02-01</th><th>2026-01-01</th><th></th><th>2024-10-08T00:02:22.000Z</th></tr><tr><th style=\"background-color:silver;\">ALCOHOL AND TOBACCO TAX AND TRADE BUREAU</th><th>Recompete</th><th>Market Research</th><th>TTB-OCIO-0003</th><th>VA, United States</th><th>Order (under IDC, BPA, BOA, FSS, or GWAC)</th><th>Fixed-Price (see FAR 16.2)</th><th>undefined</th><th>undefined</th><th>541519-Other Computer Related Services</th><th>7J20-IT AND TELECOM - SECURITY AND COMPLIANCE PRODUCTS (HARDWARE AND PERPETUAL LICENSE SOFTWARE)</th><th>20341424F00016</th><th>Treasury</th><th>OFFICE OF ASST ADMIN INFO RES/CIO</th><th>> $10K to < or = $250K</th><th></th><th>Yes</th><th>Yes</th><th>Full and Open Competition with Set-aside After Exclusion of Sources (see FAR 6.2)</th><th>Current Contract Number</th><th>20341424F00016</th><th>FY 2025 Q3</th><th>NASA SEWP (NASA)</th><th>2025-04-01</th><th>2026-04-01</th><th></th><th>2024-10-08T00:02:22.000Z</th></tr><tr><th style=\"background-color:silver;\">ALCOHOL AND TOBACCO TAX AND TRADE BUREAU</th><th>Recompete</th><th>Market Research</th><th>TTB-OCIO-0001</th><th>GA, United States</th><th>Order (under IDC, BPA, BOA, FSS, or GWAC)</th><th>Cost-Reimbursable (see FAR 16.3)</th><th>undefined</th><th>undefined</th><th>541512-Computer Systems Design Services</th><th>DA01-IT AND TELECOM - BUSINESS APPLICATION/APPLICATION DEVELOPMENT SUPPORT SERVICES (LABOR)</th><th>20341420F00043</th><th>Treasury</th><th>OFFICE OF ASST ADMIN INFO RES/CIO</th><th>> $50M to < or = $100M</th><th></th><th>Yes</th><th>Yes</th><th>Full and Open Competition with Set-aside After Exclusion of Sources (see FAR 6.2)</th><th>Current Contract Number</th><th>20341420F00043</th><th>FY 2025 Q3</th><th>NIH Information Technology Acquisition and Assessment Center (NITAAC) (NIH)</th><th>2025-04-01</th><th>2030-04-01</th><th></th><th>2024-10-08T00:02:22.000Z</th></tr><tr><th style=\"background-color:silver;\">ALCOHOL AND TOBACCO TAX AND TRADE BUREAU</th><th>Recompete</th><th>Market Research</th><th>TTB-OCIO-0002</th><th>MD, United States</th><th>Order (under IDC, BPA, BOA, FSS, or GWAC)</th><th>Fixed-Price (see FAR 16.2)</th><th>undefined</th><th>undefined</th><th>541519-Other Computer Related Services</th><th>7J20-IT AND TELECOM - SECURITY AND COMPLIANCE PRODUCTS (HARDWARE AND PERPETUAL LICENSE SOFTWARE)</th><th>20341424F00013</th><th>Treasury</th><th>OFFICE OF ASST ADMIN INFO RES/CIO</th><th>> $250K to < or = $750K</th><th></th><th>Yes</th><th>Yes</th><th>Full and Open Competition with Set-aside After Exclusion of Sources (see FAR 6.2)</th><th>Current Contract Number</th><th>20341424F00013</th><th>FY 2025 Q2</th><th>NASA SEWP (NASA)</th><th>2025-02-01</th><th>2026-01-01</th><th></th><th>2024-10-08T00:09:15.000Z</th></tr><tr><th style=\"background-color:silver;\">BUREAU OF THE FISCAL SERVICE</th><th>Recompete</th><th>Market Research</th><th>ARC-501104-25-003/000001</th><th>WV, United States</th><th>Order (under IDC, BPA, BOA, FSS, or GWAC)</th><th>Fixed-Price (see FAR 16.2)</th><th>undefined</th><th>undefined</th><th>541519-Other Computer Related Services</th><th>7A21-IT AND TELECOM - BUSINESS APPLICATION SOFTWARE (PERPETUAL LICENSE SOFTWARE)</th><th>2033H624F00004</th><th>Treasury</th><th>ADMINISTRATIVE RESOURCE CENTER</th><th>> $10K to < or = $250K</th><th></th><th>Yes</th><th>Yes</th><th>Full and Open Competition with Set-aside After Exclusion of Sources (see FAR 6.2)</th><th>Current Contract Number</th><th>2033H624F00004</th><th>FY 2025 Q1</th><th>NASA SEWP (NASA)</th><th>2025-01-01</th><th>2025-12-01</th><th></th><th>2024-10-08T00:02:22.000Z</th></tr><tr><th style=\"background-color:silver;\">BUREAU OF THE FISCAL SERVICE</th><th>Recompete</th><th>Market Research</th><th>CIO-30130403-25-001</th><th>WV, United States</th><th>Definitive Contract</th><th>Fixed-Price (see FAR 16.2)</th><th>undefined</th><th>undefined</th><th>541519-Other Computer Related Services</th><th>7A21-IT AND TELECOM - BUSINESS APPLICATION SOFTWARE (PERPETUAL LICENSE SOFTWARE)</th><th>2033H620P00044</th><th>Treasury</th><th>ADMINISTRATIVE RESOURCE CENTER</th><th>> $250K to < or = $750K</th><th></th><th>No</th><th>Yes</th><th>Full and Open Competition Non-setaside (see FAR 6.1)</th><th>Current Contract Number</th><th>2033H620P00044</th><th>FY 2025 Q2</th><th>Standalone Contract</th><th>2025-01-01</th><th>2025-12-01</th><th></th><th>2024-10-08T00:02:22.000Z</th></tr><tr><th style=\"background-color:silver;\">BUREAU OF THE FISCAL SERVICE</th><th>Recompete</th><th>Market Research</th><th>TFS-RQTS2-24-0131 TFS-ACQWS3-24-0262</th><th>WV, United States</th><th>Order (under IDC, BPA, BOA, FSS, or GWAC)</th><th>Fixed-Price (see FAR 16.2)</th><th>undefined</th><th>undefined</th><th>541519-Other Computer Related Services</th><th>DH01-IT AND TELECOM - PLATFORM SUPPORT SERVICES: DATABASE, MAINFRAME, MIDDLEWARE (LABOR)</th><th>2033H622F00109</th><th>Treasury</th><th>ADMINISTRATIVE RESOURCE CENTER</th><th>> $5M to < or = $10M</th><th></th><th>Yes</th><th>Yes</th><th>Full and Open Competition with Set-aside After Exclusion of Sources (see FAR 6.2)</th><th>Current Contract Number</th><th>2033H622F00109</th><th>FY 2025 Q1</th><th>NASA SEWP (NASA)</th><th>2024-12-01</th><th>2025-11-01</th><th></th><th>2024-10-08T00:02:22.000Z</th></tr><tr><th style=\"background-color:silver;\">FINANCIAL CRIME ENFORCEMENT NETWORK</th><th>New</th><th>Market Research</th><th>FIN-25-0001</th><th>VA, United States</th><th>Order (under IDC, BPA, BOA, FSS, or GWAC)</th><th>Fixed-Price (see FAR 16.2)</th><th>undefined</th><th>undefined</th><th>541613-Marketing Consulting Services</th><th>R699-SUPPORT- ADMINISTRATIVE: OTHER</th><th>FIN0001</th><th>Treasury</th><th>GENREAL OPERATIONS</th><th>> $750K to < or = $2M</th><th></th><th>Yes</th><th>Yes</th><th>Full and Open Competition with Set-aside After Exclusion of Sources (see FAR 6.2)</th><th>Early Market Research</th><th>FIN-0001</th><th>FY 2025 Q2</th><th>OASIS (GSA)</th><th>2025-03-01</th><th>2026-03-01</th><th></th><th>2024-10-08T00:02:22.000Z</th></tr><tr><th style=\"background-color:silver;\">OFFICE OF THE INSPECTOR GENERAL FOR TAX ADMINISTRATION</th><th>New</th><th>Market Research</th><th>TGT-TBD-25-00001</th><th>MD, United States</th><th>Order (under IDC, BPA, BOA, FSS, or GWAC)</th><th>Fixed-Price (see FAR 16.2)</th><th>undefined</th><th>undefined</th><th>332993-Ammunition (except Small Arms) Manufacturing</th><th>1310-AMMUNITION, OVER 30MM UP TO 75MM</th><th>TGTTBD2500001</th><th>Treasury</th><th>GENERAL INVESTIGATIONS</th><th>> $10K to < or = $250K</th><th></th><th>No</th><th>No</th><th></th><th>Solicitation Number (RFP)</th><th>TGT-25-0001</th><th>FY 2025 Q1</th><th>Reduced Hazard Training Ammunition II (RHTA II) (DHS)</th><th>2024-12-31</th><th>2025-12-30</th><th></th><th>2024-10-08T00:02:22.000Z</th></tr><tr><th style=\"background-color:silver;\">OFFICE OF THE INSPECTOR GENERAL FOR TAX ADMINISTRATION</th><th>New</th><th>Market Research</th><th>TGT-TBD-25-00002</th><th>MD, United States</th><th>Purchase Order (PO)</th><th>Fixed-Price (see FAR 16.2)</th><th>undefined</th><th>undefined</th><th>518210-Computing Infrastructure Providers, Data Processing, Web Hosting, and Related Services</th><th>DH10-IT AND TELECOM - PLATFORM AS A SERVICE: DATABASE, MAINFRAME, MIDDLEWARE</th><th>TGTTBD2500002</th><th>Treasury</th><th>AUDIT</th><th>> $250K to < or = $750K</th><th></th><th>Yes</th><th>Yes</th><th>Full and Open Competition with Set-aside After Exclusion of Sources (see FAR 6.2)</th><th>RFI Number</th><th>RFI-117693-SB</th><th>FY 2025 Q4</th><th>NIH Information Technology Acquisition and Assessment Center (NITAAC) (NIH)</th><th>2025-09-30</th><th>2026-09-29</th><th></th><th>2024-10-08T00:02:22.000Z</th></tr><tr><th style=\"background-color:silver;\">OFFICE OF THE INSPECTOR GENERAL FOR TAX ADMINISTRATION</th><th>New</th><th>Market Research</th><th>TGT-TBD-25-00003</th><th>MD, United States</th><th>Purchase Order (PO)</th><th>Time-and-Materials (see FAR 16.601)</th><th>undefined</th><th>undefined</th><th>621511-Medical Laboratories</th><th>Q401-MEDICAL- NURSING</th><th>TGTTBD2500003</th><th>Treasury</th><th>GENERAL INVESTIGATIONS</th><th>> $250K to < or = $750K</th><th></th><th>Yes</th><th>Yes</th><th>Full and Open Competition Non-setaside (see FAR 6.1)</th><th>Sources Sought Notice (SSN)</th><th>TGT-25-0003</th><th>FY 2025 Q4</th><th>Standalone Contract</th><th>2025-09-30</th><th>2026-09-29</th><th></th><th>2024-10-08T00:02:22.000Z</th></tr></table>\n", "0 ['Bureau', 'Type of Requirement', 'Status', 'ShopCart/req', 'Place of Performance', 'Award Type', 'Contract Type', 'Bureau Point of Contact', 'Program Office Point of Contact', 'NAICS', 'PSC', 'Contract Number', 'Agency', 'Program Office', 'Estimated Total Contract Value', 'Type of Small Business Set-aside', 'Small Business Set-aside', 'Competition', 'Extent Competed', 'Listing Id Type', 'Specific Id', 'Projected Award FY_Qtr', 'Projected Contract Vehicle', 'Project Period of Performance Start', 'Project Period of Performance End', 'Project Period of Performance Close Date', 'Last Modified Date']\n", "0 ['<PERSON><PERSON><PERSON><PERSON> AND <PERSON><PERSON><PERSON><PERSON> TAX AND TRADE BUREAU', 'Recompete', 'Market Research', 'TTB-OCIO-0002', 'MD, United States', 'Order (under IDC, BPA, BOA, FSS, or GWAC)', 'Fixed-Price (see FAR 16.2)', 'undefined', 'undefined', '541519-Other Computer Related Services', '7J20-IT AND TELECOM - SECURITY AND COMPLIANCE PRODUCTS (HARDWARE AND PERPETUAL LICENSE SOFTWARE)', '20341424F00013', 'Treasury', 'OFFICE OF ASST ADMIN INFO RES/CIO', '> $250K to < or = $750K', '', 'Yes', 'Yes', 'Full and Open Competition with Set-aside After Exclusion of Sources (see FAR 6.2)', 'Current Contract Number', '20341424F00013', 'FY 2025 Q2', 'NASA SEWP (NASA)', '2025-02-01', '2026-01-01', '', '2024-10-08T00:02:22.000Z']\n", "0 ['<PERSON><PERSON><PERSON><PERSON> AND <PERSON><PERSON><PERSON><PERSON> TAX AND TRADE BUREAU', 'Recompete', 'Market Research', 'TTB-OCIO-0003', 'VA, United States', 'Order (under IDC, BPA, BOA, FSS, or GWAC)', 'Fixed-Price (see FAR 16.2)', 'undefined', 'undefined', '541519-Other Computer Related Services', '7J20-IT AND TELECOM - SECURITY AND COMPLIANCE PRODUCTS (HARDWARE AND PERPETUAL LICENSE SOFTWARE)', '20341424F00016', 'Treasury', 'OFFICE OF ASST ADMIN INFO RES/CIO', '> $10K to < or = $250K', '', 'Yes', 'Yes', 'Full and Open Competition with Set-aside After Exclusion of Sources (see FAR 6.2)', 'Current Contract Number', '20341424F00016', 'FY 2025 Q3', 'NASA SEWP (NASA)', '2025-04-01', '2026-04-01', '', '2024-10-08T00:02:22.000Z']\n", "0 ['<PERSON><PERSON><PERSON><PERSON> AND <PERSON><PERSON><PERSON><PERSON> TAX AND TRADE BUREAU', 'Recompete', 'Market Research', 'TTB-OCIO-0001', 'GA, United States', 'Order (under IDC, BPA, BOA, FSS, or GWAC)', 'Cost-Reimbursable (see FAR 16.3)', 'undefined', 'undefined', '541512-Computer Systems Design Services', 'DA01-IT AND TELECOM - BUSINESS APPLICATION/APPLICATION DEVELOPMENT SUPPORT SERVICES (LABOR)', '20341420F00043', 'Treasury', 'OFFICE OF ASST ADMIN INFO RES/CIO', '> $50M to < or = $100M', '', 'Yes', 'Yes', 'Full and Open Competition with Set-aside After Exclusion of Sources (see FAR 6.2)', 'Current Contract Number', '20341420F00043', 'FY 2025 Q3', 'NIH Information Technology Acquisition and Assessment Center (NITAAC) (NIH)', '2025-04-01', '2030-04-01', '', '2024-10-08T00:02:22.000Z']\n", "0 ['<PERSON><PERSON><PERSON><PERSON> AND <PERSON><PERSON><PERSON><PERSON> TAX AND TRADE BUREAU', 'Recompete', 'Market Research', 'TTB-OCIO-0002', 'MD, United States', 'Order (under IDC, BPA, BOA, FSS, or GWAC)', 'Fixed-Price (see FAR 16.2)', 'undefined', 'undefined', '541519-Other Computer Related Services', '7J20-IT AND TELECOM - SECURITY AND COMPLIANCE PRODUCTS (HARDWARE AND PERPETUAL LICENSE SOFTWARE)', '20341424F00013', 'Treasury', 'OFFICE OF ASST ADMIN INFO RES/CIO', '> $250K to < or = $750K', '', 'Yes', 'Yes', 'Full and Open Competition with Set-aside After Exclusion of Sources (see FAR 6.2)', 'Current Contract Number', '20341424F00013', 'FY 2025 Q2', 'NASA SEWP (NASA)', '2025-02-01', '2026-01-01', '', '2024-10-08T00:09:15.000Z']\n", "0 ['BUREAU OF THE FISCAL SERVICE', 'Recompete', 'Market Research', 'ARC-501104-25-003/000001', 'WV, United States', 'Order (under IDC, BPA, BOA, FSS, or GWAC)', 'Fixed-Price (see FAR 16.2)', 'undefined', 'undefined', '541519-Other Computer Related Services', '7A21-IT AND TELECOM - BUSINESS APPLICATION SOFTWARE (PERPETUAL LICENSE SOFTWARE)', '2033H624F00004', 'Treasury', 'ADMINISTRATIVE RESOURCE CENTER', '> $10K to < or = $250K', '', 'Yes', 'Yes', 'Full and Open Competition with Set-aside After Exclusion of Sources (see FAR 6.2)', 'Current Contract Number', '2033H624F00004', 'FY 2025 Q1', 'NASA SEWP (NASA)', '2025-01-01', '2025-12-01', '', '2024-10-08T00:02:22.000Z']\n", "0 ['BUREAU OF THE FISCAL SERVICE', 'Recompete', 'Market Research', 'CIO-30130403-25-001', 'WV, United States', 'Definitive Contract', 'Fixed-Price (see FAR 16.2)', 'undefined', 'undefined', '541519-Other Computer Related Services', '7A21-IT AND TELECOM - BUSINESS APPLICATION SOFTWARE (PERPETUAL LICENSE SOFTWARE)', '2033H620P00044', 'Treasury', 'ADMINISTRATIVE RESOURCE CENTER', '> $250K to < or = $750K', '', 'No', 'Yes', 'Full and Open Competition Non-setaside (see FAR 6.1)', 'Current Contract Number', '2033H620P00044', 'FY 2025 Q2', 'Standalone Contract', '2025-01-01', '2025-12-01', '', '2024-10-08T00:02:22.000Z']\n", "0 ['BUREAU OF THE FISCAL SERVICE', 'Recompete', 'Market Research', 'TFS-RQTS2-24-0131 TFS-ACQWS3-24-0262', 'WV, United States', 'Order (under IDC, BPA, BOA, FSS, or GWAC)', 'Fixed-Price (see FAR 16.2)', 'undefined', 'undefined', '541519-Other Computer Related Services', 'DH01-IT AND TELECOM - PLATFORM SUPPORT SERVICES: DATABASE, MAINFRAME, MIDDLEWARE (LABOR)', '2033H622F00109', 'Treasury', 'ADMINISTRATIVE RESOURCE CENTER', '> $5M to < or = $10M', '', 'Yes', 'Yes', 'Full and Open Competition with Set-aside After Exclusion of Sources (see FAR 6.2)', 'Current Contract Number', '2033H622F00109', 'FY 2025 Q1', 'NASA SEWP (NASA)', '2024-12-01', '2025-11-01', '', '2024-10-08T00:02:22.000Z']\n", "0 ['FINANCIAL CRIME ENFORCEMENT NETWORK', 'New', 'Market Research', 'FIN-25-0001', 'VA, United States', 'Order (under IDC, BPA, BOA, FSS, or GWAC)', 'Fixed-Price (see FAR 16.2)', 'undefined', 'undefined', '541613-Marketing Consulting Services', 'R699-SUPPORT- ADMINISTRATIVE: OTHER', 'FIN0001', 'Treasury', 'GENREAL OPERATIONS', '> $750K to < or = $2M', '', 'Yes', 'Yes', 'Full and Open Competition with Set-aside After Exclusion of Sources (see FAR 6.2)', 'Early Market Research', 'FIN-0001', 'FY 2025 Q2', 'OASIS (GSA)', '2025-03-01', '2026-03-01', '', '2024-10-08T00:02:22.000Z']\n", "0 ['OFFICE OF THE INSPECTOR GENERAL FOR TAX ADMINISTRATION', 'New', 'Market Research', 'TGT-TBD-25-00001', 'MD, United States', 'Order (under IDC, BPA, BOA, FSS, or GWAC)', 'Fixed-Price (see FAR 16.2)', 'undefined', 'undefined', '332993-Ammunition (except Small Arms) Manufacturing', '1310-AMMUNITION, OVER 30MM UP TO 75MM', 'TGTTBD2500001', 'Treasury', 'GENERAL INVESTIGATIONS', '> $10K to < or = $250K', '', 'No', 'No', '', 'Solicitation Number (RFP)', 'TGT-25-0001', 'FY 2025 Q1', 'Reduced Hazard Training Ammunition II (RHTA II) (DHS)', '2024-12-31', '2025-12-30', '', '2024-10-08T00:02:22.000Z']\n", "0 ['OFFICE OF THE INSPECTOR GENERAL FOR TAX ADMINISTRATION', 'New', 'Market Research', 'TGT-TBD-25-00002', 'MD, United States', 'Purchase Order (PO)', 'Fixed-Price (see FAR 16.2)', 'undefined', 'undefined', '518210-Computing Infrastructure Providers, Data Processing, Web Hosting, and Related Services', 'DH10-IT AND TELECOM - PLATFORM AS A SERVICE: DATABASE, MAINFRAME, MIDDLEWARE', 'TGTTBD2500002', 'Treasury', 'AUDIT', '> $250K to < or = $750K', '', 'Yes', 'Yes', 'Full and Open Competition with Set-aside After Exclusion of Sources (see FAR 6.2)', 'RFI Number', 'RFI-117693-SB', 'FY 2025 Q4', 'NIH Information Technology Acquisition and Assessment Center (NITAAC) (NIH)', '2025-09-30', '2026-09-29', '', '2024-10-08T00:02:22.000Z']\n", "0 ['OFFICE OF THE INSPECTOR GENERAL FOR TAX ADMINISTRATION', 'New', 'Market Research', 'TGT-TBD-25-00003', 'MD, United States', 'Purchase Order (PO)', 'Time-and-Materials (see FAR 16.601)', 'undefined', 'undefined', '621511-Medical Laboratories', 'Q401-MEDICAL- NURSING', 'TGTTBD2500003', 'Treasury', 'GENERAL INVESTIGATIONS', '> $250K to < or = $750K', '', 'Yes', 'Yes', 'Full and Open Competition Non-setaside (see FAR 6.1)', 'Sources Sought Notice (SSN)', 'TGT-25-0003', 'FY 2025 Q4', 'Standalone Contract', '2025-09-30', '2026-09-29', '', '2024-10-08T00:02:22.000Z']\n", "Data extracted and saved to 'extracted_data.csv' successfully.\n", "\n", " total lines: 1\n"]}], "source": ["compare_instance = Compare(\n", "          old_filename=\"treasury_changes.csv\", \n", "          new_filename=\"Opportunity Data.xls\",\n", "          key=\"Specific Id\", \n", "          download_path=os.path.join(os.getenv(\"DOWNLOAD_PATH\"), \"treasury\")\n", "          )\n", "compare_instance.convert_xls_to_csv()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 2}