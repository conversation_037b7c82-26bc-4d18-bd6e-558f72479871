# GSA Contractor Scraper

This Python script scrapes contractor information from the GSA eLibrary website using Selenium.

## Prerequisites

- Python 3.7 or higher
- Chrome browser installed
- ChromeDriver (will be automatically installed by webdriver-manager)
- Chrome for Testing https://googlechromelabs.github.io/chrome-for-testing/#stable V137 Linux https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.68/linux64/chrome-linux64.zip

## Installation

1. Clone this repository
2. Install the required packages:

```bash
pip install -r requirements.txt
```

## Usage

Run the script:

```bash
python gsa_scraper.py
```

### Debug Mode

To run the script in debug mode:

1. Start the script with debugpy:

```bash
python3 -m debugpy --listen 5678 --wait-for-client gsa_scraper.py
```

2. Configure your IDE's launch configuration to attach to the debugger:

```json
{
  "name": "Attach to GSALibrary Python Port",
  "type": "debugpy",
  "request": "attach",
  "connect": {
    "host": "localhost",
    "port": 5678
  }
}
```

### Automated Scheduling

#### Option 1: Using the Built-in Python Scheduler (Recommended)

The project includes a Python-based scheduler that runs the scraper every Sunday. This is more reliable and easier to manage than cron jobs.

**Start the scheduler:**
```bash
# Run scheduler with default time (9:00 AM every Sunday)
python run_scheduler.py

# Or specify a custom time
python run_scheduler.py --time "14:30"

# Run the scraper immediately for testing
python run_scheduler.py --run-now
```

**Run directly from the scheduler module:**
```bash
# Default time (9:00 AM every Sunday)
python -m scheduler.gsa_schedule

# Custom time
python -m scheduler.gsa_schedule --time "06:00"

# Run immediately
python -m scheduler.gsa_schedule --run-now
```

The scheduler will:
- Run continuously and execute the scraper every Sunday at the specified time
- Log all activities to `gsa_scheduler.log`
- Handle errors gracefully and continue running
- Can be stopped with Ctrl+C

#### Option 2: Using Cron (Alternative)

To run the scraper automatically every Sunday at 1 AM using cron:

```bash
# Edit crontab
crontab -e

# Add this line
0 1 * * 0 cd /path/to/GSAEPythonLibrary && /usr/bin/python3 gsa_scraper.py >> /path/to/GSAEPythonLibrary/cron.log 2>&1
```

## Features

- Handles session management and cookies
- Implements retry logic for failed requests
- Saves data to the kontratar_global table
- Comprehensive error handling and logging

## Output

The script saves each contractor detail to three tables in the `kontratar_global` schema:

1. `gsa_contractors` - Stores basic contractor information like name, address, contact details, and contract dates
2. `gsa_contract_details` - Stores contract-specific information including source, title, contract numbers, and dates
3. `gsa_contractor_json_data` - Stores the complete contractor data in JSON format for reference

The data is saved using UPSERT operations to handle both new entries and updates to existing records.

## Notes

- The script runs in headless mode by default
- It includes appropriate delays to avoid overwhelming the server
- It maintains proper session management to avoid access issues
