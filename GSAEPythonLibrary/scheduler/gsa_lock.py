#!/usr/bin/env python3
"""
GSA Scraper Lock Service
Database-backed distributed locking mechanism to prevent multiple instances 
of the GSA scraper from running simultaneously.
Uses psycopg2 for direct PostgreSQL connectivity.
"""

from datetime import datetime
import logging
import os
import psycopg2
from psycopg2.extras import DictCursor
from typing import Optional, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

class GSALockService:
    """
    Lock service specifically for GSA scraper operations.
    Prevents multiple instances from running concurrently.
    Uses psycopg2 for direct database connectivity.
    Matches kontratar_main.lock table schema.
    """
    
    LOCK_ID = "GSA_SCRAPER_LOCK"
    LOCK_TYPE = "GSA_SCRAPER"
    TIMEOUT_SECONDS = 3600  # 1 hour timeout for stuck locks
    TABLE_NAME = "kontratar_main.lock"  # Use existing table
    
    def __init__(self, connection_params: Optional[Dict[str, Any]] = None):
        """
        Initialize the GSA lock service
        
        Args:
            connection_params (dict, optional): Database connection parameters. If None, uses environment variables.
        """
        self.conn: Optional[psycopg2.extensions.connection] = None
        self.tenant_id = None        
        # Setup database connection using the same method as GSA scraper
        try:
            self.connect()
        except Exception as e:
            logger.error(f"Failed to initialize GSA lock service: {e}")
            raise
    
    def connect(self):
        """Establish database connection (duplicated from GSA scraper)"""
        try:
            self.conn = psycopg2.connect(
                dbname="postgres",
                user="alpha",
                password="5t3r2i66123",
                host="govcon-1.cwjbmcfdk9qo.us-east-1.rds.amazonaws.com",
                port="5432",
                sslmode="verify-ca",
                sslrootcert="us-east-1-bundle.pem"
            )
            logger.info("GSA lock service successfully connected to the database")
        except Exception as e:
            logger.error(f"Error connecting to database in GSA lock service: {str(e)}")
            raise
    
    def try_acquire_lock(self):
        """
        Attempt to acquire the GSA scraper lock
        
        Returns:
            bool: True if lock was acquired, False otherwise
        """
        if not self.conn:
            logger.error("Database connection not established")
            return False
            
        try:
            with self.conn.cursor(cursor_factory=DictCursor) as cur:
                current_time = datetime.utcnow()
                
                # Check if lock exists
                cur.execute(f"SELECT * FROM {self.TABLE_NAME} WHERE lock_id = %s", (self.LOCK_ID,))
                lock = cur.fetchone()
                
                if not lock:
                    # No lock exists, create and acquire it
                    cur.execute(f"""
                        INSERT INTO {self.TABLE_NAME} (lock_id, is_processing, lock_acquired_at, type, tenant_id)
                        VALUES (%s, %s, %s, %s, %s)
                    """, (self.LOCK_ID, True, current_time, self.LOCK_TYPE, self.tenant_id))
                    self.conn.commit()
                    logger.info(f"GSA lock acquired for the first time by {self.tenant_id}")
                    return True
                
                 # Check if lock is currently held
                if lock['is_processing']:
                    # Lock is already being processed, cannot acquire
                    logger.info(f"Lock {self.LOCK_ID} is already being processed by {lock.get('tenant_id', 'unknown')}, cannot acquire")
                    return False
                # Lock exists but not currently processing, acquire it
                cur.execute(f"""
                    UPDATE {self.TABLE_NAME} 
                    SET is_processing = %s, lock_acquired_at = %s, tenant_id = %s
                    WHERE lock_id = %s
                """, (True, current_time, self.tenant_id, self.LOCK_ID))
                self.conn.commit()
                logger.info(f"GSA lock acquired by {self.tenant_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error trying to acquire GSA lock: {e}")
            if self.conn:
                self.conn.rollback()
            return False
    
    def release_lock(self):
        """Release the GSA scraper lock"""
        if not self.conn:
            logger.error("Database connection not established")
            return
            
        try:
            with self.conn.cursor(cursor_factory=DictCursor) as cur:
                # Get current lock info for logging
                cur.execute(f"SELECT tenant_id FROM {self.TABLE_NAME} WHERE lock_id = %s", (self.LOCK_ID,))
                lock = cur.fetchone()
                
                # Update lock to released state
                cur.execute(f"""
                    UPDATE {self.TABLE_NAME} 
                    SET is_processing = %s, lock_acquired_at = %s 
                    WHERE lock_id = %s
                """, (False, datetime.utcnow(), self.LOCK_ID))
                
                if cur.rowcount > 0:
                    self.conn.commit()
                    if lock:
                        logger.info(f"GSA lock released by {lock['tenant_id']}")
                    else:
                        logger.info("GSA lock released")
                else:
                    logger.warning("Attempted to release GSA lock but no lock found")
                    
        except Exception as e:
            logger.error(f"Error releasing GSA lock: {e}")
            if self.conn:
                self.conn.rollback()
    
    def is_currently_processing(self):
        """
        Check if GSA scraper is currently processing
        
        Returns:
            bool: True if currently processing, False otherwise
        """
        if not self.conn:
            logger.error("Database connection not established")
            return False
            
        try:
            with self.conn.cursor(cursor_factory=DictCursor) as cur:
                cur.execute(f"SELECT * FROM {self.TABLE_NAME} WHERE lock_id = %s", (self.LOCK_ID,))
                lock = cur.fetchone()
                
                if not lock:
                    return False
                
                # Check for stale locks
                if lock['is_processing'] and lock['lock_acquired_at']:
                    duration = datetime.utcnow() - lock['lock_acquired_at']
                    if duration.total_seconds() > self.TIMEOUT_SECONDS:
                        logger.warning(f"Detected stale processing lock (held for {duration.total_seconds()} seconds)")
                        return False
                
                return lock['is_processing'] if lock else False
                
        except Exception as e:
            logger.error(f"Error checking processing status: {e}")
            return False
    
    def get_lock_info(self):
        """
        Get information about the current lock
        
        Returns:
            dict: Lock information or None if no lock exists
        """
        if not self.conn:
            logger.error("Database connection not established")
            return None
            
        try:
            with self.conn.cursor(cursor_factory=DictCursor) as cur:
                cur.execute(f"SELECT * FROM {self.TABLE_NAME} WHERE lock_id = %s", (self.LOCK_ID,))
                lock = cur.fetchone()
                
                if not lock:
                    return None
                
                duration = None
                if lock['lock_acquired_at']:
                    duration = datetime.utcnow() - lock['lock_acquired_at']
                
                return {
                    'id': lock['id'],
                    'lock_id': lock['lock_id'],
                    'is_processing': lock['is_processing'],
                    'lock_acquired_at': lock['lock_acquired_at'],
                    'next_scheduled_run': lock.get('next_scheduled_run'),
                    'type': lock['type'],
                    'tenant_id': lock['tenant_id'],
                    'duration_seconds': duration.total_seconds() if duration else None
                }
                
        except Exception as e:
            logger.error(f"Error getting lock info: {e}")
            return None
    
    def force_release_lock(self):
        """
        Force release the lock (for emergency situations)
        """
        if not self.conn:
            logger.error("Database connection not established")
            return
            
        try:
            with self.conn.cursor(cursor_factory=DictCursor) as cur:
                # Get current lock info for logging
                cur.execute(f"SELECT tenant_id FROM {self.TABLE_NAME} WHERE lock_id = %s", (self.LOCK_ID,))
                lock = cur.fetchone()
                
                # Force release
                cur.execute(f"""
                    UPDATE {self.TABLE_NAME} 
                    SET is_processing = %s, lock_acquired_at = %s 
                    WHERE lock_id = %s
                """, (False, datetime.utcnow(), self.LOCK_ID))
                
                if cur.rowcount > 0:
                    self.conn.commit()
                    if lock:
                        old_info = lock['tenant_id']
                        logger.warning(f"GSA lock forcibly released from {old_info}")
                    else:
                        logger.warning("GSA lock forcibly released")
                else:
                    logger.info("No GSA lock found to force release")
                    
        except Exception as e:
            logger.error(f"Error force releasing GSA lock: {e}")
            if self.conn:
                self.conn.rollback()
    
    def set_next_scheduled_run(self, next_run_time: datetime):
        """
        Set the next scheduled run time for the lock
        
        Args:
            next_run_time (datetime): When the next run is scheduled
        """
        if not self.conn:
            logger.error("Database connection not established")
            return
            
        try:
            with self.conn.cursor() as cur:
                cur.execute(f"""
                    UPDATE {self.TABLE_NAME} 
                    SET next_scheduled_run = %s 
                    WHERE lock_id = %s
                """, (next_run_time, self.LOCK_ID))
                
                if cur.rowcount > 0:
                    self.conn.commit()
                    logger.info(f"Next scheduled run set to {next_run_time}")
                else:
                    logger.warning("Failed to set next scheduled run - lock not found")
                    
        except Exception as e:
            logger.error(f"Error setting next scheduled run: {e}")
            if self.conn:
                self.conn.rollback()
    
    def close(self):
        """Close the database connection"""
        if self.conn:
            self.conn.close()
            logger.debug("GSA lock service database connection closed")

# Context manager for easier usage
