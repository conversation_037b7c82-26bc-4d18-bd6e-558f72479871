#!/usr/bin/env python3
"""
GSA Scraper Scheduler
This module schedules the GSA scraper to run every Sunday using the schedule library, with proper logging and error handling.
"""

import sys
import os
import logging
import time
import schedule
from datetime import datetime
from .gsa_lock import GSALockService   
# Add the parent directory to the Python path to import gsa_scraper
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


try:
    from gsa_scraper import main as run_gsa_scraper
  
except ImportError as e:
    logging.error(f"Failed to import gsa_scraper: {e}")
    sys.exit(1)

# Configure logging
log_filename = 'gsa_scheduler.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def run_scraper_job():
    """
    Wrapper function to run the GSA scraper with error handling and logging.
    """
    try:
        logger.info("Starting scheduled GSA scraper job...")
        start_time = datetime.now()
        
        # Run the GSA scraper
        run_gsa_scraper()
        
        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(f"GSA scraper job completed successfully in {duration}")
        
    except Exception as e:
        logger.error(f"Error running GSA scraper job: {str(e)}")
        raise

def schedule_gsa_scraper(run_time="09:00"):
    """
    Schedule the GSA scraper to run every Sunday at the specified time.
    
    Args:
        run_time (str): Time to run the scraper in HH:MM format (default: "09:00")
    """
    try:
        # Schedule the job to run every Sunday at the specified time
        schedule.every().sunday.at(run_time).do(run_scraper_job)
        
        logger.info(f"GSA scraper scheduled to run every Sunday at {run_time}")
        logger.info("Scheduler is now running. Press Ctrl+C to stop.")
        
        # Keep the scheduler running
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
            
    except KeyboardInterrupt:
        logger.info("Scheduler stopped by user")
    except Exception as e:
        logger.error(f"Error in scheduler: {str(e)}")
        raise

def run_now():
    """
    Run the scraper immediately (for testing purposes).
    """
    logger.info("Running GSA scraper immediately...")
    run_scraper_job()

def main():
    """
    Main function to start the scheduler.
    """
    import argparse
    gsa_lock = GSALockService()
    lock_service = gsa_lock.try_acquire_lock()
    parser = argparse.ArgumentParser(description='GSA Scraper Scheduler')
    parser.add_argument('--time', '-t', default='09:00', 
                       help='Time to run the scraper (HH:MM format, default: 09:00)')
    parser.add_argument('--run-now', '-n', action='store_true', 
                       help='Run the scraper immediately instead of scheduling')
    
    args = parser.parse_args()
    logger.info(f"Lock service: {lock_service}")
    
    if lock_service is not True:
        logger.error("Failed to acquire lock. Exiting...")
        return  # Exit without releasing lock since we never acquired it
   
    try:
        if args.run_now:
            run_now()
        else:
            logger.info("Lock acquired. Scheduling GSA scraper...")
            schedule_gsa_scraper()
    except Exception as e:
        logger.error(f"Error during execution: {e}")
    finally:
        # Only release if we successfully acquired the lock
        gsa_lock.release_lock()
        logger.info("Lock released")

if __name__ == "__main__":
    main()
