#!/bin/bash

# Set the working directory
cd "$(dirname "$0")"

# Load environment variables
source .env

# Set up logging
LOG_FILE="GSAEPythonLibrary.logs"
TIMESTAMP=$(date +"%Y-%m-%d %H:%M:%S")

pip install -r requirements.txt

# Default run mode
RUN_MODE="scheduled"
CUSTOM_TIME=""

# Parse command-line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --time) CUSTOM_TIME="$2"; shift ;;
        --run-now) RUN_MODE="immediate" ;;
        *) echo "Unknown parameter passed: $1"; exit 1 ;;
    esac
    shift
done

# Run the scheduler based on mode
if [ "$RUN_MODE" == "immediate" ]; then
    # Run immediately for testing
    nohup python3 run_scheduler.py --run-now &
    echo "$TIMESTAMP - GSA scraper started in immediate mode" >> "$LOG_FILE"
elif [ -n "$CUSTOM_TIME" ]; then
    # Run with custom time
    nohup python3 run_scheduler.py --time "$CUSTOM_TIME" &
    echo "$TIMESTAMP - GSA scraper started with custom time: $CUSTOM_TIME" >> "$LOG_FILE"
else
    # Start scheduler with default time (9:00 AM every Sunday)
    nohup python3 run_scheduler.py &
    echo "$TIMESTAMP - GSA scraper started in scheduled mode" >> "$LOG_FILE"
fi

# Get the process ID
PID=$!

# Log start of execution
echo "$TIMESTAMP - Starting GSA scraper" >> "$LOG_FILE"

# Log the process ID
echo "$TIMESTAMP - GSA scraper started with PID: $PID" >> "$LOG_FILE"

# Log completion
TIMESTAMP=$(date +"%Y-%m-%d %H:%M:%S")
echo "$TIMESTAMP - GSA scraper process started in background" >> "$LOG_FILE"

# Examples of usage:
# ./start.sh                     # Run in default scheduled mode
# ./start.sh --run-now           # Run immediately
# ./start.sh --time "14:30"      # Run at a custom time