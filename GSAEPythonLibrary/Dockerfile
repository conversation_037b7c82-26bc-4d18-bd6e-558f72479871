# ---- Builder Stage ----
FROM python:3.11-slim AS builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_DEFAULT_TIMEOUT=100 \
    DEBIAN_FRONTEND=noninteractive

# Install build dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    pkg-config \
    libpq-dev \
    wget \
    gnupg \
    unzip && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* \
    && apt-get purge -y --auto-remove

# Create virtual user for build
RUN useradd -m builder

ENV HOME=/home/<USER>

# Install Python dependencies
WORKDIR /build
COPY requirements.txt .
RUN pip install --user -r requirements.txt

# ---- Runtime Stage ----
FROM python:3.11-slim

# Build-time arguments (can be overridden at build time)
ARG APP_USER=kontratar
ARG APP_UID=1000
ARG APP_GID=1000
ARG RUN_MODE=scheduled
ARG CUSTOM_TIME=09:00

# Set environment variables (can be overridden at runtime)
ENV DEBIAN_FRONTEND=noninteractive \
    APP_USER=${APP_USER} \
    APP_UID=${APP_UID} \
    APP_GID=${APP_GID} \
    RUN_MODE=${RUN_MODE} \
    CUSTOM_TIME=${CUSTOM_TIME} \
    PATH="/home/<USER>/.local/bin:$PATH" \
    PYTHONPATH="/app"

# Install runtime dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    wget \
    gnupg \
    unzip \
    chromium \
    chromium-driver && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* \
    && apt-get purge -y --auto-remove

# Create application user structure using build args
RUN groupadd --gid ${APP_GID} ${APP_USER} && \
    useradd --uid ${APP_UID} --gid ${APP_USER} --shell /bin/bash --create-home ${APP_USER} && \
    mkdir -p /app /logs && \
    chown -R ${APP_USER}:${APP_USER} /app /logs /home/<USER>

# Copy installed packages from builder
COPY --from=builder --chown=${APP_USER}:${APP_USER} /home/<USER>/.local /home/<USER>/.local

# Set working directory
WORKDIR /app

# Copy application code
COPY --chown=${APP_USER}:${APP_USER} . .

# Configure logging
RUN ln -sf /dev/stdout /logs/GSAEPythonLibrary.logs && \
    ln -sf /dev/stdout /logs/gsa_scheduler.log && \
    chown ${APP_USER}:${APP_USER} /logs/GSAEPythonLibrary.logs /logs/gsa_scheduler.log

# Switch to non-root user
USER ${APP_USER}

# Health check to ensure the service is running
HEALTHCHECK --interval=60s --timeout=10s --start-period=30s --retries=3 \
    CMD ps aux | grep -v grep | grep "run_scheduler.py" || exit 1

# Use exec form to ensure proper signal handling
# The scheduler will run based on the RUN_MODE environment variable
CMD ["python3", "run_scheduler.py"] 