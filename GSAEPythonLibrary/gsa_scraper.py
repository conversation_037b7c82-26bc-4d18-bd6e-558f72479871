import logging
import time
from datetime import datetime
from typing import List, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from dataclasses import dataclass
from bs4 import BeautifulSoup
import json
import psycopg2
from psycopg2.extras import DictCursor
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
log_filename = 'GSAEPythonLibrary.logs'
# Remove existing log file if it exists
if os.path.exists(log_filename):
    os.remove(log_filename)

# Configure logging to write to file
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()  # This will keep console output as well
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ContractDetails:
    contract_number: str
    contract_title: str  = None
    contract_award_date: str = None
    contract_expiration_date: str = None
    contractor: 'Contractor' = None

@dataclass
class Contractor:
    contract_number: str
    name: str = None
    address: str = None
    phone: str = None
    email: str = None
    website: str = None
    sam_uei: str = None
    naics: str = None
    socio_economic: str = None
    current_option_period_end_date: datetime = None
    ultimate_contract_end_date: datetime = None
    govt_poc_name: str = None
    govt_poc_phone: str = None
    govt_poc_email: str = None
    epls: str = None
    contract_details: List[ContractDetails] = None

    def __post_init__(self):
        if self.contract_details is None:
            self.contract_details = []

class DatabaseConnection:
    def __init__(self):
        self.conn = None
        self.connect()

    def connect(self):
        """Establish database connection"""
        try:
            self.conn = psycopg2.connect(
                dbname="postgres",
                user="alpha",
                password="5t3r2i66123",
                host="govcon-1.cwjbmcfdk9qo.us-east-1.rds.amazonaws.com",
                port="5432",
                sslmode="verify-ca",
                sslrootcert="us-east-1-bundle.pem"
            )
            logger.info("Successfully connected to the database")
        except Exception as e:
            logger.error(f"Error connecting to database: {str(e)}")
            raise

    def save_contractor(self, contractor: 'Contractor'):
        """Save contractor data to database"""
        try:
            with self.conn.cursor() as cur:
                # Insert contractor                
                cur.execute("""
                    INSERT INTO kontratar_global.gsa_contractors (
                        contract_number, name, address, phone, email, website,
                        sam_uei, naics, socio_economic, current_option_period_end_date,
                        ultimate_contract_end_date, govt_poc_name, govt_poc_phone,
                        govt_poc_email, epls
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    ) ON CONFLICT (contract_number) DO UPDATE SET
                        name = EXCLUDED.name,
                        address = EXCLUDED.address,
                        phone = EXCLUDED.phone,
                        email = EXCLUDED.email,
                        website = EXCLUDED.website,
                        sam_uei = EXCLUDED.sam_uei,
                        naics = EXCLUDED.naics,
                        socio_economic = EXCLUDED.socio_economic,
                        current_option_period_end_date = EXCLUDED.current_option_period_end_date,
                        ultimate_contract_end_date = EXCLUDED.ultimate_contract_end_date,
                        govt_poc_name = EXCLUDED.govt_poc_name,
                        govt_poc_phone = EXCLUDED.govt_poc_phone,
                        govt_poc_email = EXCLUDED.govt_poc_email,
                        epls = EXCLUDED.epls
                    RETURNING id
                """, (
                    contractor.contract_number,
                    contractor.name,
                    contractor.address,
                    contractor.phone,
                    contractor.email,
                    contractor.website,
                    contractor.sam_uei,
                    contractor.naics,
                    contractor.socio_economic,
                    self.convert_empty_to_none(contractor.current_option_period_end_date),
                    self.convert_empty_to_none(contractor.ultimate_contract_end_date),
                    contractor.govt_poc_name,
                    contractor.govt_poc_phone,
                    contractor.govt_poc_email,
                    contractor.epls
                ))
                
                contractor_id = cur.fetchone()[0]
                
                # Insert into contract_json_data
                cur.execute("""
                    INSERT INTO kontratar_global.gsa_contractor_json_data
                    (id, contract_number, sam_uei, raw_json, created_at, updated_at)
                    VALUES (%s, %s, %s, %s::jsonb, NOW(), NOW())
                    ON CONFLICT (id) DO UPDATE SET
                        sam_uei = EXCLUDED.sam_uei,
                        raw_json = EXCLUDED.raw_json,
                        updated_at = NOW()
                """, (
                    contractor_id,
                    contractor.contract_number,
                    contractor.sam_uei,
                    json.dumps(vars(contractor), default=str)
                ))

                # Insert contract details
                for detail in contractor.contract_details:
                    cur.execute("""
                        INSERT INTO kontratar_global.gsa_contract_details (
                            contractor_id, source, title, contract_number,
                            terms_and_conditions_url, current_option_period_end_date,
                            ultimate_contract_end_date, category
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s
                        ) ON CONFLICT (category, contract_number, contractor_id) DO UPDATE SET
                            source = EXCLUDED.source,
                            title = EXCLUDED.title,
                            terms_and_conditions_url = EXCLUDED.terms_and_conditions_url,
                            current_option_period_end_date = EXCLUDED.current_option_period_end_date,
                            ultimate_contract_end_date = EXCLUDED.ultimate_contract_end_date,
                            category = EXCLUDED.category,
                            updated_at = NOW()
                    """, (
                        contractor_id,
                        detail.contract_title,  # Using contract_title as source
                        detail.contract_title,  # Using contract_title as title
                        detail.contract_number,
                        None,  # terms_and_conditions_url
                        self.convert_empty_to_none(detail.contract_award_date),  # Only convert date field
                        self.convert_empty_to_none(detail.contract_expiration_date),  # Only convert date field
                        None  # category
                    ))

                self.conn.commit()
                logger.info(f"Successfully saved contractor {contractor.contract_number} to database")

        except Exception as e:
            self.conn.rollback()
            logger.error(f"Error saving contractor to database: {str(e)}")
            raise

    def close(self):
        """Close database connection"""
        if self.conn:
            self.conn.close()
            logger.info("Database connection closed")

    def convert_empty_to_none(self, value):
        """Convert empty string to None, otherwise return the value"""
        return None if value == '' else value

class GSAScraper:
    BASE_URL = "https://www.gsaelibrary.gsa.gov/ElibMain"
    CONTRACTOR_LIST_URL = f"{BASE_URL}/contractorList.do"
    CONTRACTOR_DETAILS_URL = f"{BASE_URL}/contractorInfo.do"
    TIMEOUT = 30

    def __init__(self):
        self.driver = None
        self.setup_driver()

    def setup_driver(self):
        """Setup Selenium WebDriver with appropriate options"""
        chrome_browser = os.getenv("CHROME_BROWSER")
        if not chrome_browser:
            raise EnvironmentError("CHROME_BROWSER environment variable is not set. Please add the path to the Chrome Testing app in the .env file.")
        
        print(f"Chrome browser: {chrome_browser}")
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-data-dir=/tmp/chrome-testing-{}'.format(int(time.time())))
        chrome_options.binary_location = chrome_browser

        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.implicitly_wait(10)

    def scrape_all_contractors(self):
        """
        Scrape contractors for all letters of the alphabet
        Returns:
            List[Contractor]: List of all contractors scraped

        """
        all_contractors = []
        alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"

        for letter in alphabet:
            try:
                contractors = self.scrape_contractors_for_letter(letter)
                all_contractors.extend(contractors)
                logger.info(f"Successfully scraped {len(contractors)} contractors for letter {letter}")
            except Exception as e:
                logger.error(f"Error scraping contractors for letter {letter}: {str(e)}")
                continue

        return all_contractors

    def scrape_contractors_for_letter(self, letter: str):
        """
        Scrape contractors for a specific letter
        Returns:
            List[Contractor]: List of all contractors scraped
            """
        url = f"{self.CONTRACTOR_LIST_URL}?contractorListFor={letter}"
        logger.info(f"Attempting to fetch contractors for letter {letter} from URL: {url}")
        contractors = []
        db = None

        try:
            db = DatabaseConnection()
            self.driver.get(url)
            WebDriverWait(self.driver, self.TIMEOUT).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "a[href*='contractorInfo.do']"))
            )

            contractor_links = self.driver.find_elements(By.CSS_SELECTOR, "a[href*='contractorInfo.do']")
            contractor_hrefs = []
            for link in contractor_links:
                try:
                    href = link.get_attribute('href')
                    if href:
                        contractor_hrefs.append(href)
                except Exception as e:
                    logger.error(f"Error getting href from link: {str(e)}")
                    continue

            logger.info(f"Stored all links for letter {letter}, now looping through each")
            
            for href in contractor_hrefs:
                try:
                    contractor = self.scrape_contractor_details(href)
                    if contractor:
                        # Save to database
                        try:
                            db.save_contractor(contractor)
                            logger.info(f"Successfully saved contractor {contractor.contract_number} to database")
                            time.sleep(5)  # Wait 5 seconds before processing next contractor
                        except Exception as e:
                            logger.error(f"Error saving contractor {contractor.contract_number} to database: {str(e)}")
                        
                        contractors.append(contractor)
                except Exception as e:
                    logger.error(f"Error processing contractor link: {str(e)}")
                    continue

            return contractors

        except Exception as e:
            logger.error(f"Error fetching contractors for letter {letter}: {str(e)}")
            return []
        finally:
            if db:
                try:
                    db.close()
                    logger.info("Database connection closed")
                except Exception as e:
                    logger.error(f"Error closing database connection: {str(e)}")

    def scrape_contractor_details(self, url: str):
        """
        Scrape details for a specific contractor
        
        Returns:
            List[Contractor]: List of all contractors scraped
            """
        try:
            # First visit home page to establish session
            self.driver.get(f"{self.BASE_URL}/home.do")
            time.sleep(2)  # Allow session to establish

            # Visit contractor list page
            self.driver.get(f"{self.CONTRACTOR_LIST_URL}?contractorListFor=A")
            time.sleep(2)

            # Now visit the contractor details page
            self.driver.get(url)
            
            # Wait for the page to load
            WebDriverWait(self.driver, self.TIMEOUT).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "td font.newcolumntitle"))
            )

            # Check for error message
            error_elements = self.driver.find_elements(By.CSS_SELECTOR, "td[bgcolor='#ffffcc'] font")
            for element in error_elements:
                if "Could not retrieve contractor information" in element.text:
                    logger.error(f"Error retrieving contractor information: {element.text}")
                    return None

            # Extract contractor details
            return self.extract_contractor_details()

        except Exception as e:
            logger.error(f"Error scraping contractor details: {str(e)}")
            return None

    def extract_contractor_details(self):
        """
        Extract contractor details from the current page 
        
        Returns:
            Optional[Contractor]: List of all contractors scraped
        """
        try:
            # Get the page source and parse with BeautifulSoup
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Extract contract number
            contract_number_element = soup.find('font', class_='newcolumntitle', string='Contract #:')
            if not contract_number_element:
                logger.error("Could not find contract number element")
                return None
            contract_number = contract_number_element.find_next('font').text.strip()
            contractor = Contractor(contract_number=contract_number)

            # Extract name
            name_element = soup.find('font', class_='newcolumntitle', string='Contractor:')
            if name_element:
                contractor.name = name_element.find_next('font').text.strip()

            # Extract address
            address_element = soup.find('font', class_='newcolumntitle', string='Address:')
            if address_element:
                contractor.address = address_element.find_next('font').text.strip()

            # Extract phone
            phone_element = soup.find('font', class_='newcolumntitle', string='Call:')
            if phone_element:
                contractor.phone = phone_element.find_next('font').text.strip()

            # Extract email
            email_element = soup.find('font', class_='newcolumntitle', string='Email:')
            if email_element:
                email_link = email_element.find_next('a')
                if email_link:
                    contractor.email = email_link.text.strip()

            # Extract website
            website_element = soup.find('font', class_='newcolumntitle', string='Web Address:')
            if website_element:
                website_link = website_element.find_next('a')
                if website_link:
                    contractor.website = website_link.get('href', '').strip()

            # Extract SAM UEI
            sam_uei_element = soup.find('font', class_='newcolumntitle', string='SAM UEI:')
            if sam_uei_element:
                contractor.sam_uei = sam_uei_element.find_next('font').text.strip()

            # Extract NAICS
            naics_element = soup.find('font', class_='newcolumntitle', string='NAICS:')
            if naics_element:
                contractor.naics = naics_element.find_next('font').text.strip()

            # Extract Socio-Economic
            socio_economic_element = soup.find('font', class_='newcolumntitle', string='Socio-Economic :')
            if socio_economic_element:
                contractor.socio_economic = socio_economic_element.find_next('font').text.strip()

            # Extract dates
            current_option_period_element = soup.find('font', class_='newcolumntitle', string='Current Option Period End Date :')
            if current_option_period_element:
                date_str = current_option_period_element.find_next('font').text.strip()
                if date_str:
                    contractor.current_option_period_end_date = self.parse_date(date_str)

            ultimate_contract_end_date_element = soup.find('font', class_='newcolumntitle', string='Ultimate Contract End Date :')
            if ultimate_contract_end_date_element:
                date_str = ultimate_contract_end_date_element.find_next('font').text.strip()
                if date_str:
                    contractor.ultimate_contract_end_date = self.parse_date(date_str)

            # Extract Government POC information
            govt_poc_element = soup.find('td', string='Govt. POC:')
            if govt_poc_element:
                poc_text = govt_poc_element.parent.text
                poc_lines = poc_text.split('\n')
                if len(poc_lines) >= 3:
                    contractor.govt_poc_name = poc_lines[1].strip()
                    contractor.govt_poc_phone = poc_lines[2].strip()
                    poc_email_element = govt_poc_element.parent.find('a', href=lambda x: x and x.startswith('mailto:'))
                    if poc_email_element:
                        contractor.govt_poc_email = poc_email_element.text.strip()

            # Extract EPLS status
            epls_element = soup.find('td', string='EPLS :')
            if epls_element:
                contractor.epls = epls_element.find_next('td').find('font').text.strip()

            # Extract contract details from the table
            contract_rows = soup.find_all('tr', valign='top', align='left')
            for row in contract_rows:
                cells = row.find_all('td')
                if len(cells) >= 6:
                    details = ContractDetails(contract_number=contract_number)
                    
                    # Source (Schedule)
                    source_cell = cells[0]
                    source_link = source_cell.find('a')
                    if source_link:
                        details.contract_title = source_link.text.strip()

                    # Contract Number
                    contract_num_cell = cells[2]
                    if contract_num_cell:
                        details.contract_number = contract_num_cell.text.strip()

                    # Current Option Period End Date
                    current_end_date_cell = cells[4]
                    if current_end_date_cell:
                        details.contract_award_date = current_end_date_cell.text.strip()

                    # Ultimate Contract End Date
                    ultimate_end_date_cell = cells[5]
                    if ultimate_end_date_cell:
                        details.contract_expiration_date = ultimate_end_date_cell.text.strip()

                    if details.contract_number:
                        details.contractor = contractor
                        contractor.contract_details.append(details)

            # Save the raw HTML for reference
            # self.save_raw_html(contract_number, soup.prettify())

            return contractor

        except Exception as e:
            logger.error(f"Error extracting contractor details: {str(e)}")
            return None

    def parse_date(self, date_str: str):
        """
        Parse date string to datetime object
          Returns:
          Optional[datetime] datetime object
        """
        try:
            return datetime.strptime(date_str, "%b %d, %Y")
        except Exception as e:
            logger.error(f"Error parsing date: {date_str}")
            return None

    def save_raw_html(self, contract_number: str, html_content: str):
        """Save raw HTML content to a file"""
        try:
            filename = f"contractor_data_{contract_number}.html"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            logger.info(f"Saved raw HTML data to {filename}")
        except Exception as e:
            logger.error(f"Failed to save HTML data: {str(e)}")

    def close(self):
        """Close the WebDriver"""
        if self.driver:
            self.driver.quit()

def main():
    scraper = GSAScraper()
   
    try:
        contractors = scraper.scrape_all_contractors()
        logger.info(f"Successfully scraped {len(contractors)} contractors")
        
        # Save results to JSON
        with open('contractors.json', 'w', encoding='utf-8') as f:
            json.dump([vars(c) for c in contractors], f, default=str, indent=2)
        logger.info("Saved results to contractors.json")
    finally:
        scraper.close()
        

if __name__ == "__main__":
    main() 