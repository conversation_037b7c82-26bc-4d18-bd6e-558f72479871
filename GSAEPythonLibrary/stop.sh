#!/bin/bash

# Set the working directory
cd "$(dirname "$0")"

# Set up logging
LOG_FILE="GSAEPythonLibrary.logs"
TIMESTAMP=$(date +"%Y-%m-%d %H:%M:%S")

# Find and stop the nohup process
PIDS=$(ps aux | grep -E "run_scheduler.py" | grep -v grep | awk '{print $2}')

if [ -z "$PIDS" ]; then
    echo "$TIMESTAMP - No GSA scheduler processes found to stop" >> "$LOG_FILE"
    exit 0
fi

# Kill all found processes
for PID in $PIDS; do
    kill -9 "$PID"
    echo "$TIMESTAMP - Stopped GSA scheduler process with PID: $PID" >> "$LOG_FILE"
done

# Clean up log files if needed
if [ -f nohup.out ]; then
    rm nohup.out
fi

echo "$TIMESTAMP - GSA scheduler service stopped" >> "$LOG_FILE"

exit 0