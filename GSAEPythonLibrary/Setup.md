# GSA Scraper Setup Guide

## Prerequisites

- Python 3.8 or higher
- Google Chrome browser
- SSL certificate file (us-east-1-bundle.pem)

## Setup Steps

### 1. Create and Activate Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On macOS/Linux:
source venv/bin/activate
# On Windows:
.\venv\Scripts\activate
```

### 2. Install Required Packages

```bash
pip install -r requirements.txt
```

### 3. SSL Certificate Setup

1. Download the SSL certificate file:
   - Place `us-east-1-bundle.pem` in the root directory of the project
   - This file is required for secure database connection

### 4. Environment Variables Setup

Create a `.env` file in the root directory with the following variables:

```env
# Chrome Browser Path
CHROME_BROWSER=/path/to/your/chrome/browser

# Database Configuration
DB_NAME=postgres
DB_USER=alpha
DB_PASSWORD=5t3r2i66123
DB_HOST=govcon-1.cwjbmcfdk9qo.us-east-1.rds.amazonaws.com
DB_PORT=5432
```

### 5. Required Python Packages

Create a `requirements.txt` file with the following dependencies:

```txt
selenium>=4.0.0
beautifulsoup4>=4.9.3
psycopg2-binary>=2.9.3
python-dotenv>=0.19.0
webdriver-manager>=3.8.0
```

### 6. Running the Scraper

```bash
# Make sure your virtual environment is activated
python GSAEPythonLibrary/gsa_scraper.py
```

## Troubleshooting

### Common Issues

1. **Chrome Browser Not Found**

   - Ensure the `CHROME_BROWSER` path in `.env` is correct
   - For macOS, typical path is: `/User/Google Chrome.app/Contents/MacOS/Google Chrome`

2. **SSL Certificate Error**

   - Verify `us-east-1-bundle.pem` is in the correct location
   - Check file permissions

3. **Database Connection Issues**
   - Verify database credentials in `.env`
   - Ensure the database server is accessible
   - Check if the SSL certificate is properly configured

### Logging

- Logs are written to the console
- Check for any error messages in the output
- Log level is set to INFO by default

## Notes

- The scraper includes a 5-second delay between contractor processing to avoid rate limiting
- Raw HTML data is saved for each contractor (commented out by default)
- The scraper processes contractors alphabetically (A-Z)
