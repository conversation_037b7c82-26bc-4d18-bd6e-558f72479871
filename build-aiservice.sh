#!/bin/bash

set -e

echo "=== AIService Build Script ==="

# Check configuration
if [ ! -f "properties/.env.ini" ]; then
    echo "❌ Error: properties/.env.ini not found!"
    echo "Please create your configuration file first."
    echo ""
    echo "Example structure:"
    echo "  [AIService]"
    echo "  DATABASE_URL=postgresql://user:pass@host:port/db"
    echo "  API_KEY=your-api-key"
    echo "  # ... other configurations"
    exit 1
fi

echo "✅ Configuration file found: properties/.env.ini"

# Load environment if exists
if [ -f ".env" ]; then
    echo "📄 Loading environment variables from .env"
    export $(grep -v '^#' .env | xargs)
fi

# Set defaults
export TAG=${TAG:-latest}
export APP_PORT=${APP_PORT:-3011}
export APP_USER=${APP_USER:-kontratar}

echo "🏗️  Building AIService container..."
echo "   - Tag: ${TAG}"
echo "   - Port: ${APP_PORT}"
echo "   - User: ${APP_USER}"
echo

# Build the container
docker-compose build --no-cache

echo
echo "✅ Build completed successfully!"
echo
echo "🚀 To run the service:"
echo "   docker-compose up"
echo
echo "🔍 To run in detached mode:"
echo "   docker-compose up -d"
echo
echo "📋 To view logs:"
echo "   docker-compose logs -f aiservice"
echo
