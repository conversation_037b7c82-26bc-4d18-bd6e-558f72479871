GSAEPythonLibrary/.env
GSAEPythonLibrary/.venv
GSAEPythonLibrary/__pycache__
GSAEPythonLibrary/gsa_scheduler.log
GSAEPythonLibrary/nohup.out
GSAEPythonLibrary/scheduler/__pycache__
contractors.json
GSAEPythonLibrary.logs
.vscode
env_adept/
AIService/data/
AIService/test.py
*/.DS_Store
#*/.env
.idea/

state-opportunity/service/schedule/__pycache__
state-opportunity/common/__pycache__
state-opportunity/.env
state-opportunity/.venv
state-opportunity/__pycache__
state-opportunity/maryland_solicitations.json
state-opportunity/maryland_solicitations.json
state-opportunity/maryland_scraper.log
state-opportunity/maryland_scheduler.log
state-opportunity/master_scheduler.log

AIService/chroma
AIService/.env

TokenReplacementUtility
TokenReplacementUtility/*


__pycache__
*logs
