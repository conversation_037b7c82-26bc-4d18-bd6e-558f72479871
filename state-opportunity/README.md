# Maryland eMMA Solicitations Scraper

This Python script scrapes government contract opportunities from the Maryland eMMA (eMaryland Marketplace Advantage) public solicitations page.

## Features

- Scrapes all available solicitations from the public procurement page
- Extracts key details including:
  - Title and BPM number
  - Agency and contact information
  - Due dates and posting dates
  - Contract categories and types
  - Status and amendment information
- Saves data in both JSON and CSV formats
- Includes logging and error handling
- Displays summary in console

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

Run the scraper:
```bash
python maryland_scrapper.py
```

The script will:
1. Connect to the Maryland eMMA website
2. Parse the solicitations table
3. Extract all available contract opportunities
4. Display a summary in the console
5. Save the data to two files:
   - `maryland_solicitations.json` - Raw data in JSON format
   - `maryland_solicitations.csv` - Formatted data for Excel/analysis

## Output Files

### JSON Output
Contains the raw scraped data with all available fields for each solicitation.

### CSV Output
Formatted data suitable for spreadsheet analysis with columns for:
- Title, BPM Number, Status
- Due Date, Posted Date
- Agency, Category, Type
- Contact information and links

## Logging

The script creates a log file `maryland_scraper.log` that tracks:
- Scraping progress
- Any errors encountered
- Number of solicitations found
- Detailed debugging information

## Notes

- The script respects the website's structure and includes appropriate delays
- It uses browser-like headers to ensure compatibility
- Error handling ensures the script continues even if individual records fail
- The scraper is designed to handle the dynamic nature of the eMMA website

## Target Website

- URL: https://emma.maryland.gov/page.aspx/en/rfp/request_browse_public
- Maryland's official procurement platform for public solicitations 