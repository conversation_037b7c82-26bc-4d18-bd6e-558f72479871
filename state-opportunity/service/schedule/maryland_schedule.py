#!/usr/bin/env python3
"""
Maryland Scraper Scheduler
This module schedules the Maryland solicitation scraper to run every Sunday at 6:00 PM.
"""

import sys
import os
import logging
from datetime import datetime


# Import the generic scheduler using importlib
import importlib.util
try:
   
    scheduler_path = os.path.join(os.path.dirname(__file__), '..', '..', 'common', 'scheduler.py')
    spec = importlib.util.spec_from_file_location("scheduler", scheduler_path)
    if spec is None or spec.loader is None:
        raise ImportError(f"Could not load scheduler from {scheduler_path}")
    
    scheduler_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(scheduler_module)
    
    create_scheduler = scheduler_module.create_scheduler
    ScheduleFrequency = scheduler_module.ScheduleFrequency
    schedule_weekly = scheduler_module.schedule_weekly

    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))
    from common.schedule_lock import ScheduleLockService
except Exception as e:
    logging.error(f"Failed to import scheduler: {e}")
    sys.exit(1)

# Add the parent directory to import the Maryland scraper
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

try:
    from maryland_scrapper import main as run_maryland_scraper
except ImportError as e:
    logging.error(f"Failed to import maryland_scrapper: {e}")
    sys.exit(1)

# Configure logging
log_filename = 'maryland_scheduler.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)



def schedule_maryland_scraper(run_time="00:00"):
    """
    Schedule the Maryland scraper to run every Sunday at the specified time.
    
    Args:
        run_time (str): Time to run the scraper in HH:MM format (default: "18:00" for 6:00 PM)
    """
    try:
        # Create scheduler instance
        scheduler = create_scheduler(log_filename)
        
        # Schedule the Maryland scraper to run every Sunday at 6 PM
        schedule_weekly(
            scheduler, 
            "maryland_scraper", 
            run_maryland_scraper, 
            "sunday", 
            run_time
        )
        
        logger.info(f"Maryland scraper scheduled to run every Sunday at {run_time}")
        logger.info("Scheduler is now running. Press Ctrl+C to stop.")
        
        # Start the scheduler
        scheduler.start()
        
    except KeyboardInterrupt:
        logger.info("Scheduler stopped by user")
    except Exception as e:
        logger.error(f"Error in Maryland scheduler: {str(e)}")
        raise

def run_now():
    """
    Run the Maryland scraper immediately (for testing purposes).
    """
    logger.info("Running Maryland scraper immediately...")
    run_maryland_scraper()

def main():
    """
    Main function to start the Maryland scraper scheduler.
    """
    maryland_lock = ScheduleLockService("MARYLAND_LOCK", "  MARYLAND_SCRAPER")
    lock_service = maryland_lock.try_acquire_lock()
    if lock_service is not True:
        logger.error("Failed to acquire lock. Exiting...")
        return  # Exit without releasing lock since we never acquired it
    try:
        schedule_maryland_scraper()
    except Exception as e:
        logger.error(f"Error in Maryland scheduler: {str(e)}")
        raise
    finally:
        maryland_lock.release_lock()

if __name__ == "__main__":
    main()
