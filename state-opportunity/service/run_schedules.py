#!/usr/bin/env python3
"""
Master Scheduler Runner
This module automatically discovers and runs all schedulers in the schedule folder.
Each scheduler runs in its own thread for concurrent execution.
"""

import os
import sys
import threading
import time
import logging
import importlib.util
import signal
from pathlib import Path
from typing import List, Dict, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('master_scheduler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SchedulerRunner:
    """Main class to discover and run all schedulers"""
    
    def __init__(self, schedule_folder: Optional[str] = None):
        """
        Initialize the scheduler runner
        
        Args:
            schedule_folder (str): Path to the schedule folder (default: ./schedule)
        """
        if schedule_folder is None:
            schedule_folder = os.path.join(os.path.dirname(__file__), 'schedule')
        
        self.schedule_folder = schedule_folder
        self.running_threads: List[threading.Thread] = []
        self.scheduler_modules: List[Dict[str, Any]] = []
        self.shutdown_event = threading.Event()
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_event.set()
    
    def discover_schedulers(self) -> List[str]:
        """
        Discover all scheduler files in the schedule folder
        
        Returns:
            List[str]: List of scheduler file paths
        """
        schedulers = []
        
        if not os.path.exists(self.schedule_folder):
            logger.error(f"Schedule folder not found: {self.schedule_folder}")
            return schedulers
        
        for file in os.listdir(self.schedule_folder):
            if file.endswith('.py') and not file.startswith('__'):
                scheduler_path = os.path.join(self.schedule_folder, file)
                schedulers.append(scheduler_path)
                logger.info(f"Discovered scheduler: {file}")
        
        return schedulers
    
    def load_scheduler_module(self, scheduler_path: str) -> Optional[Dict[str, Any]]:
        """
        Load a scheduler module dynamically
        
        Args:
            scheduler_path (str): Path to the scheduler file
            
        Returns:
            Dict[str, Any]: Module info with name, module, and callable function
        """
        try:
            scheduler_name = Path(scheduler_path).stem
            
            # Load module using importlib
            spec = importlib.util.spec_from_file_location(scheduler_name, scheduler_path)
            if spec is None or spec.loader is None:
                raise ImportError(f"Could not load scheduler from {scheduler_path}")
            
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # Look for main function (most common pattern)
            scheduler_function = None
            if hasattr(module, 'main'):
                scheduler_function = module.main
            elif hasattr(module, 'run'):
                scheduler_function = module.run
            elif hasattr(module, 'start'):
                scheduler_function = module.start
            else:
                # Look for any function that contains 'schedule' in the name
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if callable(attr) and 'schedule' in attr_name.lower() and not attr_name.startswith('_'):
                        scheduler_function = attr
                        break
            
            if scheduler_function is None:
                logger.warning(f"No suitable function found in {scheduler_name}")
                return None
            
            return {
                'name': scheduler_name,
                'module': module,
                'function': scheduler_function,
                'path': scheduler_path
            }
            
        except Exception as e:
            logger.error(f"Failed to load scheduler {scheduler_path}: {e}")
            return None
    
    def run_scheduler_in_thread(self, scheduler_info: Dict[str, Any]):
        """
        Run a scheduler in a separate thread
        
        Args:
            scheduler_info (Dict[str, Any]): Scheduler module information
        """
        scheduler_name = scheduler_info['name']
        scheduler_function = scheduler_info['function']
        
        try:
            logger.info(f"Starting scheduler: {scheduler_name}")
            
            # Call the scheduler function directly
            try:
                scheduler_function()
            except KeyboardInterrupt:
                logger.info(f"Scheduler {scheduler_name} stopped by KeyboardInterrupt")
            except Exception as e:
                logger.error(f"Error in scheduler {scheduler_name}: {e}")
            
        except Exception as e:
            logger.error(f"Failed to run scheduler {scheduler_name}: {e}")
        finally:
            logger.info(f"Scheduler ended: {scheduler_name}")
    
    def start_all_schedulers(self):
        """Start all discovered schedulers in separate threads"""
        # Discover schedulers
        scheduler_paths = self.discover_schedulers()
        
        if not scheduler_paths:
            logger.warning("No schedulers found in the schedule folder")
            return
        
        # Load scheduler modules
        for path in scheduler_paths:
            scheduler_info = self.load_scheduler_module(path)
            if scheduler_info:
                self.scheduler_modules.append(scheduler_info)
        
        if not self.scheduler_modules:
            logger.error("No valid schedulers found")
            return
        
        logger.info(f"Starting {len(self.scheduler_modules)} schedulers...")
        
        # Start each scheduler in its own thread
        for scheduler_info in self.scheduler_modules:
            thread = threading.Thread(
                target=self.run_scheduler_in_thread,
                args=(scheduler_info,),
                name=f"Scheduler-{scheduler_info['name']}",
                daemon=True
            )
            thread.start()
            self.running_threads.append(thread)
            logger.info(f"Started scheduler: {scheduler_info['name']}")
        
        # Wait for shutdown signal or threads to complete
        try:
            while not self.shutdown_event.is_set():
                # Check if any threads are still alive
                alive_threads = [t for t in self.running_threads if t.is_alive()]
                if not alive_threads:
                    logger.info("All scheduler threads have ended")
                    break
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Received KeyboardInterrupt, shutting down...")
        
        self.shutdown()
    
    def shutdown(self):
        """Gracefully shutdown all schedulers"""
        logger.info("Shutting down all schedulers...")
        
        # Set shutdown event
        self.shutdown_event.set()
        
        # Wait for threads to finish (with timeout)
        for thread in self.running_threads:
            if thread.is_alive():
                thread.join(timeout=5)
                if thread.is_alive():
                    logger.warning(f"Thread {thread.name} did not finish gracefully")
        
        logger.info("All schedulers shut down")
    
    def list_schedulers(self):
        """List all discovered schedulers"""
        scheduler_paths = self.discover_schedulers()
        
        print("Discovered Schedulers:")
        print("=" * 50)
        
        for path in scheduler_paths:
            scheduler_info = self.load_scheduler_module(path)
            if scheduler_info:
                print(f"✓ {scheduler_info['name']} - {scheduler_info['function'].__name__}()")
                print(f"  Path: {scheduler_info['path']}")
            else:
                print(f"✗ {Path(path).stem} - Failed to load")
                print(f"  Path: {path}")
            print()

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Master Scheduler Runner')
    parser.add_argument('--list', '-l', action='store_true', 
                       help='List all discovered schedulers and exit')
    parser.add_argument('--schedule-folder', '-f', 
                       help='Path to the schedule folder (default: ./schedule)')
    
    args = parser.parse_args()
    
    # Create scheduler runner
    runner = SchedulerRunner(args.schedule_folder)
    
    if args.list:
        runner.list_schedulers()
    else:
        try:
            runner.start_all_schedulers()
        except Exception as e:
            logger.error(f"Failed to start schedulers: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main()
