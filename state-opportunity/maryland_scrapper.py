#!/usr/bin/env python3
"""
Maryland eMMA Public Solicitations Scraper
Scrapes contract opportunities from https://emma.maryland.gov/page.aspx/en/rfp/request_browse_public
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import json
import time
from datetime import datetime
import logging
from urllib.parse import urljoin, urlparse
import re
import os
import sys
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Import dbConnection if available
sys.path.append(os.path.join(os.path.dirname(__file__), 'common'))
from dbConnection import DatabaseConnection  # type: ignore
from scrapper import DefaultScraper #type: ignore


# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('maryland_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)
class Db(DatabaseConnection):
     def convert_date(self, date_str):
        """Convert empty date strings to None for database, keep valid dates"""
        if not date_str or date_str.strip() == '':
            return None
        
        # Try to parse the date to validate it
        try:
            from datetime import datetime
            date_str = date_str.strip()
            
            # Try different date formats to validate
            formats = [
                '%m/%d/%Y %I:%M:%S %p',  # 6/26/2025 11:27:41 AM
                '%m/%d/%Y',              # 7/14/2025
                '%Y-%m-%d',              # 2025-07-14
                '%m-%d-%Y',              # 06-26-2025
                '%d/%m/%Y',              # 14/07/2025
            ]
            
            for fmt in formats:
                try:
                    datetime.strptime(date_str, fmt)
                    return date_str  # Return original valid date string
                except ValueError:
                    continue
                    
            # If no format matches but string exists, return it anyway
            return date_str
            
        except Exception:
            return date_str  # Return original if parsing fails
        
     def parse_date_for_comparison(self, date_value):
        """Parse and standardize date for comparison"""
        if date_value is None or date_value == '':
            return None
            
        from datetime import datetime
        
        # If it's already a date object, convert to string
        if hasattr(date_value, 'strftime'):
            return date_value.strftime('%Y-%m-%d')
        
        # Convert string date to standard format
        date_str = str(date_value).strip()
        if not date_str:
            return None
            
        try:
            # Try different date formats
            formats = [
                '%m/%d/%Y %I:%M:%S %p',  # 6/26/2025 11:27:41 AM
                '%m/%d/%Y',              # 7/14/2025
                '%Y-%m-%d',              # 2025-07-14 (already standard)
                '%m-%d-%Y',              # 06-26-2025
                '%d/%m/%Y',              # 14/07/2025
            ]
            
            for fmt in formats:
                try:
                    parsed_date = datetime.strptime(date_str, fmt)
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    continue
                    
            # If no format matches, return original
            return date_str
            
        except Exception:
            return date_str
        
     def save_solicitation(self, solicitation):
        """Save solicitation data to database"""
        try:
            with self.conn.cursor() as cur:
                # Check if record exists
                cur.execute("""
                    SELECT id FROM kontratar_states.us_maryland 
                    WHERE id = %s
                """, (solicitation['bmp_number'],))
                
                existing_record = cur.fetchone()
                print(f"Existing record: {existing_record}")
                if existing_record is not None and existing_record[0] is not None:
                    # Override existing record completely
                    print(f"Overriding existing record for BMP: {solicitation['bmp_number']}")
                    
                    converted_due_date = self.convert_date(solicitation['due_date'])
                    converted_published_date = self.convert_date(solicitation['published_date'])
                    
                    print(f"  Due date: '{solicitation.get('due_date', 'N/A')}' -> '{converted_due_date}'")
                    print(f"  Published date: '{solicitation.get('published_date', 'N/A')}' -> '{converted_published_date}'")
                    
                    cur.execute("""
                        UPDATE kontratar_states.us_maryland 
                        SET title = %s, edit_link = %s, status = %s, due_date = %s, 
                            published_date = %s, category = %s, type = %s, agency = %s, source_url = %s
                        WHERE id = %s
                    """, (
                        solicitation['title'],
                        solicitation['edit_link'],
                        solicitation['status'],
                        converted_due_date,
                        converted_published_date,
                        solicitation['category'],
                        solicitation['type'],
                        solicitation['agency'],
                        solicitation['source_url'],
                        solicitation['bmp_number']
                    ))
                    self.conn.commit()
                    print(f"Updated existing record for ID: {solicitation['bmp_number']}")
                    return 'updated'
                        
                else:
                    # Insert new record with proper date handling
                    print(f"Inserting new record for BMP: {solicitation['bmp_number']}")
                    print(f"  Due date before conversion: '{solicitation.get('due_date', 'N/A')}'")
                    print(f"  Published date before conversion: '{solicitation.get('published_date', 'N/A')}'")
                    
                    converted_due_date = self.convert_date(solicitation['due_date'])
                    converted_published_date = self.convert_date(solicitation['published_date'])
                    
                    print(f"  Due date after conversion: '{converted_due_date}'")
                    print(f"  Published date after conversion: '{converted_published_date}'")
                    
                    cur.execute("""
                        INSERT INTO kontratar_states.us_maryland (
                            title, edit_link, id, status, due_date, published_date, category, type, agency, source_url
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                        )
                    """, (
                        solicitation['title'], 
                        solicitation['edit_link'], 
                        solicitation['bmp_number'], 
                        solicitation['status'], 
                        converted_due_date, 
                        converted_published_date, 
                        solicitation['category'], 
                        solicitation['type'], 
                        solicitation['agency'], 
                        solicitation['source_url']
                    ))
                    self.conn.commit()
                    print(f"Inserted new record for ID: {solicitation['bmp_number']}")
                    return 'new'
                
              
            
                
        except Exception as e:
            self.conn.rollback()  # Rollback the failed transaction
            print(f"Transaction rolled back for BMP: {solicitation.get('bmp_number', 'Unknown')}")
            logger.error(f"Error saving solicitation to database: {e}")
            # Re-raise the exception if needed for debugging
            raise
class MarylandSolicrationScraper(DefaultScraper):
    def __init__(self, base_url, target_url=None, headers=None, session_config=None):
        super().__init__(base_url, target_url, headers, session_config)
        self.solicitations = []
        self.attachments = []
    
    def clean_text(self, text):
        """Clean and normalize text"""
        if not text:
            return ""
        
        # Remove leading "Edit" (case-insensitive) if it's the first word
        text = text.strip()
        if text.lower().startswith("edit "):
            text = text[5:]  # remove "Edit " (5 characters)

        # Normalize whitespace
        text = ' '.join(text.split())
        return text.strip()

    
    def parse_solicitation_row(self, row):
        """Parse a single table row containing solicitation data"""
        cells = row.find_all(['td', 'th']) # type: ignore
        if len(cells) < 2:  # Minimum cells needed for a valid row
            return None
        
        try:
            # Extract data based on the table structure observed
            solicitation = {}
            title_text = ""
            edit_link = ""
            # Look for a link in the first few cells
            for i, cell in enumerate(cells[:4]):  # Check first 4 cells instead of 3
                link = cell.find('a')
                if link and link.get_text().strip():
                    title_text = self.clean_text(link.get_text())
                    edit_link = urljoin(self.base_url, link.get('href', ''))
                    break
            
            # If no link found, use text from first cell that has substantial content
            if not title_text:
                for cell in cells[:3]:
                    text = self.clean_text(cell.get_text())
                    if text and len(text) > 3:  # At least 4 characters
                        title_text = text
                        break    
            solicitation['title'] = title_text
            solicitation['edit_link'] = edit_link
            # Extract other fields by position with more flexibility
            # Try to find BMP number in early columns - look for patterns like BPM followed by digits
            bmp_number = ""
            for i, cell in enumerate(cells[:4]):
                text = self.clean_text(cell.get_text())
                # Look for BPM pattern (BPM followed by digits)
                if text and ('BPM' in text.upper() or 'BMP' in text.upper()):
                    # Extract the actual BPM number
                    import re
                    bmp_match = re.search(r'BM[P]?\d+', text.upper())
                    if bmp_match:
                        bmp_number = bmp_match.group()
                        break
                # Also check if it's just a number that could be a BMP number
                elif text and text.isdigit() and len(text) >= 5:
                    bmp_number = text
                    break
            
            # If still no BMP number found, use position-based extraction
            if not bmp_number and len(cells) > 1:
                bmp_number = self.clean_text(cells[1].get_text())
            
            solicitation['bmp_number'] = bmp_number
            solicitation['status'] = self.clean_text(cells[3].get_text()) if len(cells) > 3 else ""
            solicitation['due_date'] = self.clean_text(cells[4].get_text()) if len(cells) > 4 else ""
            solicitation['published_date'] = self.clean_text(cells[5].get_text()) if len(cells) > 5 else ""
            solicitation['category'] = self.clean_text(cells[6].get_text()) if len(cells) > 6 else ""
            solicitation['type'] = self.clean_text(cells[7].get_text()) if len(cells) > 7 else ""
            solicitation['agency'] = self.clean_text(cells[8].get_text()) if len(cells) > 8 else ""
            
            # Clean up any remaining long text fields that might be form data
            for key, value in solicitation.items():
                if isinstance(value, str) and len(value) > 150:
                    solicitation[key] = value[:150] + "..."
            solicitation['scraped_at'] = datetime.now().isoformat()
            solicitation['source_url'] = self.target_url
            return solicitation
        except Exception as e:
            logger.error(f"Error parsing row: {e}")
            return None
    
    
    def save_to_json(self, filename='maryland_solicitations.json'):
        """Save scraped data to JSON file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.solicitations, f, indent=2, ensure_ascii=False)
            logger.info(f"Data saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving to JSON: {e}")
    

            
    def parse_page_with_soup(self, soup):
        """Parse a BeautifulSoup object for solicitations"""
        page_solicitations = []
        
        try:
            # Find the specific table containing solicitations data
            data_table = soup.find('table', {'id': 'body_x_grid_grd'})
            
            if not data_table:
                logger.error("Could not find the solicitations data table")
                return []
            
            rows = data_table.find_all('tr')
            
            for row_idx, row in enumerate(rows):
                # Skip header rows
                if row.find('th'):
                    continue
                    
                # Skip first row if it's typically headers
                if row_idx == 0:
                    continue
                
                # Check cells count - be less strict
                cells = row.find_all(['td', 'th'])
                if len(cells) < 3:  # Reduced from 5 to 3
                    continue
                
                solicitation = self.parse_solicitation_row(row)
                if solicitation:
                    # More lenient validation
                    title = solicitation.get('title', '')
                    bmp_number = solicitation.get('bmp_number', '')
                    
                    # Skip if both title and BMP number are empty
                    if not title and not bmp_number:
                        continue
                    
                    # Add to page solicitations list
                    page_solicitations.append(solicitation)
                    # Also add to instance list for compatibility
                    self.solicitations.append(solicitation)
                    logger.info(f"Scraped ({len(self.solicitations)}): {title[:50]}... (BMP: {bmp_number})")
            
            logger.info(f"Found {len(page_solicitations)} solicitations on current page")
            return page_solicitations
            
        except Exception as e:
            logger.error(f"Error parsing page with soup: {e}")
            return []

    def scrape_solicitation_attachments(self):
        """Scrape attachments from each solicitation's detail page using Beautiful Soup"""
        if not self.solicitations:
            logger.warning("No solicitations found to scrape attachments from")
            return
        
        logger.info(f"Starting attachment scraping for {len(self.solicitations)} solicitations")
        
        for idx, solicitation in enumerate(self.solicitations):
            edit_link = solicitation.get('edit_link')
            bpm_number = solicitation['bmp_number']
            
            if not edit_link:
                logger.warning(f"No edit_link found for solicitation: {solicitation.get('title', '')[:50]}...")
                continue
            
            logger.info(f"Scraping attachments for ({idx+1}/{len(self.solicitations)}): {solicitation.get('title', '')[:50]}...")
            
            try:
                # Get the solicitation detail page using requests
                response = self.get_page_content(edit_link)
                if not response:
                    logger.error(f"Failed to get page content for {edit_link}")
                    continue
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                               # Find table inside the attachment container div using CSS selector
                attachment_table = soup.select_one('div#body_x_tabc_rfp_ext_prxrfp_ext_x_phcDoc table')
                
                if attachment_table:
                    # Parse the table content
                    table_attachments = self.parse_attachment_table_soup(attachment_table, solicitation)
                    
                    if table_attachments:
                        self.attachments.extend(table_attachments)
                        logger.info(f"Found {len(table_attachments)} attachments for BMP: {bpm_number}")
                    else:
                        logger.info(f"No attachments found for BMP: {bpm_number}")
                else:
                    logger.info(f"No attachment table found for BMP: {bpm_number}")
                    
            except Exception as e:
                logger.error(f"Failed to scrape attachments for {edit_link}: {e}")
                continue
            
            # Small delay between requests to be respectful
            time.sleep(1)
            
        logger.info(f"Attachment scraping completed. Found {len(self.attachments)} total attachments")

    def parse_attachment_table_soup(self, table_element, solicitation):
        """Parse the attachment table and return single attachment object with array of att objects"""
        att_objects = []
        
        try:
            # Get all rows from the table
            rows = table_element.find_all('tr')
            
            # Process data rows (skip header)
            for row_idx, row in enumerate(rows[1:], 1):
                try:
                    cells = row.find_all('td')
                    if not cells or len(cells) < 4:
                        continue
                    
                    # Extract document link and title from any cell with <a> tag
                    doc_link = ""
                    doc_title = ""
                    
                    # Look for <a> tag in any cell
                    for cell in cells:
                        link = cell.find('a')
                        if link:
                            doc_link = link.get('href', '')  # Don't join with base URL
                            doc_title = self.clean_text(link.get_text())
                            break
                    
                    # If no link found, use text from first cell
                    if not doc_title and cells:
                        doc_title = self.clean_text(cells[0].get_text())
                    
                   
                    
                    # Save link and title without downloading
                    if doc_link:
                        # Ensure the URL is properly formed
                        if doc_link.startswith('/'):
                            full_doc_link = urljoin(self.base_url, doc_link)
                        else:
                            full_doc_link = doc_link
                    else:
                        full_doc_link = ""
                    
                    # Create individual att object with link info only
                    att_obj = {
                        'doctitle': doc_title,
                        'docLink': full_doc_link
                    }
                    
                    att_objects.append(att_obj)
                    
                except Exception as cell_e:
                    logger.warning(f"Error parsing row {row_idx}: {cell_e}")
                    continue
            
            # Return single attachment record with array of att objects
            if att_objects:
                # Get common fields from first row
                first_row = rows[1] if len(rows) > 1 else None
                doc_type = ""
                last_modified = ""
                create_date = ""
                
                if first_row:
                    cells = first_row.find_all('td')
                    doc_type = self.clean_text(cells[1].get_text()) if len(cells) > 1 else ""
                    last_modified = self.clean_text(cells[3].get_text()) if len(cells) > 3 else ""
                    create_date = self.clean_text(cells[4].get_text()) if len(cells) > 4 else ""
                
                attachment = {
                    'bpm_number': solicitation['bmp_number'],
                    'type': doc_type,
                    'att': att_objects,
                    'last_modified': last_modified,
                    'create_date': create_date,
                    'scraped_at': datetime.now().isoformat()
                }
                
                return [attachment]  # Return as list for consistency
                    
        except Exception as e:
            logger.error(f"Error parsing attachment table: {e}")
        
        return []

    def save_attachments_to_json(self, filename='maryland_attachments.json'):
        """Save attachment data to JSON file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.attachments, f, indent=2, ensure_ascii=False)
            logger.info(f"Attachments saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving attachments to JSON: {e}")

def main():
    """Main execution function"""
    print("Starting Maryland eMMA Solicitations Scraper...")
    print("=" * 50)
    
    scraper = MarylandSolicrationScraper(base_url="https://emma.maryland.gov", target_url="https://emma.maryland.gov/page.aspx/en/rfp/request_browse_public",headers=None, session_config=None)
    db = Db()
    
    try:
        # Configure pagination
        pagination_config = {
            'next_button': {
                'id': 'body_x_grid_PagerBtnNextPage'
            },
            'content_element_id': 'body_x_grid_grd',
            'page_delay': 1,
            'max_pages': 100
        }
        
        # Scrape the data
        scraper.scrape_with_selenium(
            scrape_element='table', 
            scrape_element_id='body_x_grid_grd', 
            parse_method=scraper.parse_page_with_soup,
            chrome_options=None, 
            pagination_config=pagination_config
        )
        
        # Save main solicitations data
        scraper.save_to_json()
        
        # Scrape attachments from each solicitation (collect links only)
        print("\nStarting attachment link collection...")
        scraper.scrape_solicitation_attachments()
        
        # Save attachment data
        scraper.save_attachments_to_json()
        solicitation = open('maryland_solicitations.json', 'r')
        solicitation_data = json.load(solicitation)
        print(f"Total solicitations to process: {len(solicitation_data)}")
        
        saved_count = 0
        failed_count = 0
        updated_count = 0
        
        for idx, solicitation in enumerate(solicitation_data):
            try:    
                result = db.save_solicitation(solicitation)
                time.sleep(3)  
                
                if result == 'updated':
                    updated_count += 1
                else:
                    saved_count += 1
                    
            except Exception as e:
                print(f"FAILED to save solicitation: {solicitation.get('title', 'Unknown')[:50]} - Error: {e}")
                print(f"BMP Number: {solicitation.get('bmp_number', 'N/A')}")
                failed_count += 1
                # Don't break, continue with next record
                continue
        
        print(f"\n=== DATABASE SAVE COMPLETED ===")
        print(f"Total records processed: {len(solicitation_data)}")
        print(f"New records inserted: {saved_count}")
        print(f"Existing records updated: {updated_count}")
        print(f"Failed to save: {failed_count}")
        print(f"Total successful: {saved_count + updated_count}")
        
    except Exception as e:
        logger.error(f"Scraping failed: {e}")
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
