#!/bin/bash

# Set the working directory
cd "$(dirname "$0")"

# Log file for tracking stop actions
LOG_FILE="state-opportunity.logs"
TIMESTAMP=$(date +"%Y-%m-%d %H:%M:%S")

# Find the process running the scheduler
SCHEDULER_PID=$(pgrep -f "service/run_schedules.py")

if [ -n "$SCHEDULER_PID" ]; then
    # Log the attempt to stop the process
    echo "$TIMESTAMP - Attempting to stop state opportunity scraper (PID: $SCHEDULER_PID)" >> "$LOG_FILE"
    
    # Attempt to gracefully terminate the process
    kill -15 "$SCHEDULER_PID"
    
    # Wait a moment to allow graceful shutdown
    sleep 2
    
    # Check if the process is still running and force kill if necessary
    if kill -0 "$SCHEDULER_PID" 2>/dev/null; then
        kill -9 "$SCHEDULER_PID"
        echo "$TIMESTAMP - Forcefully terminated state opportunity scraper (PID: $SCHEDULER_PID)" >> "$LOG_FILE"
    else
        echo "$TIMESTAMP - State opportunity scraper stopped gracefully (PID: $SCHEDULER_PID)" >> "$LOG_FILE"
    fi
else
    echo "$TIMESTAMP - No running state opportunity scraper found" >> "$LOG_FILE"
fi 