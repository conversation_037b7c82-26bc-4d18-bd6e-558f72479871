#!/usr/bin/env python3
"""
Default Scraper Class
A flexible web scraper that can be configured for different websites and use cases.
"""

import os
import requests
from bs4 import BeautifulSoup
import time
import logging
from urllib.parse import urljoin
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class DefaultScraper:
    def __init__(self, base_url, target_url=None, headers=None, session_config=None):
        """
        Initialize the scraper with basic configuration
        
        Args:
            base_url (str): Base URL of the website
            target_url (str): Initial target URL to scrape (defaults to base_url)
            headers (dict): Custom headers for requests
            session_config (dict): Additional session configuration
        """
        self.base_url = base_url
        self.target_url = target_url or base_url
        
        # Setup requests session
        self.session = requests.Session()
        
        # Default headers
        default_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        # Update with custom headers if provided
        if headers:
            default_headers.update(headers)
        
        self.session.headers.update(default_headers)
        
        # Apply session configuration
        if session_config:
            for key, value in session_config.items():
                setattr(self.session, key, value)
        
        # Data storage
        self.scraped_data = []
        self.driver = None
        self.setup_driver()
        
        # Setup logging
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def get_page_content(self, url, retries=3, timeout=30):
        """
        Fetch page content with retries
        
        Args:
            url (str): URL to fetch
            retries (int): Number of retry attempts
            timeout (int): Request timeout in seconds
            
        Returns:
            requests.Response: Response object
        """
        for attempt in range(retries):
            try:
                self.logger.info(f"Fetching page: {url} (Attempt {attempt + 1})")
                response = self.session.get(url, timeout=timeout)
                response.raise_for_status()
                return response
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"Attempt {attempt + 1} failed: {e}")
                if attempt < retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                else:
                    self.logger.error(f"Failed to fetch {url} after {retries} attempts")
                    raise
        
        return None

    def setup_driver(self):
        """Setup Selenium WebDriver with appropriate options"""
        chrome_browser = os.getenv("CHROME_BROWSER")
        if not chrome_browser:
            raise EnvironmentError("CHROME_BROWSER environment variable is not set. Please add the path to the Chrome Testing app in the .env file.")
        
        print(f"Chrome browser: {chrome_browser}")
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-data-dir=/tmp/chrome-testing-{}'.format(int(time.time())))
        chrome_options.binary_location = chrome_browser

        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.implicitly_wait(10)
    
    def scrape_with_selenium(self, scrape_element, scrape_element_id, parse_method, 
                           chrome_options=None, pagination_config=None, **kwargs):
        """
        Scrape using Selenium with configurable parameters
        
        Args:
            scrape_element (str): Type of element to scrape ('table', 'div', 'ul', etc.)
            scrape_element_id (str): ID or selector of the element to scrape
            parse_method (callable): Method to parse each page/element
            chrome_options (dict): Chrome browser options
            pagination_config (dict): Pagination configuration
            **kwargs: Additional parameters passed to parse_method
            
        Returns:
            list: Scraped data
        """
        # Setup Chrome options
        options = Options()
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        
        # Apply custom chrome options
        if chrome_options:
            for option in chrome_options.get('arguments', []):
                options.add_argument(option)
            
            if chrome_options.get('prefs'):
                options.add_experimental_option("prefs", chrome_options['prefs'])
        
       
        try:
            
            self.logger.info(f"Starting Selenium scraping of {self.target_url}")
            
            # Navigate to target URL
            self.driver.get(self.target_url)
            page_number = 1
            
            # Wait for initial page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, scrape_element_id))
            )
            
            while True:
                self.logger.info(f"Scraping page {page_number}")
                
                # Wait for element to be present
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.ID, scrape_element_id))
                )
                
                # Parse current page
                soup = BeautifulSoup(self.driver.page_source, 'html.parser')
                page_data = parse_method(soup)
                
                if page_data:
                    self.scraped_data.extend(page_data)
                    self.logger.info(f"Found {len(page_data)} items on page {page_number}")
                
                # Handle pagination if configured
                if pagination_config:
                    if not self._handle_pagination(pagination_config, page_number):
                        break
                    page_number += 1
                else:
                    # No pagination, break after first page
                    break
                
                # Safety limit
                if page_number > pagination_config.get('max_pages', 100):
                    self.logger.warning(f"Reached maximum page limit: {pagination_config.get('max_pages', 100)}")
                    break
        
        except Exception as e:
            self.logger.error(f"Error during Selenium scraping: {e}")
            raise
        
        finally:
            if self.driver:
                self.driver.quit()
        
        return self.scraped_data
    
    def _handle_pagination(self, pagination_config, current_page):
        """
        Handle pagination logic
        
        Args:
            pagination_config (dict): Pagination configuration
            current_page (int): Current page number
            
        Returns:
            bool: True if successfully moved to next page, False otherwise
        """
        if not self.driver:
            self.logger.error("Driver is not initialized")
            return False
            
        try:
            next_button_config = pagination_config.get('next_button', {})
            button_selector = next_button_config.get('selector')
            button_id = next_button_config.get('id')
            
            # Find next button
            if button_id:
                next_button = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.ID, button_id))
                )
            elif button_selector:
                next_button = WebDriverWait(self.driver, 10).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, button_selector))
                )
            else:
                self.logger.error("No next button configuration provided")
                return False
            
            # Check if button is enabled
            if next_button.is_enabled() and not next_button.get_attribute("disabled"):
                # Store current content for change detection
                content_element_id = pagination_config.get('content_element_id')
                if content_element_id:
                    old_content = self.driver.find_element(By.ID, content_element_id).get_attribute('innerHTML')
                
                # Click next button
                next_button.click()
                
                # Wait for content change
                if content_element_id:
                    try:
                        WebDriverWait(self.driver, 10).until(
                            lambda d: d.find_element(By.ID, content_element_id).get_attribute('innerHTML') != old_content
                        )
                        self.logger.info("Page content changed successfully")
                    except TimeoutException:
                        self.logger.warning("Page content may not have changed after clicking next")
                
                # Optional delay between pages
                delay = pagination_config.get('page_delay', 1)
                time.sleep(delay)
                
                return True
            else:
                self.logger.info("Next button is disabled - reached end")
                return False
                
        except (TimeoutException, NoSuchElementException) as e:
            self.logger.info(f"No more pages or next button not found: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Error handling pagination: {e}")
            return False
    
    def scrape_with_requests(self, parse_method, urls=None, **kwargs):
        """
        Scrape using requests library
        
        Args:
            parse_method (callable): Method to parse the content
            urls (list): List of URLs to scrape (defaults to target_url)
            **kwargs: Additional parameters passed to parse_method
            
        Returns:
            list: Scraped data
        """
        if urls is None:
            urls = [self.target_url]
        
        for url in urls:
            try:
                response = self.get_page_content(url)
                if response:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    page_data = parse_method(soup, url=url, **kwargs)
                    
                    if page_data:
                        self.scraped_data.extend(page_data)
                        self.logger.info(f"Found {len(page_data)} items from {url}")
                        
            except Exception as e:
                self.logger.error(f"Error scraping {url}: {e}")
                continue
        
        return self.scraped_data
    
    def save_data(self, filename, format='json'):
        """
        Save scraped data to file
        
        Args:
            filename (str): Output filename
            format (str): Output format ('json', 'csv')
        """
        import json
        
        if format.lower() == 'json':
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.scraped_data, f, indent=2, ensure_ascii=False)
        elif format.lower() == 'csv':
            import pandas as pd
            df = pd.DataFrame(self.scraped_data)
            df.to_csv(filename, index=False)
        
        self.logger.info(f"Data saved to {filename}")
    
    def clear_data(self):
        """Clear scraped data"""
        self.scraped_data = []
    
    def get_data_count(self):
        """Get count of scraped items"""
        return len(self.scraped_data)
