#!/usr/bin/env python3
"""
Generic Scheduler Module
A flexible scheduler that can handle various scheduling patterns including:
- Daily at specific time
- Weekly on specific day at specific time  
- Monthly on specific day at specific time
- Yearly on specific date at specific time
- Custom intervals
"""

import sys
import os
import logging
import time
import schedule
from datetime import datetime, timedelta
from typing import Callable, Optional, Dict, Any, List, Union
import threading
from enum import Enum

class ScheduleFrequency(Enum):
    """Enumeration for different scheduling frequencies"""
    MINUTES = "minutes"
    HOURS = "hours" 
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    YEARLY = "yearly"

class ScheduledJob:
    """Class to represent a scheduled job with its configuration"""
    
    def __init__(self, 
                 job_name: str,
                 job_function: Callable,
                 frequency: ScheduleFrequency,
                 **kwargs):
        """
        Initialize a scheduled job
        
        Args:
            job_name (str): Name of the job for logging
            job_function (Callable): Function to execute
            frequency (ScheduleFrequency): How often to run the job
            **kwargs: Additional parameters based on frequency
                - For MINUTES/HOURS: interval (int)
                - For DAILY: time (str, default "00:00")
                - For WEEKLY: day (str), time (str, default "00:00") 
                - For MONTHLY: day (int), time (str, default "00:00")
                - For YEARLY: month (int), day (int), time (str, default "00:00")
        """
        self.job_name = job_name
        self.job_function = job_function
        self.frequency = frequency
        self.kwargs = kwargs
        self.job_instance: Optional[Any] = None
        
    def __str__(self):
        return f"ScheduledJob(name='{self.job_name}', frequency='{self.frequency.value}')"

class GenericScheduler:
    """
    A flexible scheduler that can handle multiple jobs with different scheduling patterns
    """
    
    def __init__(self, log_file: str = "scheduler.log"):
        """
        Initialize the scheduler
        
        Args:
            log_file (str): Path to the log file
        """
        self.jobs: List[ScheduledJob] = []
        self.running = False
        self.log_file = log_file
        self._setup_logging()
        
    def _setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def _wrap_job_function(self, scheduled_job: ScheduledJob):
        """
        Wrapper function to add logging and error handling to job functions
        
        Args:
            scheduled_job (ScheduledJob): The job to wrap
        """
        def wrapped_function():
            try:
                self.logger.info(f"Starting job: {scheduled_job.job_name}")
                start_time = datetime.now()
                
                # Execute the job function
                result = scheduled_job.job_function()
                
                end_time = datetime.now()
                duration = end_time - start_time
                self.logger.info(f"Job '{scheduled_job.job_name}' completed successfully in {duration}")
                return result
                
            except Exception as e:
                self.logger.error(f"Error in job '{scheduled_job.job_name}': {str(e)}")
                # Don't re-raise to keep scheduler running
                
        return wrapped_function
    
    def add_job(self, 
                job_name: str,
                job_function: Callable,
                frequency: ScheduleFrequency,
                **kwargs) -> ScheduledJob:
        """
        Add a job to the scheduler
        
        Args:
            job_name (str): Name of the job
            job_function (Callable): Function to execute
            frequency (ScheduleFrequency): Scheduling frequency
            **kwargs: Additional scheduling parameters
            
        Returns:
            ScheduledJob: The created job instance
            
        Examples:
            # Run every 30 minutes
            scheduler.add_job("my_job", my_function, ScheduleFrequency.MINUTES, interval=30)
            
            # Run daily at 2:30 PM
            scheduler.add_job("daily_job", my_function, ScheduleFrequency.DAILY, time="14:30")
            
            # Run every Sunday at 9:00 AM
            scheduler.add_job("weekly_job", my_function, ScheduleFrequency.WEEKLY, day="sunday", time="09:00")
            
            # Run on the 15th of every month at 10:00 AM
            scheduler.add_job("monthly_job", my_function, ScheduleFrequency.MONTHLY, day=15, time="10:00")
            
            # Run on March 1st every year at 8:00 AM
            scheduler.add_job("yearly_job", my_function, ScheduleFrequency.YEARLY, month=3, day=1, time="08:00")
        """
        scheduled_job = ScheduledJob(job_name, job_function, frequency, **kwargs)
        self._schedule_job(scheduled_job)
        self.jobs.append(scheduled_job)
        
        self.logger.info(f"Added job: {scheduled_job}")
        return scheduled_job
    
    def _schedule_job(self, scheduled_job: ScheduledJob):
        """
        Schedule a job based on its frequency and parameters
        
        Args:
            scheduled_job (ScheduledJob): The job to schedule
        """
        wrapped_function = self._wrap_job_function(scheduled_job)
        
        if scheduled_job.frequency == ScheduleFrequency.MINUTES:
            interval = scheduled_job.kwargs.get('interval', 1)
            job = schedule.every(interval).minutes.do(wrapped_function)
            
        elif scheduled_job.frequency == ScheduleFrequency.HOURS:
            interval = scheduled_job.kwargs.get('interval', 1)
            job = schedule.every(interval).hours.do(wrapped_function)
            
        elif scheduled_job.frequency == ScheduleFrequency.DAILY:
            time_str = scheduled_job.kwargs.get('time', '00:00')
            job = schedule.every().day.at(time_str).do(wrapped_function)
            
        elif scheduled_job.frequency == ScheduleFrequency.WEEKLY:
            day = scheduled_job.kwargs.get('day', 'monday').lower()
            time_str = scheduled_job.kwargs.get('time', '00:00')
            
            day_methods = {
                'monday': schedule.every().monday,
                'tuesday': schedule.every().tuesday,
                'wednesday': schedule.every().wednesday,
                'thursday': schedule.every().thursday,
                'friday': schedule.every().friday,
                'saturday': schedule.every().saturday,
                'sunday': schedule.every().sunday
            }
            
            if day not in day_methods:
                raise ValueError(f"Invalid day: {day}. Must be one of: {list(day_methods.keys())}")
                
            job = day_methods[day].at(time_str).do(wrapped_function)
            
        elif scheduled_job.frequency == ScheduleFrequency.MONTHLY:
            day = scheduled_job.kwargs.get('day', 1)
            time_str = scheduled_job.kwargs.get('time', '00:00')
            
            if not (1 <= day <= 31):
                raise ValueError(f"Invalid day: {day}. Must be between 1 and 31")
                
            # For monthly, we need to use a custom approach since schedule doesn't support it directly
            def monthly_job():
                now = datetime.now()
                if now.day == day:
                    hour, minute = map(int, time_str.split(':'))
                    if now.hour == hour and now.minute == minute:
                        wrapped_function()
                        
            job = schedule.every().day.at(time_str).do(monthly_job)
            
        elif scheduled_job.frequency == ScheduleFrequency.YEARLY:
            month = scheduled_job.kwargs.get('month', 1)
            day = scheduled_job.kwargs.get('day', 1)
            time_str = scheduled_job.kwargs.get('time', '00:00')
            
            if not (1 <= month <= 12):
                raise ValueError(f"Invalid month: {month}. Must be between 1 and 12")
            if not (1 <= day <= 31):
                raise ValueError(f"Invalid day: {day}. Must be between 1 and 31")
                
            # For yearly, we need a custom approach
            def yearly_job():
                now = datetime.now()
                if now.month == month and now.day == day:
                    hour, minute = map(int, time_str.split(':'))
                    if now.hour == hour and now.minute == minute:
                        wrapped_function()
                        
            job = schedule.every().day.at(time_str).do(yearly_job)
            
        else:
            raise ValueError(f"Unsupported frequency: {scheduled_job.frequency}")
            
        scheduled_job.job_instance = job
    
    def remove_job(self, job_name: str) -> bool:
        """
        Remove a job from the scheduler
        
        Args:
            job_name (str): Name of the job to remove
            
        Returns:
            bool: True if job was found and removed, False otherwise
        """
        for job in self.jobs:
            if job.job_name == job_name:
                if job.job_instance:
                    schedule.cancel_job(job.job_instance)
                self.jobs.remove(job)
                self.logger.info(f"Removed job: {job_name}")
                return True
        
        self.logger.warning(f"Job not found: {job_name}")
        return False
    
    def list_jobs(self) -> List[Dict[str, Any]]:
        """
        List all scheduled jobs
        
        Returns:
            List[Dict[str, Any]]: List of job information
        """
        job_list = []
        for job in self.jobs:
            job_info = {
                'name': job.job_name,
                'frequency': job.frequency.value,
                'parameters': job.kwargs,
                'next_run': schedule.jobs[self.jobs.index(job)].next_run if schedule.jobs else None
            }
            job_list.append(job_info)
        return job_list
    
    def run_job_now(self, job_name: str) -> bool:
        """
        Run a specific job immediately
        
        Args:
            job_name (str): Name of the job to run
            
        Returns:
            bool: True if job was found and executed, False otherwise
        """
        for job in self.jobs:
            if job.job_name == job_name:
                self.logger.info(f"Running job immediately: {job_name}")
                wrapped_function = self._wrap_job_function(job)
                wrapped_function()
                return True
                
        self.logger.warning(f"Job not found: {job_name}")
        return False
    
    def start(self, run_pending_immediately: bool = False):
        """
        Start the scheduler
        
        Args:
            run_pending_immediately (bool): Whether to run pending jobs immediately on start
        """
        if self.running:
            self.logger.warning("Scheduler is already running")
            return
            
        self.running = True
        self.logger.info("Starting scheduler...")
        self.logger.info(f"Scheduled jobs: {len(self.jobs)}")
        
        for job in self.jobs:
            self.logger.info(f"  - {job}")
            
        if run_pending_immediately:
            schedule.run_all()
            
        try:
            while self.running:
                schedule.run_pending()
                time.sleep(1)  # Check every second for better responsiveness
                
        except KeyboardInterrupt:
            self.logger.info("Scheduler stopped by user")
        except Exception as e:
            self.logger.error(f"Error in scheduler: {str(e)}")
        finally:
            self.running = False
    
    def stop(self):
        """Stop the scheduler"""
        self.running = False
        self.logger.info("Scheduler stopped")
    
    def clear_all_jobs(self):
        """Remove all scheduled jobs"""
        schedule.clear()
        self.jobs.clear()
        self.logger.info("All jobs cleared")

# Convenience functions for quick scheduling
def create_scheduler(log_file: str = "scheduler.log") -> GenericScheduler:
    """
    Create a new scheduler instance
    
    Args:
        log_file (str): Path to the log file
        
    Returns:
        GenericScheduler: New scheduler instance
    """
    return GenericScheduler(log_file)

def schedule_daily(scheduler: GenericScheduler, job_name: str, job_function: Callable, time: str = "00:00"):
    """Quick function to schedule a daily job"""
    return scheduler.add_job(job_name, job_function, ScheduleFrequency.DAILY, time=time)

def schedule_weekly(scheduler: GenericScheduler, job_name: str, job_function: Callable, day: str, time: str = "00:00"):
    """Quick function to schedule a weekly job"""
    return scheduler.add_job(job_name, job_function, ScheduleFrequency.WEEKLY, day=day, time=time)

def schedule_monthly(scheduler: GenericScheduler, job_name: str, job_function: Callable, day: int, time: str = "00:00"):
    """Quick function to schedule a monthly job"""
    return scheduler.add_job(job_name, job_function, ScheduleFrequency.MONTHLY, day=day, time=time)

def schedule_yearly(scheduler: GenericScheduler, job_name: str, job_function: Callable, month: int, day: int, time: str = "00:00"):
    """Quick function to schedule a yearly job"""
    return scheduler.add_job(job_name, job_function, ScheduleFrequency.YEARLY, month=month, day=day, time=time)

# Example usage and main function for testing
def example_function():
    """Example function for testing the scheduler"""
    print(f"Example job executed at {datetime.now()}")
    return "Job completed successfully"

def main():
    """Example usage of the scheduler"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Generic Scheduler')
    parser.add_argument('--demo', action='store_true', help='Run demo with example jobs')
    parser.add_argument('--log-file', default='scheduler.log', help='Log file path')
    
    args = parser.parse_args()
    
    if args.demo:
        # Create scheduler
        scheduler = create_scheduler(args.log_file)
        
        # Add some example jobs
        scheduler.add_job("every_minute", example_function, ScheduleFrequency.MINUTES, interval=1)
        scheduler.add_job("daily_morning", example_function, ScheduleFrequency.DAILY, time="09:00")
        scheduler.add_job("weekly_sunday", example_function, ScheduleFrequency.WEEKLY, day="sunday", time="10:00")
        scheduler.add_job("monthly_15th", example_function, ScheduleFrequency.MONTHLY, day=15, time="14:30")
        scheduler.add_job("yearly_new_year", example_function, ScheduleFrequency.YEARLY, month=1, day=1, time="00:00")
        
        # List all jobs
        print("\nScheduled Jobs:")
        for job in scheduler.list_jobs():
            print(f"  - {job}")
        
        # Start scheduler
        scheduler.start()
    else:
        print("Use --demo to run the scheduler demo")

if __name__ == "__main__":
    main()
