#!/usr/bin/env python3
"""
Dynamic Schedule Lock Service
Database-backed distributed locking mechanism to prevent multiple instances 
of scheduled tasks from running simultaneously.
Uses psycopg2 for direct PostgreSQL connectivity.
"""

from datetime import datetime
import logging
import psycopg2
from psycopg2.extras import Dict<PERSON>ursor
from typing import Optional, Dict, Any
from .dbConnection import DatabaseConnection

# Configure logging
logger = logging.getLogger(__name__)

class ScheduleLockService(DatabaseConnection):
    """
    Dynamic lock service for scheduled operations.
    Prevents multiple instances from running concurrently.
    Uses psycopg2 for direct database connectivity.
    """
    
    TABLE_NAME = "kontratar_main.lock"  # Use existing table
    
    def __init__(self, lock_id: str, lock_type: str, timeout_seconds: int = 3600):
        """
        Initialize the dynamic lock service
        
        Args:
            lock_id (str): Unique identifier for this lock
            lock_type (str): Type/category of the lock
            timeout_seconds (int): Timeout in seconds for stuck locks (default: 3600 = 1 hour)
        """
        self.lock_id = lock_id
        self.lock_type = lock_type
        self.timeout_seconds = timeout_seconds
        self.conn: Optional[psycopg2.extensions.connection] = None
        self.tenant_id = None
        
        # Setup database connection
        try:
            self.connect()
        except Exception as e:
            logger.error(f"Failed to initialize lock service for {lock_id}: {e}")
            raise
    
    
    def try_acquire_lock(self):
        """
        Attempt to acquire the lock
        
        Returns:
            bool: True if lock was acquired, False otherwise
        """
        if not self.conn:
            logger.error("Database connection not established")
            return False
            
        try:
            with self.conn.cursor(cursor_factory=DictCursor) as cur:
                current_time = datetime.utcnow()
                
                # Check if lock exists
                cur.execute(f"SELECT * FROM {self.TABLE_NAME} WHERE lock_id = %s", (self.lock_id,))
                lock = cur.fetchone()
                
                if not lock:
                    # No lock exists, create and acquire it
                    cur.execute(f"""
                        INSERT INTO {self.TABLE_NAME} (lock_id, is_processing, lock_acquired_at, type, tenant_id)
                        VALUES (%s, %s, %s, %s, %s)
                    """, (self.lock_id, True, current_time, self.lock_type, self.tenant_id))
                    self.conn.commit()
                    logger.info(f"Lock {self.lock_id} acquired for the first time by {self.tenant_id}")
                    return True
                
                # Check if lock is currently held
                if lock['is_processing']:
                    # Lock is already being processed, cannot acquire
                    logger.info(f"Lock {self.lock_id} is already being processed by {lock.get('tenant_id', 'unknown')}, cannot acquire")
                    return False
                
                # Lock exists but not currently processing, acquire it
                cur.execute(f"""
                    UPDATE {self.TABLE_NAME} 
                    SET is_processing = %s, lock_acquired_at = %s, tenant_id = %s
                    WHERE lock_id = %s
                """, (True, current_time, self.tenant_id, self.lock_id))
                self.conn.commit()
                logger.info(f"Lock {self.lock_id} acquired by {self.tenant_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error trying to acquire lock {self.lock_id}: {e}")
            if self.conn:
                self.conn.rollback()
            return False
    
    def release_lock(self):
        """Release the lock"""
        if not self.conn:
            logger.error("Database connection not established")
            return
            
        try:
            with self.conn.cursor(cursor_factory=DictCursor) as cur:
                # Get current lock info for logging
                cur.execute(f"SELECT tenant_id FROM {self.TABLE_NAME} WHERE lock_id = %s", (self.lock_id,))
                lock = cur.fetchone()
                
                # Update lock to released state
                cur.execute(f"""
                    UPDATE {self.TABLE_NAME} 
                    SET is_processing = %s, lock_acquired_at = %s 
                    WHERE lock_id = %s
                """, (False, datetime.utcnow(), self.lock_id))
                
                if cur.rowcount > 0:
                    self.conn.commit()
                    if lock:
                        logger.info(f"Lock {self.lock_id} released by {lock['tenant_id']}")
                    else:
                        logger.info(f"Lock {self.lock_id} released")
                else:
                    logger.warning(f"Attempted to release lock {self.lock_id} but no lock found")
                    
        except Exception as e:
            logger.error(f"Error releasing lock {self.lock_id}: {e}")
            if self.conn:
                self.conn.rollback()
    
   


