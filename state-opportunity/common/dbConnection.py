import psycopg2
from psycopg2.extras import DictCursor
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('maryland_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseConnection:
    def __init__(self):
        self.conn = None
        self.connect()

    def connect(self):
        """Establish database connection"""
        try:
            self.conn = psycopg2.connect(
                dbname="postgres",
                user="alpha",
                password="5t3r2i66123",
                host="govcon-1.cwjbmcfdk9qo.us-east-1.rds.amazonaws.com",
                port="5432",
                sslmode="verify-ca",
                sslrootcert="us-east-1-bundle.pem"
            )
            logger.info("Successfully connected to the database")
        except Exception as e:
            logger.error(f"Error connecting to database: {str(e)}")
            raise
    def close(self):
        """Close database connection"""
        if self.conn:
            self.conn.close()
            logger.info("Database connection closed")
    def get_connection(self):
        """Get database connection"""
        return self.conn
