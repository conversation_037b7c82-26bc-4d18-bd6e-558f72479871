#!/bin/bash

# Set the working directory
cd "$(dirname "$0")"

# Set up logging
LOG_FILE="state-opportunity.logs"
TIMESTAMP=$(date +"%Y-%m-%d %H:%M:%S")

pip install -r requirements.txt

# Default run mode
RUN_MODE="scheduled"
CUSTOM_TIME=""

# Parse command-line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --time) CUSTOM_TIME="$2"; shift ;;
        --run-now) RUN_MODE="immediate" ;;
        *) echo "Unknown parameter passed: $1"; exit 1 ;;
    esac
    shift
done

# Log start of execution
echo "$TIMESTAMP - Starting state opportunity scraper" >> "$LOG_FILE"

# Run the scheduler based on mode
if [ "$RUN_MODE" == "immediate" ]; then
    # Run immediately for testing
    nohup python3 service/run_schedules.py --run-now &
    echo "$TIMESTAMP - State opportunity scraper started in immediate mode" >> "$LOG_FILE"
elif [ -n "$CUSTOM_TIME" ]; then
    # Run with custom time
    nohup python3 service/run_schedules.py --time "$CUSTOM_TIME" &
    echo "$TIMESTAMP - State opportunity scraper started with custom time: $CUSTOM_TIME" >> "$LOG_FILE"
else
    # Default scheduled run
    nohup python3 service/run_schedules.py &
    echo "$TIMESTAMP - State opportunity scraper started in scheduled mode" >> "$LOG_FILE"
fi

# Get the process ID
PID=$!

# Log the process ID
echo "$TIMESTAMP - State opportunity scraper started with PID: $PID" >> "$LOG_FILE"

# Log completion
TIMESTAMP=$(date +"%Y-%m-%d %H:%M:%S")
echo "$TIMESTAMP - State opportunity scraper process started in background" >> "$LOG_FILE"

# Examples of usage:
# ./start.sh                     # Run in default scheduled mode
# ./start.sh --run-now           # Run immediately
# ./start.sh --time "14:30"      # Run at a custom time