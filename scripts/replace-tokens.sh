#!/bin/bash

# Enhanced Token Replacement Script for Python Services
# This script runs the enhanced Python token replacement utility to generate .env files for services

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"  # Parent directory (project root)
TOKEN_REPLACEMENT_DIR="${ROOT_DIR}/PythonTokenReplacement/src"
CENTRAL_ENV_INI="${ROOT_DIR}/properties/.env.ini"

# Define the services to look for - ADD NEW SERVICES HERE
SUPPORTED_SERVICES=(
    "AIService"
    # "GSAEPythonLibrary"
    # "ForecastDataFeed"
    # "state-opportunity"
    # Add more services here as needed
)

# Global variables for command line options
ENV_OVERRIDES=()
VERBOSE=false
NO_BACKUP=false

# Function to show usage
show_usage() {
    echo "Usage: $0 [SERVICE_NAME|all] [OPTIONS]"
    echo ""
    echo "SERVICE_NAME: Name of the service directory (e.g., AIService, ForecastDataFeed)"
    echo "all:          Process all services that have .env.template files"
    echo ""
    echo "OPTIONS:"
    echo "  -E name=value    Set environment variable override (can be used multiple times)"
    echo "  -v               Enable verbose output"
    echo "  --no-backup      Skip creating backups of existing .env files"
    echo ""
    echo "If no argument is provided, it will show available services."
    echo ""
    echo "This script uses a centralized configuration file at: $CENTRAL_ENV_INI"
    echo "Each service needs only a .env.template file in its directory."
    echo ""
    echo "Template files support:"
    echo "  - &token; syntax for variable substitution"
    echo "  - #if VARIABLE=value ... #endif conditional blocks"
    echo "  - #else clauses within conditional blocks"
    echo "  - Automatic line commenting for missing tokens"
    echo ""
    echo "Examples:"
    echo "  $0 AIService                          # Process only AIService"
    echo "  $0 all                                # Process all discovered services"
    echo "  $0 AIService -E ENVIRONMENT=production # Process with environment override"
    echo "  $0 all -v --no-backup                 # Process all with verbose, no backups"
    echo "  $0 all -E OS=Linux -E ENVIRONMENT=dev # Multiple environment overrides"
    echo "  $0                                    # Show available services"
}

# Parse command line options
parse_options() {
    ENV_OVERRIDES=()
    VERBOSE=false
    NO_BACKUP=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -E)
                if [[ -z "$2" || "$2" == -* ]]; then
                    echo "Error: -E requires a value in format name=value"
                    exit 1
                fi
                ENV_OVERRIDES+=("$2")
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --no-backup)
                NO_BACKUP=true
                shift
                ;;
            -*) 
                echo "Unknown option: $1"
                show_usage
                exit 1
                ;;
            *)
                # This is the service name, stop parsing options
                break
                ;;
        esac
    done
    
    # Return remaining arguments
    echo "$@"
}

# Function to check if a service has required files
check_service_files() {
    local service_dir="$1"
    local env_template="${service_dir}/.env.template"
    
    # Only check for .env.template since we use centralized .env.ini
    if [ -f "$env_template" ]; then
        return 0  # Template file exists
    else
        return 1  # Missing template file
    fi
}

# Function to discover available services
discover_services() {
    local services=()
    
    # Look through the predefined supported services list
    for service_name in "${SUPPORTED_SERVICES[@]}"; do
        local service_dir="${ROOT_DIR}/${service_name}"
        
        # Check if directory exists and has required files
        if [ -d "$service_dir" ] && check_service_files "$service_dir"; then
            services+=("$service_name")
        fi
    done
    
    echo "${services[@]}"
}

# Function to build command line arguments for Python utility
build_python_args() {
    local service_name="$1"
    local template_file="$2"
    local output_file="$3"
    
    local args=()
    args+=("-c" "$CENTRAL_ENV_INI")
    args+=("-t" "$template_file")
    args+=("-o" "$output_file")
    args+=("-s" "$service_name")
    
    # Add environment overrides
    for override in "${ENV_OVERRIDES[@]}"; do
        args+=("-E" "$override")
    done
    
    # Add verbose flag
    if [ "$VERBOSE" = true ]; then
        args+=("-v")
    fi
    
    # Add no-backup flag
    if [ "$NO_BACKUP" = true ]; then
        args+=("--no-backup")
    fi
    
    echo "${args[@]}"
}

# Function to process a single service
process_service() {
    local service_name="$1"
    local service_dir="${ROOT_DIR}/${service_name}"
    
    # Input files - using centralized .env.ini
    local env_template_file="${service_dir}/.env.template"
    local env_output_file="${service_dir}/.env"
    
    echo "=============================================="
    echo "Processing Service: $service_name"
    echo "=============================================="
    echo "Central config file: $CENTRAL_ENV_INI"
    echo "Service directory: $service_dir"
    
    if [ ${#ENV_OVERRIDES[@]} -gt 0 ]; then
        echo "Environment overrides: ${ENV_OVERRIDES[*]}"
    fi
    echo ""
    
    # Check if centralized config file exists
    if [ ! -f "$CENTRAL_ENV_INI" ]; then
        echo "Error: Central configuration file not found: $CENTRAL_ENV_INI"
        return 1
    fi

    # Check if template file exists
    if [ ! -f "$env_template_file" ]; then
        echo "Error: Template file not found: $env_template_file"
        return 1
    fi

    # Build Python command arguments
    local python_args
    read -ra python_args <<< "$(build_python_args "$service_name" "$env_template_file" "$env_output_file")"
    
    # Run token replacement
    echo "Running enhanced token replacement utility..."
    echo "Template file: $env_template_file"
    echo "Output file: $env_output_file"
    
    if [ "$VERBOSE" = true ]; then
        echo "Command: python3 main.py ${python_args[*]}"
    fi
    echo ""

    cd "$TOKEN_REPLACEMENT_DIR"
    if python3 main.py "${python_args[@]}"; then
        echo ""
        echo "✅ Token replacement completed successfully for $service_name!"
        echo "New .env file generated at: $env_output_file"
        
        # Check if backup was created
        local backup_dir="${service_dir}/backup"
        if [ -d "$backup_dir" ] && [ "$NO_BACKUP" != true ]; then
            local latest_backup
            latest_backup=$(find "$backup_dir" -name ".env-*" -type f | sort | tail -1 2>/dev/null || echo "")
            if [ -n "$latest_backup" ]; then
                echo "Previous .env file backed up to: $latest_backup"
            fi
        fi
        return 0
    else
        echo "❌ Token replacement failed for $service_name"
        return 1
    fi
}

# Main script logic
main() {
    # Parse command line options
    local remaining_args
    read -ra remaining_args <<< "$(parse_options "$@")"
    local target_service="${remaining_args[0]}"
    
    # Discover available services
    local available_services=($(discover_services))
    
    if [ ${#available_services[@]} -eq 0 ]; then
        echo "❌ No services found with .env.template files."
        echo ""
        echo "To set up a service for token replacement, ensure it has:"
        echo "  - .env.template file (template with &token; placeholders)"
        echo ""
        echo "The centralized configuration file will be used from:"
        echo "  - $CENTRAL_ENV_INI"
        echo ""
        echo "Template files support:"
        echo "  - &token; syntax for variable substitution"
        echo "  - #if VARIABLE=value ... #endif conditional blocks"
        echo "  - #else clauses within conditional blocks"
        exit 1
    fi
    
    # If no argument provided, show available services
    if [ -z "$target_service" ]; then
        echo "=============================================="
        echo "Available Services for Token Replacement"
        echo "=============================================="
        echo "Found ${#available_services[@]} service(s) with .env.template files:"
        echo ""
        for service in "${available_services[@]}"; do
            local template_file="${ROOT_DIR}/${service}/.env.template"
            local has_conditionals=""
            if grep -q "#if\\|#else\\|#endif" "$template_file" 2>/dev/null; then
                has_conditionals=" (with conditionals)"
            fi
            echo "  📁 $service$has_conditionals"
        done
        echo ""
        echo "Central configuration: $CENTRAL_ENV_INI"
        echo ""
        show_usage
        exit 0
    fi
    
    # Handle "all" parameter
    if [ "$target_service" = "all" ]; then
        echo "=============================================="
        echo "Processing ALL Services"
        echo "=============================================="
        echo "Found ${#available_services[@]} service(s): ${available_services[*]}"
        if [ ${#ENV_OVERRIDES[@]} -gt 0 ]; then
            echo "Environment overrides: ${ENV_OVERRIDES[*]}"
        fi
        echo ""
        
        local success_count=0
        local total_count=${#available_services[@]}
        
        for service in "${available_services[@]}"; do
            if process_service "$service"; then
                ((success_count++))
            fi
            echo ""
        done
        
        echo "=============================================="
        echo "Summary: $success_count/$total_count services processed successfully"
        if [ ${#ENV_OVERRIDES[@]} -gt 0 ]; then
            echo "Environment overrides applied: ${ENV_OVERRIDES[*]}"
        fi
        echo "=============================================="
        
        if [ $success_count -eq $total_count ]; then
            exit 0
        else
            exit 1
        fi
    fi
    
    # Check if specified service exists and is valid
    local service_found=false
    for service in "${available_services[@]}"; do
        if [ "$service" = "$target_service" ]; then
            service_found=true
            break
        fi
    done
    
    if [ "$service_found" = false ]; then
        echo "❌ Service '$target_service' not found or missing required files."
        echo ""
        echo "Available services:"
        for service in "${available_services[@]}"; do
            echo "  📁 $service"
        done
        echo ""
        show_usage
        exit 1
    fi
    
    # Process the specified service
    if process_service "$target_service"; then
        echo "🎉 All done! You can now run your $target_service with the updated configuration."
    else
        exit 1
    fi
}

# Run main function with all arguments
main "$@"