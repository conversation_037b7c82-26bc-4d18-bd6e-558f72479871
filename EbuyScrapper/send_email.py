from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys 
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import time
import json
import os 
import pandas as pd 
from selenium.common.exceptions import NoSuchElementException 
from datetime import datetime
import pytz 

# loading variables from .env file
from dotenv import load_dotenv
load_dotenv() 

# Specify the download path and ensure to change it in the arrange.py file
download_path = os.getenv("DOWNLOAD_FOLDER")
chrome_options = webdriver.ChromeOptions()
prefs = {'download.default_directory' : download_path, 'profile.default_content_setting_values.automatic_downloads': 1}
chrome_options.add_experimental_option('prefs', prefs)
driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

driver.get("http://mail.adeptengr.com/interface/root#/login")  # Replace with the desired URL
driver.fullscreen_window()  # Set the browser to full screen

email_mail = os.getenv("EMAIL_MAIL")
password_mail = os.getenv("PASSWORD_MAIL")

# Get current time to make sure it happens before email comes around
central = pytz.timezone('US/Eastern')
current_time_central = datetime.now(central)
print("Current time in Eastern time (ET):", current_time_central)

WebDriverWait(driver, 30).until(
     EC.element_to_be_clickable((By.ID, "loginUsernameBox"))
)
email_mail_input = driver.find_element(By.ID, "loginUsernameBox")
email_mail_input.clear()
email_mail_input.send_keys(email_mail)

#Input Password 
pass_mail_input = driver.find_element(By.ID, "loginPasswordBox")
pass_mail_input.clear()
pass_mail_input.send_keys(password_mail)


     # Checkbox for remember me
remember_button = driver.find_element(By.XPATH, "//st-toggle-switch[@translation-key='REMEMBER_ME']")
remember_button.click()

# Submit button
submit_button = driver.find_element(By.XPATH, "//button[@type='submit']")
submit_button.click()

# header_date = datetime.strptime(header_date_text, "%m/%d/%y %I:%M %p")

while True:
     # Click on the first email
     try:
          first_email = WebDriverWait(driver, 15).until(
          EC.visibility_of_element_located((By.XPATH, ".//div[@data-index='0']"))
          )
     except:
          time.sleep(30)
          print("No email found, going around")
          continue
     
     is_read_element = first_email.find_element(By.XPATH, ".//div[@class='mailGridItem  ']")
     is_read = is_read_element.get_attribute("read") == "true"
     print("Is read", is_read)
     
     first_email.click()
     print("Email clicked")
     
     header_date_element = WebDriverWait(driver, 5).until(
          EC.visibility_of_element_located((By.CLASS_NAME, "header-date"))
     )
     header_date_text = header_date_element.text
     header_date = central.localize(datetime.strptime(header_date_text, "%m/%d/%y %I:%M %p"))
     print(header_date_text)
     
     time.sleep(1)
     code=""

     if (header_date >= current_time_central) and not is_read:
          # Since code is in an iframe, switch to iframe context to access it
          driver.switch_to.frame("messageViewFrame") 
          verification_code = WebDriverWait(driver, 10).until(
                    EC.visibility_of_element_located((By.ID, "verification-code"))
               )
          code = verification_code.text
          driver.switch_to.default_content()  # Switch back to the original tab
          break
     else:
          print("Code not detected, going around")
          time.sleep(30)


time.sleep(10)  # Wait for 20 seconds

driver.close()  # Close the new tab