# Web Scraper for eBuy using Python

This project is a web scraper designed to extract information from the eBuy platform using Python. The main functionality is implemented in the `main.py` file.

## Getting Started

To get started with this project, follow the steps below:

1. **Install Requirements**: 
   Download the required packages by running the following command in your terminal:
   ```
   pip install -r requirements.txt
   ```

2. **Install Chrome for Testing browser**
   Version is 131.0.6778.85
   ```
   wget https://storage.googleapis.com/chrome-for-testing-public/131.0.6778.85/linux64/chrome-linux64.zip
   unzip chrome-linux.zip -d /apps/AES/eBuyScrapper
   echo "Chrome for Testing extracted to: /apps/AES/eBuyScrapper/chrome-linux"
   ```
  
3. **Set Environment Variables**:
   You need to set the following environment variables for the scraper to work:
   - `EMAIL_USER`: Your email address used for logging into eBuy.
   - `PASSWORD_USER`: Your password for the eBuy account.
   - `DOWNLOAD_FOLDER`: The folder path where you want the downloaded files to be saved. e.g DOWNLOAD_FOLDER="/Users/<USER>/Downloads/gov-scrape/"
   - `EMAIL_MAIL`: The email address used for logging into adeptengr mail service.
   - `PASSWORD_MAIL`: The password used for logging into adeptengr mail service.
   - `USE_DATE`: This field controls if the script gets RFQs for the current date, it can either be `true` or `false` is small letters.
   - `CHROME_BROWSER`: The path of the installed chrome browser

   You can set these variables in your terminal or by creating a `.env` file in the project directory with the following format:
   ```
   EMAIL_USER=<EMAIL>
   PASSWORD_USER=your_password
   DOWNLOAD_FOLDER=/path/to/download/folder
   RFQ_FOLDER=/path/to/RFQ/folder
   TABLE_FOLDER=/path/to/table/folder
   CHROME_BROWSER=/path/to/chrome/browser
   ```

4. **Run the Scraper**:
   After setting up the environment variables, you can run the scraper by executing:
   ```
   python main.py
   ```

   The scraper will scrape RFQs based on the `withDate` parameter. If `withDate` is set to `True`, it will scrape RFQs that are available for the current date. If set to `False`, it will scrape all available RFQs, regardless of the date.

This will start the web scraping process, and the data will be saved in the specified download folder.

## <b>Very Important Note</b>
For this to work, the Chrome browser must be installed on the computer, with a version of at least `130.0.6723.116`.

The `cookies.json` file is used to store cookies for the eBuy URL. This file is automatically generated during the login process to maintain the session for subsequent requests. 

The `rfq_full_table.csv` file will contain all the scraped data from the eBuy platform, including details such as RFQ ID, title, description, reference ID, issue date, close date, delivery information, contact information, shipping addresses, and attachments.

## Organizing Downloaded Files
To run the `arrange.py` script, use the following command in your terminal:
   ```
   python arrange.py
   ```