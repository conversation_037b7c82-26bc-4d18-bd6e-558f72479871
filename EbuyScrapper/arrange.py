import os
import pandas as pd
import ast

# Load the csv file of all RFQs
rfq_path = os.getenv("TABLE_FOLDER")
df = pd.read_csv(os.path.join(rfq_path, 'rfq_full_table.csv'))

# loading variables from .env file
from dotenv import load_dotenv
load_dotenv() 

# This path must correspod to the path specified as the download path in the main.py file
download_path = os.getenv("DOWNLOAD_FOLDER")
      
# Main app loop startes here:
if __name__ == "__main__":   
    for index, row in df.iterrows():
        attachements = row['attachements']
        rfq = row['rfq_id']
        attachements_list = ast.literal_eval(attachements)
        for attach in attachements_list:
            destination_folder = os.path.join(os.getenv("RFQ_FOLDER"), f'{rfq}')
            for similar_file in os.listdir(download_path):
                if attach.split()[0] in similar_file:
                    if not os.path.exists(destination_folder):
                        os.makedirs(destination_folder)
                    os.rename(os.path.join(download_path, similar_file), os.path.join(destination_folder, attach))
                    print(f'Moved file: {attach} to {destination_folder}')




