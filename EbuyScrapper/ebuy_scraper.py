from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys 
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import time
import json
import os 
import pandas as pd 
from selenium.common.exceptions import NoSuchElementException 
from datetime import datetime
import pytz 
import logging

from db.db import DatabaseConnection
from db.models import EbuyDocument
from db.models import EbuyOpportunity

# loading variables from .env file
from dotenv import load_dotenv
load_dotenv() 

# Configure logging
log_filename = 'EbuyScraper.logs'
# Remove existing log file if it exists
if os.path.exists(log_filename):
    os.remove(log_filename)

# Configure logging to write to file
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()  # This will keep console output as well
    ]
)
logger = logging.getLogger(__name__)



# Specify the download path and ensure to change it in the arrange.py file
download_path = os.getenv("DOWNLOAD_FOLDER")
rfq_folder = os.getenv("RFQ_FOLDER")
rfq_table = os.getenv("TABLE_FOLDER")

chrome_options = webdriver.ChromeOptions()
prefs = {'download.default_directory' : download_path, 'profile.default_content_setting_values.automatic_downloads': 1}
chrome_options.add_experimental_option('prefs', prefs)
# chrome_options.add_argument('--headless')
chrome_options.add_argument('--no-sandbox')
chrome_options.add_argument('--disable-dev-shm-usage')
chrome_browser = os.getenv("CHROME_BROWSER")
if not chrome_browser:
    raise EnvironmentError("CHROME_BROWSER environment variable is not set. Please add the path to the Chrome Testing app in the .env file.")
chrome_options.binary_location = chrome_browser


driver = webdriver.Chrome(options=chrome_options
                          )
  
def log_data(message):
     logger.info(message)
             
def loginFlow(driver, logged_in=False):
     log_data("Started Login Flow Process...")
     db = DatabaseConnection()
     email = db.get_ebuy_email()
     password = db.get_ebuy_passsword()

     # 3. Input email address okta-signin-username
     WebDriverWait(driver, 5).until(
          EC.element_to_be_clickable((By.ID, "okta-signin-username"))
          )
     email_input = driver.find_element(By.ID, "okta-signin-username")
     email_input.clear()
     email_input.send_keys(email)

     # 4. Input Password okta-signin-password
     pass_input = driver.find_element(By.ID, "okta-signin-password")
     pass_input.clear()
     pass_input.send_keys(password)

     # Checkbox for remember me
     # remember_button = driver.find_element(By.XPATH, "//label[@for='input55']")
     # remember_button.click()

     # 5. Press the submit button okta-signin-submit
     submit_credentials = driver.find_element(By.ID, "okta-signin-submit")
     submit_credentials.click()

     # 6. Press, send verification email - button button-primary
     verification_button = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.CLASS_NAME, "button-primary")))
     verification_button.click()
     
     time.sleep(1)
     
     code = get_verfication_code(driver, logged_in)
     if len(code) <= 0:
          message = "Code is empty"
          log_data(message)
     else:
           log_data(f"Verfication code:, {code}")
     # query = input("Enter the verification code:") #This is a test, remove this later

     # 7. Input verification code
     verification_input = WebDriverWait(driver, 5).until(EC.element_to_be_clickable((By.ID, "input93")))
     verification_input.clear()
     verification_input.send_keys(code)

     # 8. Click input button of do not challenge me for the next 12 hours, use className
     verification_button = driver.find_element(By.CLASS_NAME, "button-primary")
     verification_button.click()

     # 9. Proceed to active rfqs immediately
     time.sleep(20)
     driver.get("https://www.ebuy.gsa.gov/ebuy/seller/active-rfqs")

def get_verfication_code(driver, logged_in=False):
     email_mail = os.getenv("EMAIL_MAIL")
     password_mail = os.getenv("PASSWORD_MAIL")
     
     # Get current time to make sure it happens before email comes around
     central = pytz.timezone('US/Eastern')
     current_time_central = datetime.now(central)
     log_data(f"Current time in Eastern time (ET):, {current_time_central}")
     
     driver.execute_script("window.open('');")  # Open a new tab
     driver.switch_to.window(driver.window_handles[1])  # Switch to the new tab
     driver.get("http://mail.adeptengr.com/interface/root#/login")  # Replace with the desired URL

     if not logged_in:
          WebDriverWait(driver, 30).until(
               EC.element_to_be_clickable((By.ID, "loginUsernameBox"))
          )
          email_mail_input = driver.find_element(By.ID, "loginUsernameBox")
          email_mail_input.clear()
          email_mail_input.send_keys(email_mail)
          
          #Input Password 
          pass_mail_input = driver.find_element(By.ID, "loginPasswordBox")
          pass_mail_input.clear()
          pass_mail_input.send_keys(password_mail)
          
          
          # Checkbox for remember me
          # remember_button = driver.find_element(By.XPATH, "//st-toggle-switch[@translation-key='REMEMBER_ME']")
          # remember_button.click()
          
          # Submit button
          submit_button = driver.find_element(By.XPATH, "//button[@type='submit']")
          submit_button.click()
     
     # header_date = datetime.strptime(header_date_text, "%m/%d/%y %I:%M %p")
     code=""
     
     while True:
          # Click on the first email
          try:
               first_email = WebDriverWait(driver, 15).until(
               EC.visibility_of_element_located((By.XPATH, ".//div[@data-index='0']"))
               )
          except:
               time.sleep(20)
               log_data("No email found, going around")
               continue
          
          is_read_element = first_email.find_element(By.XPATH, ".//div[@class='mailGridItem  ']")
          is_read = is_read_element.get_attribute("read") == "true"
          log_data(f"Is read, {is_read}")
          
          first_email.click()
          log_data("Email clicked")
          
          time.sleep(5)
          
          header_date_element = WebDriverWait(driver, 10).until(
               EC.visibility_of_element_located((By.CLASS_NAME, "header-date"))
          )
          header_date_text = header_date_element.text
          header_date = central.localize(datetime.strptime(header_date_text, "%m/%d/%y %I:%M %p"))
          
          
          if (header_date >= current_time_central) and not is_read:
               # Since code is in an iframe, switch to iframe context to access it
               driver.switch_to.frame("messageViewFrame") 
               verification_code = WebDriverWait(driver, 10).until(
                         EC.visibility_of_element_located((By.ID, "verification-code"))
                    )
               code = verification_code.text
               driver.switch_to.default_content()  # Switch back to the original tab
               break
          else:
               log_data("Code not detected, going around")
               driver.refresh()
               time.sleep(20)
     
     driver.close()  # Close the new tab opened
     driver.switch_to.window(driver.window_handles[0])  # Switch back to the original tab
     return code

# Extract all cookies here
def saveCookies(driver):
     cookies=driver.get_cookies()
     with open('cookies.json', 'w') as file:
          json.dump(cookies, file)
          
def loadCookies():
     if 'cookies.json' in os.listdir():
          with open('cookies.json', 'r') as file:
               cookies = json.load(file)
               
          for cookie in cookies:
               driver.add_cookie(cookie)
     else:
          log_data('No cookies file found')
     
     driver.refresh()
     

def download_mods(rfq_id, mod_column, row):
     attachements =[]
     if mod_column.text != "":
          get_mods = row.find_elements(By.XPATH, ".//a[@ngbtooltip='View Modification Details']")
          for mod in get_mods:
               mod.click()
               time.sleep(2)
               dialog = WebDriverWait(driver, 5).until(EC.element_to_be_clickable((By.CLASS_NAME, "modal-dialog")))
               text = dialog.find_element(By.CLASS_NAME, "modal-body")

               os.makedirs(f"{rfq_folder}/{rfq_id}", exist_ok=True)
               filename = mod.text.replace('\n', '_').replace('/', '-')
               with open(f"{rfq_folder}/{rfq_id}/{filename}.txt", "w+", encoding="utf-8") as file:
                    file.write(text.text)
                    
               attachements.append(f"{filename}.txt")
               close_button = dialog.find_element(By.XPATH, ".//button[@aria-label='Close']")
               close_button.click()
               time.sleep(2)
     return attachements


def download_attachements(rfq_id, qa_column, row):
     attachements =[]
     if qa_column.text != "":
          get_qas = row.find_elements(By.XPATH, ".//a[@ngbtooltip='View the Q&A details']")
          for qas in get_qas:
               # Wait for the modal to close if it is open
               WebDriverWait(driver, 10).until(EC.invisibility_of_element_located((By.CLASS_NAME, "modal-content")))
               
               # Option 1: Click using JavaScript
               driver.execute_script("arguments[0].click();", qas)

               time.sleep(2)
               dialog = WebDriverWait(driver, 5).until(EC.element_to_be_clickable((By.CLASS_NAME, "modal-dialog")))
               qa_links = dialog.find_elements(By.TAG_NAME, "a")
               for qa_link in qa_links:
                    ActionChains(driver).move_to_element(qa_link).click(qa_link).perform()
                    attachements.append(qa_link.text)
                    
               move_attachments_to_folder(attachements, rfq_id, 4)
               close_button = dialog.find_element(By.XPATH, ".//button[@aria-label='Close']")
               close_button.click()
     return attachements

# Function to scrape the table
def scrapeTable(driver, withDate):
     WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.TAG_NAME, "table")))

     # try:
     table = driver.find_element(By.TAG_NAME, "table")
          
     rows = table.find_elements(By.TAG_NAME, "tr")
     href = []
     
     # today_date = datetime.now().strftime("%m/%d/%Y")
     today_date = "08/05/2025"
     
     c = 1 #Count to keep track of file
     
     if withDate: #If you want to scrape with date
          for row in rows:
               WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.TAG_NAME, "td")))
               cells = row.find_elements(By.TAG_NAME, "td") 

               if len(cells) == 0:
                    continue
               
                # Get posted date text  
               date_text = cells[4].text 
               
               # Check mod column
               mod_column = cells[7]
               
               # Check for date posted and date a mod was added
               if (today_date in date_text) or (today_date in mod_column.text):
                    # Get RFQ ID
                    try:
                         rfq_id = cells[2].text
                    except:
                         rfq_id = " "
                    
                    # Get Category details from the table
                    try:
                         category = cells[1].text
                    except:
                         category = " "
                         
                    # Get Buyer details from the table
                    try:
                         buyer = cells[6].text
                    except:
                         buyer = " "
                         
                    attachements = []
                    
                    # Check if it has been modified, and download it as a text file, returns an array of all attachements
                    mods_attachment = download_mods(rfq_id, mod_column, row)
                    
                    # Update attachments list with mods attachements
                    attachements = attachements + mods_attachment
                    
                    
                    qa_column = cells[8]
                    qa_attachments = download_attachements(rfq_id, qa_column, row)
                    attachements = attachements + qa_attachments
                                   
               
                    # Get the URLS for the sraping
                    a_tags = row.find_elements(By.TAG_NAME, "a")
                    for a_tag in a_tags:
                         if a_tag.get_attribute('href') != None:
                              if '/ebuy/seller/prepare-quote' in a_tag.get_attribute('href'):
                                   href.append({
                                        'category': category,
                                        'buyer': buyer,
                                        'text': a_tag.text,
                                        'href': a_tag.get_attribute('href'),
                                        'attachements': attachements
                                   })
                                   
                    log_data(f"{c} out of {len(rows)}: {rfq_id} URL scraped")
                    c +=1
               
     else: #Else scrape everything
          central = pytz.timezone('US/Eastern')
          current_time_central = datetime.now(central)
          for row in rows:
               WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.TAG_NAME, "td")))
               cells = row.find_elements(By.TAG_NAME, "td") 

               if len(cells) == 0:
                    continue
               
                # Get posted date text  
               date_text = cells[4].text 
               
               # Check mod column
               mod_column = cells[7]
               
               # Get close date
               close_date = cells[5].text
               
               try:
                    if close_date:  # Check if close_date is not empty
                         close_date = close_date.replace(" EST", "")  
                         close_date = central.localize(datetime.strptime(close_date, "%m/%d/%Y %I:%M %p"))
                    else:
                         close_date = None  # Handle the case where close_date is empty
               except:
                    close_date = None  # Handle the case where close_date is empty

               if close_date == None or (current_time_central < close_date):
                    # Get RFQ ID
                    try:
                         rfq_id = cells[2].text
                    except:
                         rfq_id = " "
                    
                    # Get Category details from the table
                    try:
                         category = cells[1].text
                    except:
                         category = " "
                         
                    # Get Buyer details from the table
                    try:
                         buyer = cells[6].text
                    except:
                         buyer = " "
                         
                    attachements = []
                    
                    # Check if it has been modified, and download it as a text file, returns an array of all attachements
                    mods_attachment = download_mods(rfq_id, mod_column, row)
                    
                    # Update attachments list with mods attachements
                    attachements = attachements + mods_attachment
                    
                    # Download all files from MOD List
                    qa_column = cells[8]
                    qa_attachments = download_attachements(rfq_id, qa_column, row)
                    attachements = attachements + qa_attachments
                    
                    a_tags = row.find_elements(By.TAG_NAME, "a")
                    for a_tag in a_tags:
                         if a_tag.get_attribute('href') != None:
                              if '/ebuy/seller/prepare-quote' in a_tag.get_attribute('href'):
                                   href.append({
                                        'category': category,
                                        'buyer': buyer,
                                        'text': a_tag.text,
                                        'href': a_tag.get_attribute('href'),
                                        'attachements': attachements
                                   })
               log_data(f"{c} out of {len(rows)}: {rfq_id} URL scraped")
               c +=1
          
     f = pd.DataFrame(href)
     f.to_csv(os.path.join(rfq_table, 'url.csv'))
     
     return href
    
# Function to wait for all files to finish downloading
def move_attachments_to_folder(attachements, rfq_id, duration=10):
     if len(attachements)>0:
          time.sleep(duration)
          # Call the function to wait for downloads to complete        
          while True:
               if not any([filename.endswith('.crdownload') for filename in os.listdir(download_path)]):
                    # Move the files to the RFQ immediately
                    rfq_test_folder = f'{rfq_folder}/{rfq_id}'
                    if not os.path.exists(rfq_test_folder):
                         os.makedirs(rfq_test_folder)
                    
                    # List our how many files are there
                    log_data(f"Processing {len(os.listdir(download_path))} files in the downloads folder")
                    
                    for filename in os.listdir(download_path):
                         file_path = os.path.join(download_path, filename)
                         log_data(f"Attempting to save file {filename} to the DB...")
                         try:
                              # Save File attachments to DB HERE
                              with open(file_path, 'rb') as file:
                                   opps_data = file.read()
                              
                              ebuyDoc = EbuyDocument(
                                   opps_source="EBUY",
                                   opps_id=rfq_id,
                                   opps_data=opps_data,
                                   opps_file_name=filename,
                                   created_date=datetime.now(),
                                   update_date=datetime.now(),
                                   opps_data_size=len(opps_data),
                                   opps_data_text=opps_data.decode('utf-8', errors='ignore'),
                                   status='NEW'
                              )
                              ebuyDoc.save_To_DB()
                              log_data(f"Saved {filename} to the DB successfully")
                         except Exception as e:
                              log_data(f"Error saving document {filename} to DB: {str(e)}")
                         
                         # Delete the file after saving to DB
                         if os.path.exists(file_path):
                              os.remove(file_path)
                              log_data(f"Deleted file: {filename}")
                    break
               time.sleep(1)

       
def check_if_session(driver, link):
     phrase = "GSA eBuy is designed to make online FAR compliant procurement faster and easier for federal civilian and military buyers. In addition to offering best pricing, GSA eBuy is your one-stop-shop for: Here is a sample of what can be done using GSA eBuy:"
     if phrase in driver.page_source:
          # 1. Click on Contractor Button
          constructor_link = WebDriverWait(driver, 5).until(EC.element_to_be_clickable((By.ID, "ebuy-home-page-signin-seller")))
          constructor_link.click()
          
          loginFlow(driver, True)
          log_data("Logged in successfully...")
          
          driver.get(link)

          # Save cookies after login
          saveCookies(driver) 
          log_data("Saved cookies...")  
     else:
          log_data("Session still active")
          
def scrapeUrl(driver, links, second_run=False):
     # Get Tenant ID
     tenant_id : str
     try:
          db = DatabaseConnection()
          tenant_id = db.get_ebuy_tenant_id()
          log_data(f"Acquired tenant id: {tenant_id}")
     except Exception as e:
          log_data(f"Error getting tenant_id: {str(e)}")
          
     log_data(f"Links length, {len(links)}")
     rfq_detail = []
     count = 1
     for link in links:
          loadCookies()
          driver.get(link['href'])
 
          rfq_id = WebDriverWait(driver, 20).until(EC.element_to_be_clickable((By.ID, "rfq-info-header-id"))).text
          title = driver.find_element(By.ID, "rfq-info-header-title").text
          
          # Press show more, then get description text
          try:
               driver.find_element(By.ID, "rfq-info-header-description-showmorebutton").click()
          except NoSuchElementException:
              pass
         
          time.sleep(2)
          try:
              desc = driver.find_element(By.ID, "rfq-info-header-description").text
          except NoSuchElementException:
              desc = " "

          try:
              reference_id = driver.find_element(By.ID, "rfq-info-header-reference-num").text
          except NoSuchElementException:
              reference_id = " "

          try:
              issue_date = driver.find_element(By.ID, "rfq-info-header-issue-date").text
          except NoSuchElementException:
              issue_date = " "

          try:
              close_date = driver.find_element(By.ID, "rfq-info-header-close-date").text
          except NoSuchElementException:
              close_date = " "

          try:
              delivery_info = driver.find_element(By.ID, "rfq-info-header-delivery-info").text
          except NoSuchElementException:
              delivery_info = " "

          try:
              contact_info = driver.find_element(By.XPATH, "//div[@class='card border p-3']").text
          except NoSuchElementException:
              contact_info = " "

          try:
              shipping_addresses_table = driver.find_element(By.ID, "rfq-display-shipping-addresses-list")
              shipping_addresses_rows = shipping_addresses_table.find_elements(By.TAG_NAME, "tr")
              shipping_addresses = []
              for row in shipping_addresses_rows:
                  shipping_addresses.append(row.text)
          except NoSuchElementException:
              shipping_addresses = []

          attachements = []
          try:
              attachment_block = driver.find_element(By.ID, "rfq-display-attachments-list")
              attachements_elem = attachment_block.find_elements(By.CLASS_NAME, "list-group-item")

              for attach in attachements_elem:
                  # Here is to download all the attachements
                  attach_span = attach.find_element(By.TAG_NAME, "a")
                  attach_span.click()

                  # Then save the name for retrieval
                  attachements.append(attach.text)
          except NoSuchElementException:
              attachements = []
               
          
          rfq_detail.append({
               'rfq_id': rfq_id,
               'title':title,
               'category': link['category'],
               'buyer': link['buyer'],
               'desc': desc,
               'reference_id': reference_id,
               'issue_date': issue_date,
               'close_date': close_date,
               'delivery_info': delivery_info,
               'contact_info': contact_info,
               'shipping_addresses': " ".join(shipping_addresses),
               'attachements': attachements + link['attachements'],
          })
          
          # Save RFQ To DB Here
          log_data(f"Scraped RFQ {count} out of {len(links)}: {rfq_id}")
          # Create EbuyOpportunity object and save to database
          opportunity = EbuyOpportunity(
               rfq_id=rfq_id,
               title=title,
               description=desc,
               reference_id=reference_id,
               tenant_id=tenant_id,
               issue_date=issue_date,
               close_date=close_date,
               delivery_info=delivery_info,
               contact_info=contact_info,
               shipping_address=" ".join(shipping_addresses),
               buyer=link['buyer']
          )
          
          # Save opportunity to database
          try:
               opportunity_id = opportunity.save_To_DB()
               log_data(f"Saved RFQ {rfq_id} to database with ID: {opportunity_id}")
          except Exception as e:
               log_data(f"Error saving RFQ {rfq_id} to database: {str(e)}")
          count +=1
          time.sleep(len(attachements)+2) #wait to download files
                   
     f = pd.DataFrame(rfq_detail)
     if second_run == True:
          f.to_csv(os.path.join(rfq_table, 'rfq_full_table.csv'), mode='a', header=not os.path.exists(os.path.join(rfq_table, 'rfq_full_table.csv')))
     else:
          f.to_csv(os.path.join(rfq_table, 'rfq_full_table.csv'))

def clearDwonloadFolder():
       # Clear out the download folder before starting the scraping process
     for filename in os.listdir(download_path):
          file_path = os.path.join(download_path, filename)
     try:
          if os.path.isfile(file_path) or os.path.islink(file_path):
               os.unlink(file_path)
          elif os.path.isdir(file_path):
               os.rmdir(file_path)
     except Exception as e:
          log_data(f"Failed to delete {file_path}. Reason: {e}")
     log_data(f"Cleared download folder: {download_path}")
     
                
def beginScrape(driver, loggedinurl: str, withDate: bool):
     clearDwonloadFolder()
      # Start script
     if withDate:
          try:
               driver.get(loggedinurl)

               # Load cookies
               loadCookies()

               # 1. Click on Contractor Button
               constructor_link = WebDriverWait(driver, 5).until(EC.element_to_be_clickable((By.ID, "ebuy-home-page-signin-seller")))
               constructor_link.click()

               time.sleep(5)

               if 'U.S. General Services Administration' in driver.page_source:
                    loginFlow(driver, False)
                    log_data("Logged in successfully...")
                    # Save cookies after login
                    saveCookies(driver) 
                    log_data("Saved cookies...")  
               else:
                    log_data("Previous session loaded")
                    
               time.sleep(10)
               
               
               scraped_links = scrapeTable(driver, withDate=withDate)
               log_data(f"Scraped {len(scraped_links)} successfully")

               # This function scrape links 
               scrapeUrl(driver, scraped_links, False)
               log_data(f"Scraped all total {len(scraped_links)} RFQs URLS successfully")
               
          except Exception as e:
               log_data(f"An error occurred: {str(e)}")
     
     # Only break loop when date scrape is not used
     else:
          try:
               log_data("Starting first loop of sequence")
               # 1. 1st loop is to get the rgq urls only
               driver.get(loggedinurl)
               
               # Load cookies
               loadCookies()
               
               constructor_link = WebDriverWait(driver, 5).until(EC.element_to_be_clickable((By.ID, "ebuy-home-page-signin-seller")))
               constructor_link.click()
               
               time.sleep(5)

               if 'U.S. General Services Administration' in driver.page_source:
                    loginFlow(driver, False)
                    log_data("Logged in successfully...")
                    # Save cookies after login
                    saveCookies(driver) 
                    log_data("Saved cookies...")  
               else:
                    log_data("Previous session loaded")
                    
               time.sleep(10)
               
               scraped_links = scrapeTable(driver, withDate=withDate)
               log_data(f"Scraped {len(scraped_links)} successfully")
               
               driver.quit()
               
               # 2. second loop is to get the first half of rfq urls
               time.sleep(10)
               log_data("Starting second loop of sequence")
               chrome_options = webdriver.ChromeOptions()
               prefs = {'download.default_directory' : download_path, 'profile.default_content_setting_values.automatic_downloads': 1}
               chrome_options.add_experimental_option('prefs', prefs)
               driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
               driver.get(loggedinurl)
               
               # Load cookies
               loadCookies()
               
               constructor_link = WebDriverWait(driver, 5).until(EC.element_to_be_clickable((By.ID, "ebuy-home-page-signin-seller")))
               constructor_link.click()
               
               time.sleep(5)

               if 'U.S. General Services Administration' in driver.page_source:
                    loginFlow(driver, False)
                    log_data("Logged in successfully...")
                    # Save cookies after login
                    saveCookies(driver) 
                    log_data("Saved cookies...")  
               else:
                    log_data("Previous session loaded")
               
               time.sleep(10)
                    
               # This function scrape links 
               half = len(scraped_links)//2
               first_half_links = scraped_links[:half]
               scrapeUrl(driver, first_half_links, False)
               log_data(f"Scraped a total of {len(scraped_links)} RFQs URL successfully")
               
               driver.quit()
               
               # 3. Third loop is to get the second half of rfqs
               time.sleep(10)
               log_data("Starting third loop of sequence")
               chrome_options = webdriver.ChromeOptions()
               prefs = {'download.default_directory' : download_path, 'profile.default_content_setting_values.automatic_downloads': 1}
               chrome_options.add_experimental_option('prefs', prefs)
               driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
               driver.get(loggedinurl)
               
               # Load cookies
               loadCookies()
               
               constructor_link = WebDriverWait(driver, 5).until(EC.element_to_be_clickable((By.ID, "ebuy-home-page-signin-seller")))
               constructor_link.click()
               
               time.sleep(5)

               if 'U.S. General Services Administration' in driver.page_source:
                    loginFlow(driver, False)
                    log_data("Logged in successfully...")
                    # Save cookies after login
                    saveCookies(driver) 
                    log_data("Saved cookies...")  
               else:
                    log_data("Previous session loaded")
               
               time.sleep(10)
                    
               # This function scrape links 
               half = len(scraped_links)//2
               first_half_links = scraped_links[half:]
               scrapeUrl(driver, first_half_links, True)
               log_data(f"Scraped all total {len(scraped_links)} RFQs URL successfully")
               
               driver.quit()
          
          except Exception as e:
               log_data(f"An error occurred: {str(e)}")
# 
     time.sleep(10)
     
     
     
# Main app loop startes here:
if __name__ == "__main__":          
     loggedinurl = "https://www.ebuy.gsa.gov/ebuy/seller/active-rfqs"
     withDate = os.getenv("USE_DATE") == "true"
     
     try:          
          # Start scraping script
          beginScrape(driver, loggedinurl, withDate)
    
     except Exception as e:
          logger.error(f"Error fetching data from db")
     
     

