{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "today_date = datetime.now().strftime(\"%m/%d/%Y\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["test = '11/04/2024'\n", "td = '11/04/2024 08:39 AM EST'"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["yes\n"]}], "source": ["if test in td:\n", "     print(\"yes\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import time\n", "import pytz "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current time in Eastern time (CT): 2024-11-13 15:58:27.976134-05:00\n"]}], "source": ["central = pytz.timezone('US/Eastern')\n", "current_time_central = datetime.now(central)\n", "\n", "print(\"Current time in Eastern time (CT):\", current_time_central)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["header_date_text = '11/13/2024 04:00 PM EST'\n", "header_date_text = header_date_text.replace(\" EST\", \"\") \n", "header_date = central.localize(datetime.strptime(header_date_text, \"%m/%d/%Y %I:%M %p\"))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["datetime.datetime(2024, 11, 13, 16, 0, tzinfo=<DstTzInfo 'US/Eastern' EST-1 day, 19:00:00 STD>)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["header_date"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Verification code acquired\n"]}], "source": ["while True:\n", "     if current_time_central < header_date:\n", "          print(\"Verification code acquired\")\n", "          break\n", "     else:\n", "          print(\"Pass, going around\")\n", "          time.sleep(30)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["z = \"Mod 1\\n10/24/2024\"\n", "z = z.replace('\\n', '_')\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["today_date = '10/24/2024'"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Date is mod\n"]}], "source": ["mods = \"10/24/2024\\nMod 2\\n10/29/2024\\nMod 3\\n10/29/2024\"\n", "\n", "if today_date in mods:\n", "     print(\"Date is mod\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# loading variables from .env file\n", "import os\n", "from dotenv import load_dotenv\n", "load_dotenv() \n", "\n", "# This path must correspod to the path specified as the download path in the main.py file\n", "download_path = os.getenv(\"DOWNLOAD_FOLDER\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["newpath = os.path.join(download_path, 'RFQs/{rfq}', \"dsdsd/dsd\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["addd = [1, 2, 3, 4]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["h = len(addd)//2"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import os \n", "from dotenv import load_dotenv\n", "import pandas as pd\n", "load_dotenv() "]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["rfq_table = os.getenv(\"TABLE_FOLDER\")\n", "href = [{\n", "     'category': \"category\",\n", "     'buyer': \"buyer\",\n", "     'text': \"a_tag.text\",\n", "     'href': \"a_tag.get_attribute('href')\",\n", "     'attachements': \"attachements\"\n", "}]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["f = pd.DataFrame(href)\n", "f.to_csv(os.path.join(rfq_table, 'url.csv'))"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 2}