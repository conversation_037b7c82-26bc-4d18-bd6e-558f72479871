import json
import psycopg2
from psycopg2.extras import DictCursor
import os
from dotenv import load_dotenv
import logging
import time
from .models import EbuyDocument, EbuyOpportunity
from datetime import datetime

# Load environment variables
load_dotenv()

# Configure logging
log_filename = 'EbuyScraper.logs'
# Remove existing log file if it exists
if os.path.exists(log_filename):
    os.remove(log_filename)

# Configure logging to write to file
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()  # This will keep console output as well
    ]
)
logger = logging.getLogger(__name__)


def parse_ebuy_datetime(date_str):
    """
    Parse eBuy date string like '08/25/2025 03:00 PM EDT' to a Python datetime object.
    Returns None if parsing fails or input is empty/None.
    """
    if not date_str or not isinstance(date_str, str):
        return None
    # Remove timezone if present (e.g., 'EDT', 'EST', etc.)
    date_str = date_str.strip()
    # Only keep the date and time part
    # eBuy sometimes has: "08/25/2025 03:00 PM EDT\nTime remaining: 19D 13H 25M"
    # So split on newline and take the first line
    date_str = date_str.split('\n')[0]
    # Remove trailing timezone abbreviation if present
    parts = date_str.rsplit(' ', 1)
    if len(parts) == 2 and len(parts[1]) == 3 and parts[1].isalpha():
        date_str = parts[0]
    # Try parsing
    for fmt in ("%m/%d/%Y %I:%M %p", "%m/%d/%Y %H:%M"):
        try:
            return datetime.strptime(date_str, fmt)
        except Exception:
            continue
    # If parsing fails, log and return None
    logger.warning(f"Could not parse date string: '{date_str}'")
    return None


class DatabaseConnection:
     def __init__(self):
          self.conn = None
          self.connect()

     def connect(self):
          """Establish database connection"""
          try:
               self.conn = psycopg2.connect(
                    dbname="postgres",
                    user="alpha",
                    password="5t3r2i66123",
                    host="govcon-1.cwjbmcfdk9qo.us-east-1.rds.amazonaws.com",
                    port="5432",
                    sslmode="verify-ca",
                    sslrootcert="us-east-1-bundle.pem"
               )
               logger.info("Successfully connected to the database")
          except Exception as e:
               logger.error(f"Error connecting to database: {str(e)}")
               raise

    
     def get_ebuy_email(self):
          """Retrieve the email value from the system table where name is 'ebuy_email'"""
          try:
               with self.conn.cursor() as cursor:
                    cursor.execute(
                         "SELECT value FROM public.system WHERE name = 'ebuy_email'"
                    )
                    result = cursor.fetchone()
                    
                    if result:
                         return result[0]
                    else:
                         logger.warning("No email found for 'ebuy_email' in system table")
                         return None
          except Exception as e:
               logger.error(f"Error retrieving email from database: {str(e)}")
               return None
          
     def get_ebuy_passsword(self):
          """Retrieve the email value from the system table where name is 'ebuy_email'"""
          try:
               with self.conn.cursor() as cursor:
                    cursor.execute(
                         "SELECT value FROM public.system WHERE name = 'ebuy_password'"
                    )
                    result = cursor.fetchone()
                    
                    if result:
                         return result[0]
                    else:
                         logger.warning("No password found for 'ebuy_password' in system table")
                         return None
          except Exception as e:
               logger.error(f"Error retrieving password from database: {str(e)}")
               return None
          
     def get_ebuy_tenant_id(self):
          """Retrieve the tenant id value from the system table where name is 'ebuy_tenant_id'"""
          try:
               with self.conn.cursor() as cursor:
                    cursor.execute(
                         "SELECT value FROM public.system WHERE name = 'ebuy_tenant_id'"
                    )
                    result = cursor.fetchone()
                    
                    if result:
                         return result[0]
                    else:
                         logger.warning("No Tenant ID found for 'ebuy_tenant_id' in system table")
                         return None
          except Exception as e:
               logger.error(f"Error retrieving password from database: {str(e)}")
               return None

     def save_ebuy_document(self, document: EbuyDocument):
          """
          Save an eBuy document to the database.
          If a document with the same opps_id and opps_file_name already exists, update the existing record.
          
          Args:
               document (EbuyDocument): The document object to be saved
          
          Returns:
               int or None: The ID of the newly inserted/updated record, or None if save failed
          """
          try:
               with self.conn.cursor() as cursor:
                    # First, check if a document with the same opps_id and opps_file_name already exists
                    check_query = """
                    SELECT id FROM kontratar_main.ebuy_opps_document 
                    WHERE opps_id = %s AND opps_file_name = %s
                    """
                    cursor.execute(check_query, (document.opps_id, document.opps_file_name))
                    existing_record = cursor.fetchone()
                    
                    if existing_record:
                         # Update existing record
                         update_query = """
                         UPDATE kontratar_main.ebuy_opps_document 
                         SET opps_source = %s, opps_data = %s, created_date = %s,
                             opps_data_size = %s, check_sum = %s, update_date = %s,
                             status = %s, opps_data_text = %s, elastic_search_index_doc = %s,
                             batch_id = %s
                         WHERE id = %s
                         RETURNING id
                         """
                         
                         values = (
                              self.clean_text_data(self.convert_empty_to_none(document.opps_source)),
                              document.opps_data,
                              document.created_date,
                              document.opps_data_size or 0,
                              document.check_sum,
                              document.update_date,
                              self.clean_text_data(self.convert_empty_to_none(document.status)),
                              self.clean_text_data(self.convert_empty_to_none(document.opps_data_text)),
                              self.clean_text_data(self.convert_empty_to_none(document.elastic_search_index_doc)),
                              self.clean_text_data(self.convert_empty_to_none(document.batch_id)),
                              existing_record[0]
                         )
                         
                         cursor.execute(update_query, values)
                         updated_id = cursor.fetchone()[0]
                         
                         # Commit the transaction
                         self.conn.commit()
                         
                         logger.info(f"Successfully updated eBuy document with ID: {updated_id}")
                         return updated_id
                    else:
                         # Insert new record
                         insert_query = """
                         INSERT INTO kontratar_main.ebuy_opps_document 
                         (opps_source, opps_id, opps_data, opps_file_name, created_date, 
                         opps_data_size, check_sum, update_date, status, 
                         opps_data_text, elastic_search_index_doc, batch_id)
                         VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                         RETURNING id
                         """
                         
                         # Convert empty values to None and clean text data
                         values = (
                              self.clean_text_data(self.convert_empty_to_none(document.opps_source)),
                              self.clean_text_data(self.convert_empty_to_none(document.opps_id)),
                              document.opps_data,
                              self.clean_text_data(self.convert_empty_to_none(document.opps_file_name)),
                              document.created_date,
                              document.opps_data_size or 0,
                              document.check_sum,
                              document.update_date,
                              self.clean_text_data(self.convert_empty_to_none(document.status)),
                              self.clean_text_data(self.convert_empty_to_none(document.opps_data_text)),
                              self.clean_text_data(self.convert_empty_to_none(document.elastic_search_index_doc)),
                              self.clean_text_data(self.convert_empty_to_none(document.batch_id))
                         )
                         
                         # Execute the insert and fetch the new ID
                         cursor.execute(insert_query, values)
                         new_id = cursor.fetchone()[0]
                         
                         # Commit the transaction
                         self.conn.commit()
                         
                         logger.info(f"Successfully saved new eBuy document with ID: {new_id}")
                         return new_id
          except Exception as e:
               # Rollback in case of error
               self.conn.rollback()
               print(document)
               logger.info(document)
               logger.error(f"Error saving eBuy document to database: {str(e)}")
               return None

     def save_ebuy_opportunity(self, opportunity:EbuyOpportunity):
          """
          Save an eBuy opportunity to the database.
          If rfq_id already exists, update the existing record.
          
          Args:
               opportunity (EbuyOpportunity): The opportunity object to be saved
          
          Returns:
               int or None: The ID of the newly inserted/updated record, or None if save failed
          """
          try:
               with self.conn.cursor() as cursor:
                    # Prepare the SQL upsert statement with ON CONFLICT
                    insert_query = """
                    INSERT INTO kontratar_main.ebuy_oppstable 
                    (rfq_id, title, description, reference_id, issue_date, close_date, 
                    delivery_info, contact_info, shipping_address, description_text, 
                    posted_date, naics_code, created_date, buyer, tenant_id, 
                    last_mod_date, summary_text, requirement_text, 
                    grading_criteria_text, requirement_full_text, opp_type, 
                    toc_text, toc_text_2, toc_text_3, toc_text_4, toc_text_5, 
                    format_compliance, keywords, content_compliance, 
                    structure_compliance, cover_page_fields, 
                    proposal_outline_1, proposal_outline_2, proposal_outline_3, 
                    proposal_outline_4, proposal_outline_5, key_personnel)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 
                            %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (rfq_id) DO UPDATE SET
                        title = EXCLUDED.title,
                        description = EXCLUDED.description,
                        reference_id = EXCLUDED.reference_id,
                        issue_date = EXCLUDED.issue_date,
                        close_date = EXCLUDED.close_date,
                        delivery_info = EXCLUDED.delivery_info,
                        contact_info = EXCLUDED.contact_info,
                        shipping_address = EXCLUDED.shipping_address,
                        description_text = EXCLUDED.description_text,
                        posted_date = EXCLUDED.posted_date,
                        naics_code = EXCLUDED.naics_code,
                        created_date = EXCLUDED.created_date,
                        buyer = EXCLUDED.buyer,
                        tenant_id = EXCLUDED.tenant_id,
                        last_mod_date = EXCLUDED.last_mod_date,
                        summary_text = EXCLUDED.summary_text,
                        requirement_text = EXCLUDED.requirement_text,
                        grading_criteria_text = EXCLUDED.grading_criteria_text,
                        requirement_full_text = EXCLUDED.requirement_full_text,
                        opp_type = EXCLUDED.opp_type,
                        toc_text = EXCLUDED.toc_text,
                        toc_text_2 = EXCLUDED.toc_text_2,
                        toc_text_3 = EXCLUDED.toc_text_3,
                        toc_text_4 = EXCLUDED.toc_text_4,
                        toc_text_5 = EXCLUDED.toc_text_5,
                        format_compliance = EXCLUDED.format_compliance,
                        keywords = EXCLUDED.keywords,
                        content_compliance = EXCLUDED.content_compliance,
                        structure_compliance = EXCLUDED.structure_compliance,
                        cover_page_fields = EXCLUDED.cover_page_fields,
                        proposal_outline_1 = EXCLUDED.proposal_outline_1,
                        proposal_outline_2 = EXCLUDED.proposal_outline_2,
                        proposal_outline_3 = EXCLUDED.proposal_outline_3,
                        proposal_outline_4 = EXCLUDED.proposal_outline_4,
                        proposal_outline_5 = EXCLUDED.proposal_outline_5,
                        key_personnel = EXCLUDED.key_personnel
                    RETURNING id
                    """
                    
                    # Convert empty values to None and clean text data
                    # Convert issue_date and close_date to timestamps if they are strings
                    issue_date = opportunity.issue_date
                    close_date = opportunity.close_date
                    if isinstance(issue_date, str):
                        issue_date = parse_ebuy_datetime(issue_date)
                    if isinstance(close_date, str):
                        close_date = parse_ebuy_datetime(close_date)

                    values = (
                         self.clean_text_data(self.convert_empty_to_none(opportunity.rfq_id)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.title)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.description)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.reference_id)),
                         issue_date,
                         close_date,
                         self.clean_text_data(self.convert_empty_to_none(opportunity.delivery_info)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.contact_info)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.shipping_address)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.description_text)),
                         opportunity.posted_date,
                         self.clean_text_data(self.convert_empty_to_none(opportunity.naics_code)),
                         opportunity.created_date,
                         self.clean_text_data(self.convert_empty_to_none(opportunity.buyer)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.tenant_id)),
                         opportunity.last_mod_date,
                         self.clean_text_data(self.convert_empty_to_none(opportunity.summary_text)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.requirement_text)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.grading_criteria_text)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.requirement_full_text)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.opp_type)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.toc_text)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.toc_text_2)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.toc_text_3)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.toc_text_4)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.toc_text_5)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.format_compliance)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.keywords)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.content_compliance)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.structure_compliance)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.cover_page_fields)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.proposal_outline_1)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.proposal_outline_2)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.proposal_outline_3)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.proposal_outline_4)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.proposal_outline_5)),
                         self.clean_text_data(self.convert_empty_to_none(opportunity.key_personnel))
                    )
                    
                    # Execute the insert and fetch the new ID
                    cursor.execute(insert_query, values)
                    new_id = cursor.fetchone()[0]
                    
                    # Commit the transaction
                    self.conn.commit()
                    
                    logger.info(f"Successfully saved/updated eBuy opportunity with ID: {new_id}")
                    return new_id
          except Exception as e:
               # Rollback in case of error
               self.conn.rollback()
               logger.info(opportunity)
               logger.error(f"Error saving eBuy opportunity to database: {str(e)}")
               return None
     def close(self):
          """Close database connection"""
          if self.conn:
               self.conn.close()
               logger.info("Database connection closed")

     def convert_empty_to_none(self, value):
          """Convert empty string to None, otherwise return the value"""
          return None if value == '' else value
     
     def clean_text_data(self, value):
          """Clean text data by removing null bytes and handling encoding issues"""
          if value is None:
               return None
          if isinstance(value, str):
               # Remove null bytes and other problematic characters
               cleaned = value.replace('\x00', '').replace('\x01', '').replace('\x02', '').replace('\x03', '')
               # Remove other control characters that might cause issues
               cleaned = ''.join(char for char in cleaned if ord(char) >= 32 or char in '\n\r\t')
               return cleaned if cleaned else None
          return value