from datetime import datetime


class EbuyDocument:
     """
     Represents a document from the Ebuy platform with various metadata and content attributes.
     
     Attributes:
         opps_source (str): Source of the opportunity, defaults to "EBUY"
         opps_id (str): Unique identifier for the opportunity
         opps_data (bytes): Raw binary data of the document
         opps_file_name (str): Name of the file
         created_date (datetime): Date and time when the document was created
         opps_data_size (int): Size of the document data in bytes
         check_sum (bytes): Checksum for data integrity verification
         update_date (datetime): Date and time of last update
         status (str): Current status of the document
         opps_data_text (str): Extracted text content of the document
         elastic_search_index_doc (str): Elasticsearch index reference
         batch_id (str): Batch identifier for grouping related documents
     """
     def __init__(self, opps_source: str = "EBUY", opps_id: str = None, opps_data: bytes = None, 
                 opps_file_name: str = None, created_date: datetime = None, 
                 opps_data_size: int = None, check_sum: bytes = None, 
                 update_date: datetime = None, status: str = None, 
                 opps_data_text: str = None, elastic_search_index_doc: str = None, 
                 batch_id: str = None):
        self.opps_source = opps_source
        self.opps_id = opps_id
        self.opps_data = opps_data
        self.opps_file_name = opps_file_name
        self.created_date = created_date
        self.opps_data_size = opps_data_size
        self.check_sum = check_sum
        self.update_date = update_date
        self.status = status
        self.opps_data_text = opps_data_text
        self.elastic_search_index_doc = elastic_search_index_doc
        self.batch_id = batch_id
        
     def save_To_DB(self):
          from db.db import DatabaseConnection
          db = DatabaseConnection()
          return db.save_ebuy_document(self)
          
          
          
          
        
        
class EbuyOpportunity:
     """
     Represents an opportunity from the Ebuy platform with comprehensive metadata.
     
     Attributes:
         rfq_id (str): Unique identifier for the Request for Quote
         title (str): Title of the opportunity
         description (str): Brief description of the opportunity
         reference_id (str): External reference number for the opportunity
         issue_date (datetime): Date when the opportunity was first issued
         close_date (datetime): Deadline for submitting quotes
         delivery_info (str): Information about delivery requirements
         contact_info (str): Contact details for the opportunity
         shipping_address (str): Address for shipping or delivery
         description_text (str): Detailed description text
         posted_date (datetime): Date when the opportunity was posted
         naics_code (str): North American Industry Classification System code
         created_date (datetime): Date of record creation
         buyer (str): Name or organization of the buyer
         tenant_id (str): Identifier for the tenant or organization
         last_mod_date (datetime): Date of last modification
         summary_text (str): Summarized overview of the opportunity
         requirement_text (str): Specific requirements of the opportunity
         grading_criteria_text (str): Criteria used for evaluating proposals
         requirement_full_text (str): Comprehensive requirements description
         opp_type (str): Type of opportunity
         toc_text (str): Table of contents or key sections text
         toc_text_2-5 (str): Additional table of contents sections
         format_compliance (str): Compliance requirements for document format
         keywords (str): Relevant keywords for the opportunity
         content_compliance (str): Compliance requirements for content
         structure_compliance (str): Compliance requirements for document structure
         cover_page_fields (str): Specific fields required on the cover page
         proposal_outline_1-5 (str): Detailed proposal outline sections
         key_personnel (str): Key personnel information
     """
     def __init__(self, rfq_id: str = None, title: str = None, description: str = None, 
                 reference_id: str = None, issue_date: datetime = None, 
                 close_date: datetime = None, delivery_info: str = None, 
                 contact_info: str = None, shipping_address: str = None, 
                 description_text: str = None, posted_date: datetime = None, 
                 naics_code: str = None, created_date: datetime = None, 
                 buyer: str = None, tenant_id: str = None, 
                 last_mod_date: datetime = None, summary_text: str = None, 
                 requirement_text: str = None, grading_criteria_text: str = None, 
                 requirement_full_text: str = None, opp_type: str = None, 
                 toc_text: str = None, toc_text_2: str = None, 
                 toc_text_3: str = None, toc_text_4: str = None, 
                 toc_text_5: str = None, format_compliance: str = None, 
                 keywords: str = None, content_compliance: str = None, 
                 structure_compliance: str = None, cover_page_fields: str = None, 
                 proposal_outline_1: str = None, proposal_outline_2: str = None, 
                 proposal_outline_3: str = None, proposal_outline_4: str = None, 
                 proposal_outline_5: str = None, key_personnel: str = None):
        self.rfq_id = rfq_id
        self.title = title
        self.description = description
        self.reference_id = reference_id
        self.issue_date = issue_date
        self.close_date = close_date
        self.delivery_info = delivery_info
        self.contact_info = contact_info
        self.shipping_address = shipping_address
        self.description_text = description_text
        self.posted_date = posted_date
        self.naics_code = naics_code
        self.created_date = created_date
        self.buyer = buyer
        self.tenant_id = tenant_id
        self.last_mod_date = last_mod_date
        self.summary_text = summary_text
        self.requirement_text = requirement_text
        self.grading_criteria_text = grading_criteria_text
        self.requirement_full_text = requirement_full_text
        self.opp_type = opp_type
        self.toc_text = toc_text
        self.toc_text_2 = toc_text_2
        self.toc_text_3 = toc_text_3
        self.toc_text_4 = toc_text_4
        self.toc_text_5 = toc_text_5
        self.format_compliance = format_compliance
        self.keywords = keywords
        self.content_compliance = content_compliance
        self.structure_compliance = structure_compliance
        self.cover_page_fields = cover_page_fields
        self.proposal_outline_1 = proposal_outline_1
        self.proposal_outline_2 = proposal_outline_2
        self.proposal_outline_3 = proposal_outline_3
        self.proposal_outline_4 = proposal_outline_4
        self.proposal_outline_5 = proposal_outline_5
        self.key_personnel = key_personnel
        
     def save_To_DB(self):
          from db.db import DatabaseConnection
          db = DatabaseConnection()
          return db.save_ebuy_opportunity(self)