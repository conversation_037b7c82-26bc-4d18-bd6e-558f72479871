name: Build and Push AIService Docker Image

on:
  workflow_call:
    inputs:
      docker_image_name:
        description: 'Docker image name (e.g. ai-service)'
        required: true
        type: string
      docker_image_tag:
        description: 'Docker image tag (e.g. latest)'
        required: true
        type: string
    secrets:
      DOCKERHUB_USERNAME:
        required: true
      DOCKERHUB_TOKEN:
        required: true

jobs:
  build-and-push:
    runs-on: [self-hosted, backend]
    defaults:
      run:
        working-directory: AIService
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Log in to Docker Hub
        run: |
          echo "${{ secrets.DOCKERHUB_TOKEN }}" | docker login -u "${{ secrets.DOCKERHUB_USERNAME }}" --password-stdin

      - name: Set up image tags
        id: vars
        run: |
          echo "IMAGE=${{ inputs.docker_image_name }}" >> $GITHUB_ENV
          echo "TAG=${{ inputs.docker_image_tag }}" >> $GITHUB_ENV

      - name: Build images with docker compose (tagged and latest)
        run: |
          set -e
          docker compose build
          docker tag $IMAGE:latest $IMAGE:$TAG

      - name: Push both tags to Docker Hub
        run: |
          set -e
          docker push $IMAGE:latest
          docker push $IMAGE:$TAG

      - name: (Optional) Validate docker-compose config
        run: docker compose config
        continue-on-error: true
