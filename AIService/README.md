# AI Service

A FastAPI-based service for processing queue items from multiple PostgreSQL databases with automated scheduling.

## Features

- **Dual Database Support**: Connects to both kontratar and customer databases
- **Schema Support**: Handles multiple PostgreSQL schemas (kontratar_main, kontratar_global, opportunity)
- **Queue Management**: Processes proposal_queue and custom_opps_queue tables
- **Automated Scheduling**: Background scheduler that picks up new items every 30 seconds
- **RESTful API**: Complete API for queue management and scheduler control
- **Async Processing**: Non-blocking async operations for better performance

## Database Configuration

The service connects to two separate PostgreSQL databases:

### 1. Kontratar Database

- **Schemas**: `kontratar_main`, `kontratar_global`
- **Tables**: `proposal_queue`, `custom_opps_queue`, `oppstable`, `ebuy_oppstable`, etc.

### 2. Customer Database

- **Schema**: `opportunity`
- **Tables**: `proposal_queue`, `custom_opps_queue`, `custom_oppstable`, etc.

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Environment Configuration

Create a `.env` file in the root directory:

```env
# Database Configuration
# Kontratar Database (kontratar_main and kontratar_global schemas)
KONTRATAR_DB_HOST=localhost
KONTRATAR_DB_PORT=5432
KONTRATAR_DB_NAME=kontratar
KONTRATAR_DB_USER=your_username
KONTRATAR_DB_PASSWORD=your_password

# Customer Database (opportunity schema)
CUSTOMER_DB_HOST=localhost
CUSTOMER_DB_PORT=5432
CUSTOMER_DB_NAME=opportunity
CUSTOMER_DB_USER=your_username
CUSTOMER_DB_PASSWORD=your_password

# Application Configuration
APP_HOST=0.0.0.0
APP_PORT=8000
DEBUG=True

# Scheduler Configuration
SCHEDULER_INTERVAL_SECONDS=30
```

### 3. Database Setup

Ensure your PostgreSQL databases have the required schemas and tables. The service will automatically create tables based on the SQLAlchemy models.

### 4. Run the Service

```bash
python main.py
```

Or using uvicorn directly:

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## API Endpoints

### Health Check

- `GET /health` - Service health status

### Scheduler Management

- `GET /scheduler/status` - Get scheduler status and job information
- `POST /scheduler/start` - Start the scheduler
- `POST /scheduler/stop` - Stop the scheduler

### Kontratar Database Endpoints

#### Proposal Queue

- `GET /kontratar/proposal-queue/new` - Get new proposal queue items
- `POST /kontratar/proposal-queue` - Create new proposal queue item
- `PUT /kontratar/proposal-queue/{job_id}/status` - Update proposal queue status

#### Custom Opps Queue

- `GET /kontratar/custom-opps-queue/new` - Get new custom opps queue items
- `POST /kontratar/custom-opps-queue` - Create new custom opps queue item
- `PUT /kontratar/custom-opps-queue/{opps_id}/status` - Update custom opps queue status

### Customer Database Endpoints

#### Proposal Queue

- `GET /customer/proposal-queue/new` - Get new proposal queue items
- `POST /customer/proposal-queue` - Create new proposal queue item
- `PUT /customer/proposal-queue/{job_id}/status` - Update proposal queue status

#### Custom Opps Queue

- `GET /customer/custom-opps-queue/new` - Get new custom opps queue items
- `POST /customer/custom-opps-queue` - Create new custom opps queue item
- `PUT /customer/custom-opps-queue/{opps_id}/status` - Update custom opps queue status

## API Documentation

Once the service is running, you can access:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Queue Processing

The scheduler automatically processes queue items with status "NEW" every 30 seconds (configurable). The processing flow is:

1. **Pick up items** with status "NEW"
2. **Update status** to "PROCESSING"
3. **Process the item** (implement your AI logic in `_process_proposal_item` and `_process_custom_opps_item`)
4. **Update status** to "COMPLETED" or "FAILED"

### Status Values

- `NEW` - Item is ready for processing
- `PROCESSING` - Item is currently being processed
- `COMPLETED` - Item has been successfully processed
- `FAILED` - Item processing failed

## Customization

### Adding AI Processing Logic

To implement your AI processing logic, modify the following methods in `services/scheduler_service.py`:

```python
async def _process_proposal_item(self, item):
    """Process a proposal queue item"""
    # Add your AI processing logic here
    # Examples:
    # - Call AI services
    # - Generate embeddings
    # - Update related tables
    # - Send notifications
    pass

async def _process_custom_opps_item(self, item):
    """Process a custom opps queue item"""
    # Add your AI processing logic here
    # Examples:
    # - Process opportunity data
    # - Extract keywords
    # - Generate summaries
    pass
```

### Database Models

The service includes SQLAlchemy models for all major tables. You can extend these models in:

- `models/kontratar_models.py` - For kontratar database models
- `models/customer_models.py` - For customer database models

### Configuration

Modify `config.py` to add new configuration options or change default values.

## Logging

The service uses `loguru` for logging. Logs include:

- Database operations
- Queue processing status
- Scheduler events
- Error handling

## Error Handling

The service includes comprehensive error handling:

- Database connection errors
- Queue processing failures
- Invalid API requests
- Scheduler errors

## Monitoring

Monitor the service using:

- Health check endpoint: `GET /health`
- Scheduler status: `GET /scheduler/status`
- Application logs

## Development

### Project Structure

```
AIService/
├── main.py                 # FastAPI application
├── config.py              # Configuration management
├── database.py            # Database connections
├── requirements.txt       # Python dependencies
├── models/                # SQLAlchemy models
│   ├── __init__.py
│   ├── kontratar_models.py
│   └── customer_models.py
└── services/              # Business logic
    ├── __init__.py
    ├── queue_service.py   # Queue operations
    └── scheduler_service.py # Background scheduler
```

### Adding New Features

1. **New Models**: Add to appropriate models file
2. **New Services**: Create in services directory
3. **New Endpoints**: Add to main.py
4. **New Configuration**: Update config.py

## Troubleshooting

### Common Issues

1. **Database Connection Errors**

   - Check database credentials in `.env`
   - Ensure databases are running
   - Verify schema permissions

2. **Scheduler Not Running**

   - Check scheduler status: `GET /scheduler/status`
   - Restart scheduler: `POST /scheduler/start`

3. **Queue Items Not Processing**
   - Verify items have status "NEW"
   - Check scheduler logs
   - Ensure no processing errors

### Debug Mode

Enable debug mode in `.env`:

```env
DEBUG=True
```

This enables auto-reload and detailed error messages.

## Production Deployment

For production deployment:

1. **Set DEBUG=False** in environment
2. **Use proper database credentials**
3. **Configure logging appropriately**
4. **Set up monitoring and alerting**
5. **Use a process manager (systemd, supervisor, etc.)**
6. **Configure reverse proxy (nginx, etc.)**

## License

This project is proprietary software.
