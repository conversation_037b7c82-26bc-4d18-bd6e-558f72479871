'''Pipeline to generate criticism reports for a proposal outline'''

import asyncio
import json
from services.proposal.outline import ProposalOutlineService
from services.proposal.criticism_chain import Criticism<PERSON>hain
from services.proposal.utilities import ProposalUtilities


reporting_data_folder = "reporting-data"

async def main():
    # dictionary = {
    #     "table_of_contents": [
    #         {
    #         "title": "Tab A - Proposal Cover/Transmittal Letter",
    #         "description": "Includes the proposal cover and transmittal letter.",
    #         "number": "1.0",
    #         "subsections": []
    #         },
    #         {
    #         "title": "Tab B - Factor 1 - Staffing & Key Personnel Qualifications",
    #         "description": "Description of recruitment, hiring, retention, and development of qualified staff. Includes resumes of proposed key personnel and contingent offer letters if applicable.",
    #         "number": "2.0",
    #         "subsections": [
    #             {
    #             "number": "2.1",
    #             "title": "Resume of Proposed Key Personnel",
    #             "description": "Resumes for proposed Key Personnel (max 2 pages each), identifying employment status.",
    #             },
    #             {
    #             "number": "2.2",
    #             "title": "Tentative/Contingent Offer letter",
    #             "description": "If not an existing employee, include a signed tentative/contingent offer letter (max 1 page)."
    #             }
    #         ]
    #         },
    #         {
    #         "title": "Tab C - Factor 2 - Management Approach",
    #         "description": "Detailed description of approach to manage and support a comprehensive DHS export controls compliance program.",   
    #         "number": "3.0",
    #         "subsections": []
    #         },
    #         {
    #         "title": "Tab D - Factor 3 - Technical Approach",
    #         "description": "Demonstrated understanding of scope, complexity, and level of effort involved in managing, maintaining, and supporting the Export Control Group (ECG). Includes expertise in compliance with EAR, ITAR, OFAC regulations, and multilateral regimes.",       
    #         "number": "4.0",
    #         "subsections": [
    #             {
    #             "number": "5.1",
    #             "title": "Task 1 – Program Management and Administration",
    #             "description": "Experience related to Program Management and Administration (includes surge support for CFIUS cases)."
    #             },
    #             {
    #             "number": "5.2",
    #             "title": "Task 2 – Information Management",
    #             "description": "Experience related to Information Management (collection, analysis, tracking, storage, archiving, database maintenance, metric tracking)."
    #             },
    #             {
    #             "number": "5.3",
    #             "title": "Task 3 – Program Compliance",
    #             "description": "Experience related to Program Compliance (inform program managers, provide guidance to DHS components, acquire documentation, maintain records)."
    #             }
    #         ]
    #         },
    #         {
    #         "title": "Tab E - Factor 4 - Demonstrated Corporate Experience",
    #         "description": "Up to three examples of federal government experience completed within the last five years or currently in progress. Examples must cover each task area in the SOW.",
    #         "number": "5.0",
    #         "subsections": [
                
    #         ]
    #         }
    #     ]
    # }
    # opportunity_id = "iRiYNgd8RC"
    # tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    # source = "custom"

    # outline_service = ProposalOutlineService()
    # proposal_outline = await outline_service.generate_outline(
    #     opportunity_id=opportunity_id,
    #     tenant_id=tenant_id,
    #     source=source,
    #     table_of_contents=dictionary.get("table_of_contents", [])
    # )
    # print("Generated proposal outline successfully!")

    proposal_outline = ProposalUtilities.read_json_from_file(f"{reporting_data_folder}/proposal_outline.json")
    
    # Initialize criticism chain
    criticism_chain = CriticismChain()
    
    # Load compliance data from files
    try:
        # Load technical requirements
        with open(f"{reporting_data_folder}/technical-requirements.json", "r", encoding="utf-8") as f:
            technical_requirements = json.load(f)
        
        # Load structure compliance
        with open(f"{reporting_data_folder}/structure-compliance.txt", "r", encoding="utf-8") as f:
            structure_content = f.read()
            # Extract JSON from the markdown code block
            import re
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', structure_content, re.DOTALL)
            if json_match:
                structure_compliance = json.loads(json_match.group(1))
            else:
                structure_compliance = {}
        
        # Load formatting requirements
        with open(f"{reporting_data_folder}/formatting-requirements.txt", "r", encoding="utf-8") as f:
            formatting_content = f.read()
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', formatting_content, re.DOTALL)
            if json_match:
                formatting_requirements = json.loads(json_match.group(1))
            else:
                formatting_requirements = {}
        
        # Load content compliance
        with open(f"{reporting_data_folder}/content-compliance.txt", "r", encoding="utf-8") as f:
            content_compliance = {"content": f.read()}
            
        print("Loaded all compliance files successfully!")
            
    except Exception as e:
        print(f"Error loading compliance files: {e}")
        # Fallback to empty dictionaries
        technical_requirements = {"content": ""}
        structure_compliance = {}
        content_compliance = {}
        formatting_requirements = {}
    
    # Generate individual criticism reports
    print("\n=== Generating Individual Criticism Reports ===")
    
    # 1. Structure Report
    print("Generating structure report...")
    structure_report = criticism_chain.create_structure_report(proposal_outline, structure_compliance)
    ProposalUtilities.save_text_to_file(structure_report, f"{reporting_data_folder}/structure-criticism-report.txt")
    print("✅ Structure report saved to 'structure-criticism-report.txt'")
    
    # 2. Content Report
    print("Generating content report...")
    content_report = criticism_chain.create_content_report(proposal_outline, content_compliance)
    ProposalUtilities.save_text_to_file(content_report, f"{reporting_data_folder}/content-criticism-report.txt")
    print("✅ Content report saved to 'content-criticism-report.txt'")
    
    # 3. Formatting Report
    print("Generating formatting report...")
    formatting_report = criticism_chain.create_formatting_report(proposal_outline, formatting_requirements)
    ProposalUtilities.save_text_to_file(formatting_report, f"{reporting_data_folder}/formatting-criticism-report.txt")
    print("✅ Formatting report saved to 'formatting-criticism-report.txt'")
    
    # 4. Technical Report
    print("Generating technical report...")
    technical_report = criticism_chain.create_technical_report(proposal_outline, technical_requirements)
    ProposalUtilities.save_text_to_file(technical_report, f"{reporting_data_folder}/technical-criticism-report.txt")
    print("✅ Technical report saved to 'technical-criticism-report.txt'")
    
    # Generate comprehensive full report
    print("\n=== Generating Comprehensive Full Report ===")
    full_report = criticism_chain.generate_full_report(
        proposal_outline,
        structure_compliance,
        content_compliance,
        formatting_requirements,
        technical_requirements
    )
    ProposalUtilities.save_text_to_file(full_report, f"{reporting_data_folder}/complete-criticism-report.txt")
    print("✅ Complete report saved to 'complete-criticism-report.txt'")
    
    # Print summary
    print("\n=== Report Generation Complete ===")
    print("Generated 5 reports:")
    print("1. structure-criticism-report.txt")
    print("2. content-criticism-report.txt") 
    print("3. formatting-criticism-report.txt")
    print("4. technical-criticism-report.txt")
    print("5. complete-criticism-report.txt")
    
    # Print a preview of the full report
    print("\n=== Full Report Preview ===")
    print(full_report[:1000] + "..." if len(full_report) > 1000 else full_report)


if __name__ == "__main__":
    asyncio.run(main())
