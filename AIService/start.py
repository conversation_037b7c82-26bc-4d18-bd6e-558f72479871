#!/usr/bin/env python3
"""
Startup script for AI Service
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import uvicorn
from config import settings
from loguru import logger

if __name__ == "__main__":
    logger.info("Starting AI Service...")
    logger.info(f"Host: {settings.app_host}")
    logger.info(f"Port: {settings.app_port}")
    logger.info(f"Debug: {settings.debug}")
    logger.info(f"Scheduler Interval: {settings.scheduler_interval_seconds} seconds")
    
    uvicorn.run(
        "main:app",
        host=settings.app_host,
        port=settings.app_port,
        reload=settings.debug,
        log_level="info"
    ) 