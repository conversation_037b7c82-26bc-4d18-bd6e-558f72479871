import re
from html import unescape
from typing import List, Dict, Any

from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import Paragraph, Spacer, Table, TableStyle
from reportlab.lib import colors
from reportlab.lib.enums import TA_JUSTIFY, TA_LEFT, TA_CENTER

from loguru import logger


# Constants
DEFAULT_FONT_TYPE = 'Times-Roman'
DEFAULT_FONT_SIZE_BODY = 12
DEFAULT_FONT_SIZE_HEADER = 14
DEFAULT_FONT_SIZE_TITLE = 18
DEFAULT_FONT_SIZE_SUBHEADING = 12
PAGE_WIDTH_POINTS = letter[0]  # 8.5 inches * 72 points/inch = 612 points


class MarkdownRenderer:
    """Utility class for rendering markdown content to ReportLab elements"""

    @staticmethod
    def _clean_html_text(html_text: str) -> str:
        """Clean HTML text for ReportLab processing"""
        # Remove HTML tags but preserve basic formatting
        text = re.sub(r'<br\s*/?>', '\n', html_text)
        text = re.sub(r'<p[^>]*>', '', text)
        text = re.sub(r'</p>', '\n\n', text)
        text = re.sub(r'<[^>]+>', '', text)  # Remove all other HTML tags
        text = unescape(text)  # Decode HTML entities
        return text.strip()

    @staticmethod
    def _process_bold_text(text: str) -> str:
        """Convert **text** to ReportLab bold markup"""
        # Find all **text** patterns and convert to <b>text</b>
        def replace_bold(match):
            return f"<b>{match.group(1)}</b>"
        
        # Replace **text** with <b>text</b> for ReportLab
        result = re.sub(r'\*\*(.*?)\*\*', replace_bold, text)
        return result

    @staticmethod
    def _add_table_to_elements(elements: list, table_rows: list, font_type: str = DEFAULT_FONT_TYPE, font_size_body: int = DEFAULT_FONT_SIZE_BODY):
        """Add a properly formatted table to the elements list"""
        if not table_rows:
            logger.warning("Markdown Renderer: No table rows to add")
            return
        
        logger.info(f"Markdown Renderer: Adding table with {len(table_rows)} rows")
        
        # Process bold text in table cells
        processed_rows = []
        for row in table_rows:
            processed_row = []
            for cell in row:
                processed_cell = MarkdownRenderer._process_bold_text(cell)
                # Create Paragraph for each cell to support bold formatting
                processed_row.append(Paragraph(processed_cell, getSampleStyleSheet()['Normal']))
            processed_rows.append(processed_row)
        
        # Calculate available width (page width minus margins)
        available_width = PAGE_WIDTH_POINTS - (2 * inch)  # Subtract left and right margins
        
        # Calculate column widths based on content
        num_cols = len(table_rows[0]) if table_rows else 1
        col_width = available_width / num_cols
        
        # Create table with calculated column widths
        table = Table(processed_rows, colWidths=[col_width] * num_cols)
        
        # Determine if first row is a header (simple heuristic)
        has_header = len(table_rows) > 1
        
        # Basic table style
        table_style = [
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), font_type),
            ('FONTSIZE', (0, 0), (-1, -1), font_size_body - 3),  # Slightly smaller font for tables
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
            ('TOPPADDING', (0, 0), (-1, -1), 4),
            ('LEFTPADDING', (0, 0), (-1, -1), 4),
            ('RIGHTPADDING', (0, 0), (-1, -1), 4),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('WORDWRAP', (0, 0), (-1, -1), 'LTR'),  # Enable word wrapping
        ]
        
        # Header styling if we have one
        if has_header:
            table_style.extend([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('FONTNAME', (0, 0), (-1, 0), f'{font_type}-Bold' if font_type == 'Times-Roman' else 'Times-Bold'),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                ('FONTSIZE', (0, 0), (-1, 0), font_size_body - 2),  # Slightly larger for headers
            ])
        
        # Alternating row colors for better readability
        if len(table_rows) > 2:
            for i in range(1 if has_header else 0, len(table_rows), 2):
                table_style.append(('BACKGROUND', (0, i), (-1, i), colors.beige))
        
        table.setStyle(TableStyle(table_style))
        
        # Set table to split across pages if needed
        table.hAlign = 'LEFT'
        table.keepWithNext = False
        table.splitByRow = True
        
        elements.append(table)
        elements.append(Spacer(1, 12))
        logger.info(f"Markdown Renderer: Successfully added table with {len(table_rows)} rows")

    @staticmethod
    def render_markdown_to_reportlab(
        markdown_content: str,
        font_type: str = DEFAULT_FONT_TYPE,
        font_size_body: int = DEFAULT_FONT_SIZE_BODY,
        font_size_header: int = DEFAULT_FONT_SIZE_HEADER,
        font_size_title: int = DEFAULT_FONT_SIZE_TITLE,
        font_size_subheading: int = DEFAULT_FONT_SIZE_SUBHEADING,
        line_spacing: float = 1.5
    ) -> list:
        """Convert markdown content to ReportLab elements"""
        # Get ReportLab styles
        styles = getSampleStyleSheet()
        
        # Create custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=font_size_title,
            spaceAfter=20,
            textColor=colors.darkblue,
            fontName='Times-Bold'
        )
        
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=font_size_header,
            spaceAfter=12,
            spaceBefore=16,
            textColor=colors.darkslategray,
            fontName='Times-Bold'
        )
        
        subheading_style = ParagraphStyle(
            'CustomSubHeading',
            parent=styles['Heading3'],
            fontSize=font_size_subheading,
            spaceAfter=8,
            spaceBefore=12,
            textColor=colors.darkslategray,
            fontName='Times-Bold'
        )
        
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=font_size_body,
            spaceAfter=8,
            alignment=TA_JUSTIFY,
            fontName='Times-Roman'
        )
        
        bold_style = ParagraphStyle(
            'CustomBold',
            parent=styles['Normal'],
            fontSize=font_size_body,
            spaceAfter=6,
            fontName='Times-Bold'
        )
        
        bullet_style = ParagraphStyle(
            'CustomBullet',
            parent=styles['Normal'],
            fontSize=font_size_body,
            spaceAfter=4,
            spaceBefore=2,
            leftIndent=20,
            bulletIndent=10,
            fontName='Times-Roman'
        )

        # Split content into lines
        lines = markdown_content.split('\n')
        elements = []
        table_rows = []
        in_table = False
        
        logger.info(f"Processing {len(lines)} lines of markdown content")
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            if not line:
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"Ending table with {len(table_rows)} rows")
                    MarkdownRenderer._add_table_to_elements(elements, table_rows, font_type=font_type, font_size_body=font_size_body)
                    table_rows = []
                    in_table = False
                elements.append(Spacer(1, 6))
                i += 1
                continue
            
            # Process different markdown elements
            if line.startswith('# '):
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"Ending table with {len(table_rows)} rows due to title")
                    MarkdownRenderer._add_table_to_elements(elements, table_rows, font_type=font_type, font_size_body=font_size_body)
                    table_rows = []
                    in_table = False
                text = line[2:].strip()
                elements.append(Paragraph(text, title_style))
                elements.append(Spacer(1, 12))
            elif line.startswith('## '):
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"Ending table with {len(table_rows)} rows due to heading")
                    MarkdownRenderer._add_table_to_elements(elements, table_rows, font_type=font_type, font_size_body=font_size_body)
                    table_rows = []
                    in_table = False
                text = line[3:].strip()
                elements.append(Paragraph(text, heading_style))
            elif line.startswith('### '):
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"Ending table with {len(table_rows)} rows due to subheading")
                    MarkdownRenderer._add_table_to_elements(elements, table_rows, font_type=font_type, font_size_body=font_size_body)
                    table_rows = []
                    in_table = False
                text = line[4:].strip()
                elements.append(Paragraph(text, subheading_style))
            elif line.startswith('**') and line.endswith('**') and len(line) > 4 and '**' not in line[2:-2]:
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"Ending table with {len(table_rows)} rows due to bold text")
                    MarkdownRenderer._add_table_to_elements(elements, table_rows, font_type=font_type, font_size_body=font_size_body)
                    table_rows = []
                    in_table = False
                text = line[2:-2].strip()
                elements.append(Paragraph(text, bold_style))
            elif line.startswith('* '):
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"Ending table with {len(table_rows)} rows due to bullet point")
                    MarkdownRenderer._add_table_to_elements(elements, table_rows, font_type=font_type, font_size_body=font_size_body)
                    table_rows = []
                    in_table = False
                # Handle bullet points
                text = line[2:].strip()
                # Process bold text within bullet points
                processed_text = MarkdownRenderer._process_bold_text(text)
                bullet_text = f"• {processed_text}"
                elements.append(Paragraph(bullet_text, bullet_style))
            elif line.startswith('---'):
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"Ending table with {len(table_rows)} rows due to horizontal rule")
                    MarkdownRenderer._add_table_to_elements(elements, table_rows, font_type=font_type, font_size_body=font_size_body)
                    table_rows = []
                    in_table = False
                elements.append(Spacer(1, 20))
            elif line.startswith('| ') and '|' in line[2:]:
                # Handle table rows
                table_data = [cell.strip() for cell in line.split('|')[1:-1]]
                if table_data:
                    logger.info(f"Adding table row: {table_data}")
                    table_rows.append(table_data)
                    in_table = True
            elif line.startswith('|---') or line.startswith('| ---') or line.startswith('|:---') or line.startswith('| :---'):
                # Skip table separator lines but keep table context
                logger.info("Skipping table separator line")
                pass
            elif in_table and '|' in line:
                # Handle table rows that don't start with | (incomplete tables)
                table_data = [cell.strip() for cell in line.split('|')]
                if len(table_data) > 1:  # At least 2 cells (empty first cell + content)
                    # Remove empty first and last cells if they exist
                    if table_data[0].strip() == '':
                        table_data = table_data[1:]
                    if table_data and table_data[-1].strip() == '':
                        table_data = table_data[:-1]
                    if table_data:
                        logger.info(f"Adding incomplete table row: {table_data}")
                        table_rows.append(table_data)
            else:
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"Ending table with {len(table_rows)} rows due to regular text")
                    MarkdownRenderer._add_table_to_elements(elements, table_rows, font_type=font_type, font_size_body=font_size_body)
                    table_rows = []
                    in_table = False
                if line:  # Only add non-empty lines
                    # Process bold text within the line
                    processed_line = MarkdownRenderer._process_bold_text(line)
                    elements.append(Paragraph(processed_line, normal_style))
            
            i += 1
        
        # Add any remaining table
        if in_table and table_rows:
            logger.info(f"Adding final table with {len(table_rows)} rows")
            MarkdownRenderer._add_table_to_elements(elements, table_rows, font_type=font_type, font_size_body=font_size_body)
        
        logger.info(f"Generated {len(elements)} ReportLab elements")
        return elements

    @staticmethod
    def convert_draft_to_markdown(
        draft_list: List[Dict[str, Any]],
        opportunity_details=None,
        user_details=None,
        tenant_details=None
    ) -> str:
        """
        Convert draft list to markdown format.
        This is a placeholder implementation that mimics the PDF export functionality.
        """
        try:
            markdown_content = []
            
            logger.info(f"Converting draft with {len(draft_list)} sections")
            
            # Process draft content
            for i, section in enumerate(draft_list, 1):
                logger.info(f"Processing section {i}: {type(section)}")
                
                if isinstance(section, dict):
                    # Extract title
                    title = section.get('title', f'Section {i}')
                    logger.info(f"Section title: {title}")
                    markdown_content.append(f"## {title}")
                    
                    # Extract content
                    content = section.get('content', section.get('text', ''))
                    if content:
                        logger.info(f"Section content length: {len(content)} characters")
                        # Check if content is truncated
                        if len(content) > 1000:
                            logger.info(f"Content preview: {content[:200]}...")
                        markdown_content.append(content)
                    else:
                        logger.warning(f"No content found for section {i}")
                    
                    # Add any additional fields (excluding title, content, text, and subsections which are handled separately)
                    '''
                    for key, value in section.items():
                        if key not in ['title', 'content', 'text', 'subsections'] and value:
                            logger.info(f"Adding additional field: {key}")
                            if isinstance(value, dict):
                                markdown_content.append(f"**{key.title()}:**")
                                import json
                                markdown_content.append(f"```json\n{json.dumps(value, indent=2)}\n```")
                            elif isinstance(value, list) and all(isinstance(item, dict) for item in value):
                                # Handle list of objects as structured data
                                markdown_content.append(f"**{key.title()}:**")
                                markdown_content.append(f"```json\n{json.dumps(value, indent=2)}\n```")
                            else:
                                markdown_content.append(f"**{key.title()}:** {value}")
                    '''

                    # Handle subsections specially
                    subsections = section.get('subsections', [])
                    if subsections and isinstance(subsections, list):
                        logger.info(f"Processing {len(subsections)} subsections")
                        for j, subsection in enumerate(subsections, 1):
                            if isinstance(subsection, dict):
                                sub_title = subsection.get('title', f'Subsection {j}')
                                logger.info(f"Subsection {j} title: {sub_title}")
                                markdown_content.append(f"### {sub_title}")
                                
                                sub_content = subsection.get('content', subsection.get('text', ''))
                                if sub_content:
                                    logger.info(f"Subsection {j} content length: {len(sub_content)} characters")
                                    markdown_content.append(sub_content)
                                else:
                                    logger.warning(f"No content found for subsection {j}")
                                
                                # Add any additional subsection fields
                                '''
                                for sub_key, sub_value in subsection.items():
                                    if sub_key not in ['title', 'content', 'text'] and sub_value:
                                        logger.info(f"Adding subsection field: {sub_key}")
                                        if isinstance(sub_value, (list, dict)):
                                            markdown_content.append(f"**{sub_key.title()}:**")
                                            markdown_content.append(f"```json\n{json.dumps(sub_value, indent=2)}\n```")
                                        else:
                                            markdown_content.append(f"**{sub_key.title()}:** {sub_value}")
                                '''
                                markdown_content.append("")
                            else:
                                logger.warning(f"Subsection {j} is not a dict: {type(subsection)}")
                    elif subsections:
                        logger.warning(f"Subsections is not a list: {type(subsections)}")
                    
                    markdown_content.append("")
                else:
                    # Handle non-dict items
                    logger.warning(f"Section {i} is not a dict: {type(section)}")
                    markdown_content.append(f"## Section {i}")
                    markdown_content.append(str(section))
                    markdown_content.append("")

            result = "\n".join(markdown_content)
            logger.info(f"Generated markdown with {len(result)} characters")
            return result

        except Exception as e:
            logger.error(f"Error converting draft to markdown: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return f"# Error Converting Draft\n\nFailed to convert draft to markdown: {str(e)}" 