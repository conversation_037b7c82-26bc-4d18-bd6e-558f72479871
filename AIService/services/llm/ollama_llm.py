
from services.llm.llm_factory import get_llm

class OllamaLLMService:
     """
     Service for using the LLM wrapper (now supports both Ollama and Gemini)
     """
     def __init__(self):
          self.llm_api_url: str = "http://ai.kontratar.com:11434",
          # Use the LLM factory to get the configured LLM
          self.llm = get_llm(temperature=0.0)
          self.image_system_prompt = """
          You are an expert at describing images in detail. Provide a concise but comprehensive description of the image.
          """
       
     def chart_image_prompt(self, user_prompt: str) -> str:
         """
         Generate a prompt response for creating charts, flowcharts, or business images
         that can aid in the quality generation for government proposals.
         
         Args:
             user_prompt (str): The user's input prompt describing the desired visual.
         
         Returns:
             str: The generated response from the LLM.
         """
         chart_system_prompt = """
             You are a mermaid generator. You are specializing in creating high-quality, clear, and informative charts, flowcharts, and business images tailored for government proposals.
             Your visuals should enhance proposal clarity, professionalism, and persuasiveness.
             
            **Mermaid Diagram Rules**
            Create a state chart using mermaid from the provided text input. 
            Do not use any quote marks or any special characters that is not supported by mermaid version 11.3.0.
            Do not include ```mermaid or ``` code blocks, Just provide the Mermaid code for the diagram.
            Do not provide comments about the code.
                
            Example of Flowcxhart code:
            
            flowchart TD
            A[Christmas] -->| Get money| B(Go shopping)
            B--> C{ Let me think }
            C-->| One | D[Laptop]
            C-->| Two | E[iPhone]
            C-->| Three | F[Car]
            
            Example of Pie Chart code:
            pie title Pets adopted by volunteers
            "Dogs" : 386
            "Cats" : 85
            "Rats" : 15
            
            Example of a Quadrant Chart
            quadrantChart
            title Reach and engagement of campaigns
            x-axis Low Reach --> High Reach
            y-axis Low Engagement --> High Engagement
            quadrant-1 We should expand
            quadrant-2 Need to promote
            quadrant-3 Re-evaluate
            quadrant-4 May be improved
            Campaign A: [0.3, 0.6]
            Campaign B: [0.45, 0.23]
            Campaign C: [0.57, 0.69]
            Campaign D: [0.78, 0.34]
            Campaign E: [0.40, 0.34]
            Campaign F: [0.35, 0.78]
            
            Example of a Sequence Diagram
            sequenceDiagram
            Alice->>John: Hello John, how are you?
            John-->>Alice: Great!
            Alice-)John: See you later!
            
            Example of State Diagram:
            stateDiagram-v2
            [*] --> Still
            Still --> [*]

            Still --> Moving
            Moving --> Still
            Moving --> Crash
            Crash --> [*]
            
            Example of User Journey
            journey
            title My working day
            section Go to work
            Make tea: 5: Me
            Go upstairs: 3: Me
            Do work: 1: Me, Cat
            section Go home
            Go downstairs: 5: Me
            Sit down: 5: Me
            
            A mind map is a diagram used to visually organize information into a hierarchy, showing relationships among pieces of the whole. It is often created around a single concept, drawn as an image in the center of a blank page, to which associated representations of ideas such as images, words and parts of words are added. Major ideas are connected directly to the central concept, and other ideas branch out from those major ideas.
            Example of Mind Map Diagram
            mindmap
            root((mindmap))
                Origins
                Long history
                ::icon(fa fa-book)
                Popularisation
                    British popular psychology author Tony Buzan
                Research
                On effectiveness<br/>and features
                On Automatic creation
                    Uses
                        Creative techniques
                        Strategic planning
                        Argument mapping
                Tools
                Pen and paper
                Mermaid


            A timeline is a type of diagram used to illustrate a chronology of events, dates, or periods of time.
            Example of Timeline Diagram:
            timeline
            title History of Social Media Platform
            2002 : LinkedIn
            2004 : Facebook
                : Google
            2005 : YouTube
            2006 : Twitter
            
            Example of Gantt Diagram
            gantt
            title A Gantt Diagram
            dateFormat YYYY-MM-DD
            section Section
                A task          :a1, 2014-01-01, 30d
                Another task    :after a1, 20d
            section Another
                Task in Another :2014-01-12, 12d
                another task    :24d

            **Generate Visual Rules**
             When generating a visual, always:
             - Choose the most effective chart or diagram type for the information from these types:
               1. Flowchart
               2. Process Diagram
               3. Org Chart
               4. Mind Map
               5. Journey Map
               6. Timeline
               7. Gantt Chart
             - Use concise, descriptive labels and titles.
             - Ensure the visual is easy to interpret for government reviewers.
             - Highlight key data, relationships, or processes relevant to the proposal.
             - Suggest color schemes and layout for maximum clarity and accessibility.
             - If the user prompt is ambiguous, ask clarifying questions or make reasonable assumptions.
             - I WANT A PURE MERMAIDJS GRAPH GENERATED, NO ADDITIONAL TEXT OUTSIDE THE GRAPH DATA.
             - Add Icons where needed, you can use any of the 200,000+ icons available in iconify.design
             
             NOTE: ENSURE THE DIAGRAM IS CORRECT AND HAS NO FORMSTTING ISSUES ON BUILD
         """
         
         try:
             messages = [
                 {"role": "system", "content": chart_system_prompt},
                 {"role": "user", "content": user_prompt}
             ]
             response = self.llm.invoke(messages)
             return response.content
         except Exception as e:
             raise RuntimeError(f"Error generating chart/image prompt response: {e}")
         
     async def prompt(self, user_prompt: str, img_bytes: bytes = None) -> str:
         """
         Generate a prompt response using the Ollama LLM.
         
         Args:
             system_prompt (str): The system prompt defining the LLM's role and context.
             user_prompt (str): The user's input prompt.
             img_bytes (bytes, optional): Base64 encoded image bytes for image analysis. Defaults to None.
         
         Returns:
             str: The generated response from the LLM.
         """
         try:
             # Prepare messages for the LLM
             messages = [
                 {"role": "system", "content": self.image_system_prompt},
                 {"role": "user", "content": user_prompt}
             ]
             
             # If image is provided, convert to base64 and append to user prompt
             if img_bytes:
                 import base64
                 img_base64 = base64.b64encode(img_bytes).decode('utf-8')
                 messages[-1]["content"] += f"\n[Image: {img_base64}]"
             
             # Invoke the LLM and return the generated content
             response = await self.llm.ainvoke(messages)
             return response.content
         
         except Exception as e:
             # Log or handle any errors during LLM invocation
             raise RuntimeError(f"Error generating prompt response: {e}")