import httpx
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple


from utils.hashing import generate_hash
from controllers.kontratar.chromadb_mapping_controller import ChromaDBMappingController

from loguru import logger
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from utils.embedding_model import Ko<PERSON><PERSON>rEmbeddings
from urllib.parse import urlparse
import random


def create_chroma_client_from_url(url):
    """Create a Chroma client from a URL"""
    parsed = urlparse(url)
    
    # Extract components
    host = parsed.hostname
    port = parsed.port
    scheme = parsed.scheme
    
    return host, port


# Utility functions for extracting IDs from collection names
def extract_opportunity_id(collection_name: str) -> str:
    logger.debug(f"extract_opportunity_id called with collection_name: {collection_name} (type: {type(collection_name)})")
    base_name = collection_name.split(".")[0]
    logger.debug(f"base_name: {base_name} (type: {type(base_name)})")
    parts = base_name.split("_")
    logger.debug(f"parts: {parts} (type: {type(parts)})")
    if len(parts) == 1:
        result = parts[0]
        logger.debug(f"Returning single part result: {result} (type: {type(result)})")
        return result
    elif len(parts) >= 2:
        result = parts[1]
        logger.debug(f"Returning second part result: {result} (type: {type(result)})")
        return result
    raise ValueError(f"Invalid collection name format: {collection_name}")

def extract_tenant_id(collection_name: str) -> str:
    logger.debug(f"extract_tenant_id called with collection_name: {collection_name} (type: {type(collection_name)})")
    base_name = collection_name.split(".")[0]
    logger.debug(f"base_name: {base_name} (type: {type(base_name)})")
    parts = base_name.split("_")
    logger.debug(f"parts: {parts} (type: {type(parts)})")
    if len(parts) == 1:
        result = "SYSTEM"
        logger.debug(f"Returning default SYSTEM result: {result} (type: {type(result)})")
        return result
    elif len(parts) >= 2:
        result = parts[0]
        logger.debug(f"Returning first part result: {result} (type: {type(result)})")
        return result
    raise ValueError(f"Invalid collection name format: {collection_name}")

def extract_suffix(collection_name: str) -> Optional[str | int]:
    logger.debug(f"extract_suffix called with collection_name: {collection_name} (type: {type(collection_name)})")
    parts = collection_name.split(".")
    logger.debug(f"Split collection_name into parts: {parts} (length: {len(parts)})")

    if len(parts) <= 1:
        logger.info(f"No suffix found in collection_name: {collection_name}")
        return None

    try:
        suffix = int(parts[-1])
        logger.info(f"Extracted numeric suffix: {suffix} from collection_name: {collection_name}")
        return suffix
    except Exception as e:
        logger.info(f"Suffix is not numeric, returning as string: {parts[-1]} from collection_name: {collection_name}")
        return parts[-1]

        

class ChromaService:
    def __init__(self, embedding_api_url: str, embedding_api_key: Optional[str] = None):
        self.embeddings = KontratarEmbeddings(embedding_api_url, embedding_api_key)
        self.chroma_instance_service = ChromaDBMappingController()
        #self.embeddings = OllamaEmbeddings(model = "nomic-embed-text:latest", base_url = "http://ai.kontratar.com:11434")

    async def get_chroma_url(self, db: AsyncSession, unique_id: str, tenant_id: str) -> Optional[str]:
        return await ChromaDBMappingController.get_chromadb_url(db, unique_id, tenant_id)

    async def get_relevant_chunks(self, db: AsyncSession, collection_name: str, query: str, n_results: int = 5) -> List[str]:

        result = await self.get_relevant_chunks_rest(db, collection_name, query, n_results)
        if result is None:
            return []

        return result
    
    async def convert_string_to_embeddings(self, text: str) -> List[float]:
        return self.embeddings.embed_query(text)
    
    async def create_collection(self, chroma_url: str, collection_name: str, collection_metadata: Dict[str, Any]) -> Optional[str]:
        existing_collection_id = await self.get_collection_id(chroma_url, collection_name)

        if existing_collection_id is not None:
            return existing_collection_id
        
        timeout = httpx.Timeout(30.0)  
        async with httpx.AsyncClient(timeout=timeout) as client:
            try:
                collections_url = f"{chroma_url}/api/v1/collections"
                payload = {
                    "name": collection_name,
                    "configuration": {},
                    "metadata": collection_metadata,
                    "get_or_create": False
                }
                resp = await client.post(collections_url, json=payload)
                resp.raise_for_status()
                collection_id = resp.json()["id"]
                return collection_id
            except Exception as e:
                logger.error(f"Error creating collection '{collection_name}' at {chroma_url}: {e}")
                return None

    async def add_documents_to_collection(self, chroma_url: str, collection_id: str, documents: List[str], embeddings: List[List[float]], metadatas: Optional[List[Dict[str, Any]]]) -> Optional[Tuple[List[str], List[List[float]]]]:
        try:
            logger.info(f"Preparing to add {len(documents)} documents to collection {collection_id}")
            # Generate random string ids for each document
            ids = [str(uuid.uuid4()) for _ in documents]
            logger.debug(f"Generated document IDs: {ids}")

            timeout = httpx.Timeout(30.0)  
            async with httpx.AsyncClient(timeout=timeout) as client:
                collections_url = f"{chroma_url}/api/v1/collections/{collection_id}/add"
                payload = {
                    "ids": ids,
                    "documents": documents,
                    "embeddings": embeddings,
                    "metadatas": metadatas
                }
                logger.debug(f"POST {collections_url} with payload {payload} ")
                
                try:
                    resp = await client.post(collections_url, json=payload)
                    logger.debug(f"Response status code: {resp}, {resp.status_code}, Response text: {resp.text}")
                    resp.raise_for_status()
                    logger.info(f"Successfully added {len(documents)} documents to collection {collection_id}")
                except httpx.HTTPStatusError as e:
                    logger.error(f"HTTP error adding documents to collection {collection_id}: {e} - Response: {resp.text}")
                    raise
            return documents, embeddings
        except Exception as e:
            logger.error(f"Error adding documents to collection {collection_id}: {e}")
            return None
            
    async def select_least_loaded_instance(self, db) -> str:
        """
        Select the ChromaDB instance with the least number of active collections.
        """
        # List of all available ChromaDB instance URLs (could come from config or DB)
        from config import settings
        chroma_instance_urls = settings.chromadb_instance_urls
        logger.info(f"ChromaDB instance URLs: {chroma_instance_urls}")

        min_collections = float('inf')
        eligible_instances = []
        instance_collection_counts = {}

        # Count active collections for each instance
        for instance_url in chroma_instance_urls:
            count = await self.count_active_collections_by_instance_url(db, instance_url)
            instance_collection_counts[instance_url] = count
            if count < min_collections:
                min_collections = count
            eligible_instances.append(instance_url)

        if not eligible_instances:
            raise RuntimeError("No available ChromaDB instances")

        # Filter instances within 20% of the minimum load
        balanced_instances = [
            url for url in eligible_instances
            if instance_collection_counts[url] <= min_collections * 1.2
        ]

        # Randomly select from balanced instances
        if balanced_instances:
            selected_instance = random.choice(balanced_instances)
        else:
            selected_instance = random.choice(eligible_instances)

        logger.info(f"Selected ChromaDB instance {selected_instance} (min collections: {min_collections})")
        return selected_instance

    async def count_active_collections_by_instance_url(self, db, instance_url: str) -> int:
        """
        Count the number of active collections for a given ChromaDB instance URL.
        """
        from models.kontratar_models import ChromaDBInstanceMapping
        from sqlalchemy import select, func

        query = select(func.count()).select_from(ChromaDBInstanceMapping).where(
            ChromaDBInstanceMapping.chroma_instance_url == instance_url,
            ChromaDBInstanceMapping.status == "ACTIVE"
        )
        result = await db.execute(query)
        return result.scalar() or 0

    # async def ensure_collection_exists(self, db, collection_name: str):
    #     """
    #     Ensure the collection and its mapping exist, creating them if needed.
    #     """
    #     from controllers.kontratar.chromadb_mapping_controller import ChromaDBMappingController
    #     from models.kontratar_models import ChromaDBInstanceMapping
    #     from datetime import datetime

    #     # Remove version suffix for base collection name
    #     import re
    #     base_collection_name = re.sub(r"\.\d+$", "", collection_name)
    #     is_versioned = base_collection_name != collection_name

    #     unique_id = extract_opportunity_id(base_collection_name)
    #     tenant_id = extract_tenant_id(base_collection_name)

    #     mapping = await ChromaDBMappingController.get_by_unique_id_and_tenant(db, unique_id, tenant_id)

    #     if mapping:
    #         # Update last accessed date
    #         mapping.last_accessed_date = datetime.utcnow()
    #         await db.commit()

    #         # If versioned, ensure collection exists in same instance
    #         if is_versioned:
    #             chroma_url = mapping.chroma_instance_url
    #             if not await self.collection_exists(collection_name, chroma_url):
    #                 await self.create_collection(chroma_url, collection_name, {
    #             "tenant_id": tenant_id,
    #             "unique_id": unique_id
    #         })
    #         return mapping

    #     # No mapping found, create new mapping for base collection
    #     selected_instance = await self.select_least_loaded_instance(db)
    #     # Create new mapping
    #     from models.kontratar_models import ChromaDBInstanceMapping
    #     new_mapping = ChromaDBInstanceMapping(
    #         unique_id=unique_id,
    #         tenant_id=tenant_id,
    #         chroma_instance_url=selected_instance,
    #         collection_name=base_collection_name,
    #         created_date=datetime.utcnow(),
    #         status="ACTIVE"
    #     )
    #     db.add(new_mapping)
    #     await db.commit()
    #     await db.refresh(new_mapping)

    #     # Create collection in selected instance
    #     await self.create_collection(selected_instance, collection_name, {
    #             "tenant_id": tenant_id,
    #             "unique_id": unique_id
    #         })

    #     return new_mapping

    async def add_documents(self, db: AsyncSession, collection_name: str, documents: List[str], metadatas: Optional[List[Dict[str, Any]]] = None) -> Optional[Tuple[List[str], List[List[float]]]]:
        try:
            logger.info(f"Starting to add documents to collection: {collection_name}")
    

            
            unique_id = extract_opportunity_id(collection_name)
            tenant_id = extract_tenant_id(collection_name)
            logger.debug(f"Extracted unique_id: {unique_id}, tenant_id: {tenant_id}")

            chroma_instance = await self.chroma_instance_service.get_by_unique_id_and_tenant(db, unique_id, tenant_id)
            
            if chroma_instance is None:
                selected_instance = await self.select_least_loaded_instance(db)
                # Create new mapping
                from models.kontratar_models import ChromaDBInstanceMapping
                chroma_instance = ChromaDBInstanceMapping(
                    unique_id=unique_id,
                    tenant_id=tenant_id,
                    chroma_instance_url=selected_instance,
                    collection_name=collection_name,
                    created_date=datetime.utcnow(),
                    status="ACTIVE"
                )
                db.add(chroma_instance)
                await db.commit()
                await db.refresh(chroma_instance)

              
                logger.info(f"Created new ChromaDB mapping for {unique_id}, {tenant_id} with collection name {collection_name}")
            else:
                logger.info(f"Found existing ChromaDB mapping for {unique_id}, {tenant_id} with collection name {chroma_instance.collection_name}")

            
            
            chroma_url = chroma_instance.chroma_instance_url

            db_collection_name = str(chroma_instance.collection_name)

            if db_collection_name == f"{tenant_id}_{unique_id}" or db_collection_name == unique_id:
                logger.info(f"Creating hash for collection name")
                db_collection_name = generate_hash(collection_name)
                
                
                logger.info(f"Generated new collection name: {db_collection_name} for unique_id: {unique_id}, tenant_id: {tenant_id}")

                ## Update new collection name with hash value
                await self.chroma_instance_service.update_by_unique_id_and_tenant(
                    db, unique_id, tenant_id, collection_name = db_collection_name,
                )

            if chroma_url is None:
                new_instance = await self.select_least_loaded_instance(db)
                # Update the chroma_instance with the new instance URL
                chroma_instance.chroma_instance_url = new_instance
                await db.commit()
                await db.refresh(chroma_instance)
                chroma_url = new_instance
            else:
                logger.debug(f"Using existing ChromaDB instance URL: {chroma_url}")    

            logger.debug(f"Retrieved ChromaDB URL: {chroma_url}")
            chroma_url = str(chroma_url)

            collection_version = int(str(chroma_instance.version)) + 1 if chroma_instance.version is not None else 0
            latest_collection_name = db_collection_name

            if collection_version > 0:
                latest_collection_name = db_collection_name + "." + str(collection_version)
                logger.debug(f"Using versioned collection name: {latest_collection_name}")

            if not chroma_url:
                logger.warning(f"No ChromaDB mapping found for {unique_id}, {tenant_id}")
                return None
    
            
            logger.info(f"Generating embeddings for {len(documents)} documents")
            embeddings = self.embeddings.embed_documents(documents)
            logger.debug(f"Generated {len(embeddings)} embeddings")
            
            # Chroma's add_texts expects texts and optional metadatas
            logger.info(f"Adding {len(documents)} documents to ChromaDB collection")
            collection_metadata = {
                "tenant_id": tenant_id,
                "unique_id": unique_id
            }

            logger.debug(f"Creating collection {latest_collection_name} with metadata: {collection_metadata}")
            collection_id = await self.create_collection(chroma_url, latest_collection_name, collection_metadata)
            logger.info(f"Collection ID for {chroma_url} , {latest_collection_name}: {collection_id}")

            #vectorstore.add_texts(documents, metadatas=metadatas, embeddings=embeddings)
            await self.add_documents_to_collection(chroma_url, collection_id, documents, embeddings, metadatas)

                ## Update Chroma Version in database
            await self.chroma_instance_service.update_version(db, unique_id, tenant_id, collection_version)
            
            logger.info(f"Successfully added documents to collection: {latest_collection_name}")
            return documents, embeddings
        except Exception as e:
            logger.error(f"Error in add_documents: {e}")
            return None

    async def ensure_collection_exists(self, db: AsyncSession, collection_name: str) -> bool:
        logger.debug(f"ensure_collection_exists called with collection_name: {collection_name}")
        unique_id = extract_opportunity_id(collection_name)
        tenant_id = extract_tenant_id(collection_name)
        logger.info(f"Extracted unique_id: {unique_id}, tenant_id: {tenant_id} from collection_name: {collection_name}")
        chroma_url = await self.get_chroma_url(db, unique_id, tenant_id)
        if not chroma_url:
            logger.warning(f"No ChromaDB mapping found for {unique_id}, {tenant_id}")
            return False
        
        logger.debug(f"Checking if collection exists for collection_name: {collection_name} at chroma_url: {chroma_url}")
        exists = await self.collection_exists(collection_name, chroma_url)
        logger.info(f"Collection '{collection_name}' existence: {exists}")
        return exists

    async def list_collections(self, db: AsyncSession, unique_id: str, tenant_id: str) -> List[str]:
        chroma_url = await self.get_chroma_url(db, unique_id, tenant_id)
        if not chroma_url:
            logger.warning(f"No ChromaDB mapping found for {unique_id}, {tenant_id}")
            return []

        return []

    async def get_collection_information_by_name(self, db: AsyncSession, collection_name: str) -> Optional[Dict[str, Any]]:
        # Not directly supported in LangChain, but can be implemented via ChromaDB REST API if needed
        return None

    async def delete_collection(self, db: AsyncSession, collection_name: str) -> bool:
        unique_id = extract_opportunity_id(collection_name)
        tenant_id = extract_tenant_id(collection_name)
        chroma_url = await self.get_chroma_url(db, unique_id, tenant_id)
        if not chroma_url:
            logger.warning(f"No ChromaDB mapping found for {unique_id}, {tenant_id}")
            return False

        return True

    async def update_collection(self, db: AsyncSession, collection_name: str, new_metadata: Dict[str, Any]) -> bool:
        # Not directly supported in LangChain, but can be implemented via ChromaDB REST API if needed
        return False

    async def delete_collection_content(self, db: AsyncSession, collection_name: str) -> bool:
        # Not directly supported in LangChain, but can be implemented via ChromaDB REST API if needed
        return False
    
    async def collection_exists(self, collection_name: str, chroma_url: str) -> bool:
        logger.debug("Entering the function - collection_exists()")
        try:
            # Assume collection_name is already hashed and versioned if needed
            url = f"{chroma_url}/api/v1/collections/{collection_name}"
            timeout = httpx.Timeout(30.0)
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(url)
                logger.info(f"Collection existence check for {collection_name}: status {response.status_code}")
                return 200 <= response.status_code < 300
        except Exception as e:
            logger.error(f"Error checking collection existence {collection_name}: {e}")
            return False
        finally:
            logger.debug("Exiting the function - collection_exists()")

    async def get_current_collection_version(self, base_collection_name: str, chroma_url: str) -> int:
        logger.info(f"Finding current version for collection base name: {base_collection_name}")
        current_version = -1  # Start at -1 to indicate no versions exist

        try:
            # First check if base collection exists (no version suffix)
            hashed_base = generate_hash(base_collection_name)
            if await self.collection_exists(hashed_base, chroma_url):
                logger.info(f"Base collection {base_collection_name} exists")
                current_version = 0  # Base collection is considered version 0
            else:
            # Check for numbered versions starting from 1 (limit to 1000 versions max)
                version_to_check = 1
                version_exists = True
                max_versions = 1000

                while version_exists and version_to_check <= max_versions:
                    versioned_name = f"{hashed_base}.{version_to_check}"
                    if await self.collection_exists(versioned_name, chroma_url):
                        current_version = version_to_check
                        version_to_check += 1
                    else:
                        version_exists = False

                if version_to_check > max_versions:
                    logger.warning(f"Reached maximum version limit ({max_versions}) for {base_collection_name}")

            logger.info(f"Current version found for {base_collection_name}: {current_version}")
            return current_version
        except Exception as e:
            logger.error(f"Error finding current collection version for {base_collection_name}: {e}")
            return -1  # Return -1 if there's an error, indicating no collections exist

    async def get_next_collection_version(self, base_collection_name: str, chroma_url: str) -> int:
        logger.info(f"Finding next version for collection base name: {base_collection_name}")
        current_version = await self.get_current_collection_version(base_collection_name, chroma_url)
        next_version = 0 if current_version == -1 else current_version + 1
        logger.info(f"Next version for {base_collection_name} is {next_version}")
        return next_version

    async def get_latest_collection_name(self, base_collection_name: str, chroma_url: str) -> Optional[str]:
        """
        Returns the name of the latest versioned collection for a base name, or None if none exist.
        """
        logger.info(f" got here! Getting latest collection name for base name: {base_collection_name}")
        try:
            logger.info(f"Finding latest collection version for base name: {base_collection_name}")
            current_version = await self.get_current_collection_version(base_collection_name, chroma_url)
            if current_version == -1:
                logger.info(f"No collection exists for base name: {base_collection_name}")
                return None
            elif current_version == 0:
                logger.info(f"Latest collection is for base {base_collection_name} is hashed collection: {generate_hash(base_collection_name)}")
                return generate_hash(base_collection_name)
            else:
                latest_name = f"{generate_hash(base_collection_name)}.{current_version}"
                logger.info(f"Latest collection is: {latest_name}")
                return latest_name
        except Exception as e:
            logger.error(f"Error finding latest collection version for {base_collection_name}: {e}")
            return None

    async def get_relevant_chunks_rest(
        self,
        db: AsyncSession,
        collection_name: str,
        query: str,
        n_results: int = 5
    ) -> Optional[List[str]]:
        """
        Retrieve relevant chunks from ChromaDB using the REST API.
        """
        import asyncio

        try:
            return await asyncio.wait_for(
                self._get_relevant_chunks_rest_impl(db, collection_name, query, n_results),
                timeout=45.0
            )
        except asyncio.TimeoutError:
            logger.warning(f"ChromaDB operation timeout for collection {collection_name} - continuing without context")
            return []
        except Exception as e:
            logger.warning(f"ChromaDB operation error for collection {collection_name}: {e} - continuing without context")
            return []

    async def _get_relevant_chunks_rest_impl(
        self,
        db: AsyncSession,
        collection_name: str,
        query: str,
        n_results: int = 5
    ) -> Optional[List[str]]:
        """
        Internal implementation of ChromaDB retrieval.
        """
        unique_id = extract_opportunity_id(collection_name)
        tenant_id = extract_tenant_id(collection_name)
        suffix = extract_suffix(collection_name)

        if suffix and isinstance(suffix, str):
            unique_id = unique_id + "." + suffix

        chroma_instance = await self.chroma_instance_service.get_by_unique_id_and_tenant(db, unique_id, tenant_id)
        
        if chroma_instance is None:
            logger.warning(f"No ChromaDB mapping url found for {unique_id}, {tenant_id}")
            return None

        chroma_url = chroma_instance.chroma_instance_url

        if chroma_url is None:
            return None

        chroma_url = str(chroma_url)
        if not chroma_url:
            logger.warning(f"No ChromaDB mapping url found for {unique_id}, {tenant_id}")
            return []


        logger.info(f"Building versioned_collection_name using collection_name: {chroma_instance.collection_name} and version: {chroma_instance.version}")
        versioned_collection_name = f"{chroma_instance.collection_name}.{chroma_instance.version}"
        version_number = int(str(chroma_instance.version)) if chroma_instance.version is not None else None
        logger.info(f"Constructed versioned_collection_name: {versioned_collection_name}, version_number: {version_number}")
        logger.info(f" about to get latest collection name using chroma_url: {chroma_url} for collection_name: {collection_name}")
        latest_collection_name = versioned_collection_name if version_number is not None and version_number > 0 else \
        await self.get_latest_collection_name(collection_name, chroma_url)

        if latest_collection_name is None:
            logger.warning(f"No latest collection name found for {collection_name} at {chroma_url}")
            return None
        
        # 1. Get collection ID by name
        timeout = httpx.Timeout(15.0)
        async with httpx.AsyncClient(timeout=timeout) as client:
            collections_url = f"{chroma_url}/api/v1/collections/{latest_collection_name}"
            resp = await client.get(collections_url)
            resp.raise_for_status()
            collection_id = resp.json()["id"]
            
            if not collection_id:
                logger.warning(f"Collection {latest_collection_name} not found in ChromaDB")
                return []

            # 2. Query the collection for relevant chunks
            query_url = f"{chroma_url}/api/v1/collections/{collection_id}/query"
            query_embedding = self.embeddings.embed_query(query)
            payload = {
                "query_embeddings": [query_embedding],
                "n_results": n_results,
                "include": ["documents", "metadatas", "distances"]
            }
            query_resp = await client.post(query_url, json=payload)
            # Now you can optionally check for errors
            if query_resp.status_code >= 400:
                print(f"Error occurred with status code: {query_resp.status_code}")
                # You can still access the error response body
                try:
                    error_details = query_resp.json()
                    print(f"Error details: {error_details}")
                except:
                    print(f"Error response (not JSON): {query_resp.text}")

            results = query_resp.json()

            if chroma_instance.version is None:
                version = int(latest_collection_name.split(".")[-1])
                await self.chroma_instance_service.update_version(db, unique_id, tenant_id, version)
            
            #logger.debug(f"results: {results}")
            # The relevant chunks are in results["documents"][0]
            metadatas = results["metadatas"][0]
            documents = results["documents"][0]

            # Pair each document with its chunk_index
            paired = list(zip(metadatas, documents))

            # Sort by chunk_index
            paired_sorted = sorted(paired, key=lambda x: x[0]['chunk_index'])

            # Extract the documents in the new order
            documents_sorted = [doc for meta, doc in paired_sorted]

            if "documents" in results and results["documents"]:
                return documents_sorted
            else:
                logger.warning(f"No relevant documents found for query in collection {collection_name}")
                return []

    async def get_collection_id(self, chroma_url: str, collection_name: str) -> Optional[str]:
        timeout = httpx.Timeout(30.0)  
        async with httpx.AsyncClient(timeout=timeout) as client:
            collections_url = f"{chroma_url}/api/v1/collections/{collection_name}"
            resp = await client.get(collections_url)
            
            if resp.status_code >= 400:
                return None
                
            collection_id = resp.json()["id"]
            return collection_id
        
