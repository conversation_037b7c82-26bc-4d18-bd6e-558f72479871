import uuid
from datetime import datetime
from typing import List, Optional

from loguru import logger
from models.customer_models import ProposalQueue as CustomerProposalQueue
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession


class ProposalQueueService:
    """Service for handling proposal queue operations"""
    
    @staticmethod
    async def get_proposal_queue_items(db: AsyncSession, limit: int = 10) -> List[CustomerProposalQueue]:
        """Get new proposal queue items that have not been indexed (all toc fields are null)"""
        try:
            # Get all NEW items (more than needed, we'll filter in Python)
            query = select(CustomerProposalQueue).where(
                CustomerProposalQueue.status == "N"
            ).order_by(CustomerProposalQueue.creation_date.asc())
            result = await db.execute(query)
            all_items = list(result.scalars().all())
            logger.info(f"Fetched {len(all_items)} proposal queue items to filter.")

            picked = []
            for item in all_items:
                opps_id = item.opps_id
                logger.debug(f"Checking proposal queue item with opps_id: {opps_id}")
                if len(opps_id) == 32:
                    # Check main oppstable
                    opp_query = select(OppsTable).where(OppsTable.notice_id == opps_id)
                    opp_result = await db.execute(opp_query)
                    opp = opp_result.scalar_one_or_none()
                    if opp and any(
                        getattr(opp, f"toc_text{i}" if i > 1 else "toc_text") is not None
                        for i in range(1, 6)
                    ):
                        logger.info(f"Picked item (main oppstable) with opps_id: {opps_id}")
                        picked.append(item)
                    else:
                        logger.debug(f"Skipped item (main oppstable) with opps_id: {opps_id} (all toc fields null or not found)")
                else:
                    # Check custom_oppstable
                    opp_query = select(CustomOppsTable).where(CustomOppsTable.opportunity_id == opps_id)
                    opp_result = await db.execute(opp_query)
                    opp = opp_result.scalar_one_or_none()
                    if opp and any(
                        getattr(opp, f"toc_text_{i}" if i > 1 else "toc_text")
                        for i in range(1, 6)
                    ):
                        logger.info(f"Picked item (custom oppstable) with opps_id: {opps_id}")
                        picked.append(item)
                    else:
                        logger.debug(f"Skipped item (custom oppstable) with opps_id: {opps_id} (all toc fields null or not found)")
                if len(picked) >= limit:
                    break
            logger.info(f"Returning {len(picked)} proposal queue items after filtering.")
            return picked
        except Exception as e:
            logger.error(f"Error getting new proposal queue items: {e}")
            return []

    @staticmethod
    async def get_proposal_queue_item(db: AsyncSession, opportunity_id: str) -> List[CustomerProposalQueue]:
        """Get new proposal queue items"""
        try:
            query = select(CustomerProposalQueue).where(
                CustomerProposalQueue.opps_id == opportunity_id
            )
            
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting new proposal queue items: {e}")
            return []
    
    @staticmethod
    async def update_proposal_queue_status(
        db: AsyncSession, 
        job_id: str, 
        status: str, 
        next_state: Optional[str] = None
    ) -> bool:
        """Update proposal queue status"""
        try:
            query = update(CustomerProposalQueue).where(
                CustomerProposalQueue.job_id == job_id
            ).values(
                status=status,
                next_state=next_state
            )
            
            result = await db.execute(query)
            await db.commit()
            
            logger.info(f"Updated proposal queue status for job_id {job_id} to {status}")
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating proposal queue status: {e}")
            await db.rollback()
            return False
    
    @staticmethod
    async def create_proposal_queue_item(
        db: AsyncSession,
        job_instruction: str,
        opps_id: str,
        tenant_id: str,
        request_type: int,
        job_submitted_by: Optional[str] = None,
        next_state: Optional[str] = None
    ) -> Optional[CustomerProposalQueue]:
        """Create a new proposal queue item"""
        try:
            job_id = str(uuid.uuid4())
            new_item = CustomerProposalQueue(
                job_id=job_id,
                job_instruction=job_instruction,
                job_submitted_by=job_submitted_by,
                status="NEW",
                next_state=next_state,
                opps_id=opps_id,
                tenant_id=tenant_id,
                request_type=request_type,
                creation_date=datetime.utcnow()
            )
            
            db.add(new_item)
            await db.commit()
            await db.refresh(new_item)
            
            logger.info(f"Created new proposal queue item with job_id {job_id}")
            return new_item
        except Exception as e:
            logger.error(f"Error creating proposal queue item: {e}")
            await db.rollback()
            return None 