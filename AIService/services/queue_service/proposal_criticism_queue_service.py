from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, desc
from models.customer_models import ProposalCriticismQueue
from loguru import logger
import json


class ProposalCriticismQueueService:
    """Service for proposal criticism queue operations"""
    
    @staticmethod
    async def get_new_items(db: AsyncSession, limit: int = 10) -> List[ProposalCriticismQueue]:
        """Get new criticism queue items with status NEW"""
        try:
            query = select(ProposalCriticismQueue).where(
                ProposalCriticismQueue.status == "NEW"
            ).order_by(
                ProposalCriticismQueue.priority.asc(),
                ProposalCriticismQueue.creation_date.asc()
            ).limit(limit)
            
            result = await db.execute(query)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error(f"Error getting new criticism queue items: {e}")
            return []
    
    @staticmethod
    async def create_item(
        db: AsyncSession,
        opportunity_id: str,
        tenant_id: str,
        client_short_name: str,
        priority: int = 1,
        analysis_type: str = "full",
        submitted_by: Optional[str] = None
    ) -> Optional[ProposalCriticismQueue]:
        """Create a new criticism queue item"""
        try:
            existing_query = select(ProposalCriticismQueue).where(
                and_(
                    ProposalCriticismQueue.opportunity_id == opportunity_id,
                    ProposalCriticismQueue.tenant_id == tenant_id,
                    ProposalCriticismQueue.status.in_(["NEW", "PROCESSING"])
                )
            )
            
            existing_result = await db.execute(existing_query)
            existing_item = existing_result.scalars().first()
            
            if existing_item:
                logger.info(f"Criticism queue item already exists for opportunity {opportunity_id}")
                return existing_item
            
            new_item = ProposalCriticismQueue(
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                client_short_name=client_short_name,
                status="NEW",
                priority=priority,
                analysis_type=analysis_type,
                creation_date=datetime.now(),
                submitted_by=submitted_by
            )
            
            db.add(new_item)
            await db.commit()
            await db.refresh(new_item)
            
            logger.info(f"Created criticism queue item for opportunity {opportunity_id}")
            return new_item
            
        except Exception as e:
            await db.rollback()
            logger.error(f"Error creating criticism queue item: {e}")
            return None
    
    @staticmethod
    async def get_by_id(db: AsyncSession, item_id: int) -> Optional[ProposalCriticismQueue]:
        """Get criticism queue item by ID"""
        try:
            query = select(ProposalCriticismQueue).where(ProposalCriticismQueue.id == item_id)
            result = await db.execute(query)
            return result.scalars().first()
            
        except Exception as e:
            logger.error(f"Error getting criticism queue item {item_id}: {e}")
            return None
    
    @staticmethod
    async def update_status(
        db: AsyncSession,
        item_id: int,
        status: str,
        analysis_results: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
        processing_time: Optional[float] = None
    ) -> bool:
        """Update criticism queue item status"""
        try:
            item = await ProposalCriticismQueueService.get_by_id(db, item_id)
            if not item:
                return False
            
            item.status = status
            
            if status == "COMPLETED":
                item.completion_date = datetime.now()
                if analysis_results:
                    item.analysis_results = json.dumps(analysis_results)
                if processing_time:
                    item.processing_time_seconds = processing_time
            elif status == "FAILED":
                item.completion_date = datetime.now()
                if error_message:
                    item.error_message = error_message
                if processing_time:
                    item.processing_time_seconds = processing_time
            
            await db.commit()
            logger.info(f"Updated criticism queue item {item_id} status to {status}")
            return True
            
        except Exception as e:
            await db.rollback()
            logger.error(f"Error updating criticism queue item {item_id}: {e}")
            return False
    
    @staticmethod
    async def get_processing_items(db: AsyncSession) -> List[ProposalCriticismQueue]:
        """Get items currently being processed"""
        try:
            query = select(ProposalCriticismQueue).where(
                ProposalCriticismQueue.status == "PROCESSING"
            ).order_by(ProposalCriticismQueue.creation_date.asc())
            
            result = await db.execute(query)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error(f"Error getting processing criticism queue items: {e}")
            return []
    
    @staticmethod
    async def get_queue_summary(db: AsyncSession) -> Dict[str, Any]:
        """Get summary of queue status"""
        try:
            # Count items by status
            status_query = select(
                ProposalCriticismQueue.status,
                func.count(ProposalCriticismQueue.id).label('count')
            ).group_by(ProposalCriticismQueue.status)
            
            status_result = await db.execute(status_query)
            status_counts = {row.status: row.count for row in status_result}
            
            # Get total items
            total_query = select(func.count(ProposalCriticismQueue.id))
            total_result = await db.execute(total_query)
            total_count = total_result.scalar()
            
            # Get average processing time for completed items
            avg_time_query = select(
                func.avg(ProposalCriticismQueue.processing_time_seconds)
            ).where(
                and_(
                    ProposalCriticismQueue.status == "COMPLETED",
                    ProposalCriticismQueue.processing_time_seconds.isnot(None)
                )
            )
            
            avg_time_result = await db.execute(avg_time_query)
            avg_processing_time = avg_time_result.scalar()
            
            # Get oldest pending item
            oldest_query = select(ProposalCriticismQueue).where(
                ProposalCriticismQueue.status == "NEW"
            ).order_by(ProposalCriticismQueue.creation_date.asc()).limit(1)
            
            oldest_result = await db.execute(oldest_query)
            oldest_item = oldest_result.scalars().first()
            
            return {
                "total_items": total_count,
                "status_counts": status_counts,
                "average_processing_time_seconds": float(avg_processing_time) if avg_processing_time else None,
                "oldest_pending_item": {
                    "id": oldest_item.id,
                    "opportunity_id": oldest_item.opportunity_id,
                    "creation_date": oldest_item.creation_date.isoformat(),
                    "priority": oldest_item.priority
                } if oldest_item else None
            }
            
        except Exception as e:
            logger.error(f"Error getting criticism queue summary: {e}")
            return {}
    
    @staticmethod
    async def delete_item(db: AsyncSession, item_id: int) -> bool:
        """Delete a criticism queue item"""
        try:
            item = await ProposalCriticismQueueService.get_by_id(db, item_id)
            if not item:
                return False
            
            await db.delete(item)
            await db.commit()
            
            logger.info(f"Deleted criticism queue item {item_id}")
            return True
            
        except Exception as e:
            await db.rollback()
            logger.error(f"Error deleting criticism queue item {item_id}: {e}")
            return False
    
    @staticmethod
    async def get_items_by_tenant(
        db: AsyncSession,
        tenant_id: str,
        status: Optional[str] = None,
        limit: int = 50
    ) -> List[ProposalCriticismQueue]:
        """Get criticism queue items for a specific tenant"""
        try:
            query = select(ProposalCriticismQueue).where(
                ProposalCriticismQueue.tenant_id == tenant_id
            )
            
            if status:
                query = query.where(ProposalCriticismQueue.status == status)
            
            query = query.order_by(desc(ProposalCriticismQueue.creation_date)).limit(limit)
            
            result = await db.execute(query)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error(f"Error getting criticism queue items for tenant {tenant_id}: {e}")
            return []
    
    @staticmethod
    async def cleanup_old_completed_items(db: AsyncSession, days: int = 30) -> int:
        """Clean up old completed items"""
        try:
            from datetime import timedelta
            
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Delete old completed items
            delete_query = select(ProposalCriticismQueue).where(
                and_(
                    ProposalCriticismQueue.status.in_(["COMPLETED", "FAILED"]),
                    ProposalCriticismQueue.completion_date < cutoff_date
                )
            )
            
            result = await db.execute(delete_query)
            items_to_delete = result.scalars().all()
            
            for item in items_to_delete:
                await db.delete(item)
            
            await db.commit()
            
            deleted_count = len(items_to_delete)
            logger.info(f"Cleaned up {deleted_count} old criticism queue items")
            return deleted_count
            
        except Exception as e:
            await db.rollback()
            logger.error(f"Error cleaning up old criticism queue items: {e}")
            return 0
