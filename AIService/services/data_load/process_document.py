from typing import List, Optional, Tuple, List, Any
import re

from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_experimental.text_splitter import <PERSON>mantic<PERSON>hun<PERSON>
from database import get_kontratar_db
from utils.embedding_model import KontratarEmbeddings
from services.chroma.chroma_service import ChromaService
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession


class DocumentProcessingService:
    """
    Service for processing and semantically chunking large text corpora for vector databases.
    """

    @staticmethod
    def process_corpus(text: str) -> str:
        """
        Preprocess the text corpus (e.g., cleaning, normalization).
        Args:
            text (str): The raw text corpus.
        Returns:
            str: The cleaned/processed text.
        """
        logger.info("Starting corpus processing")
        
        # Normalize line endings and strip leading/trailing whitespace
        processed = text.replace('\r\n', '\n').strip()
        logger.debug("Normalized line endings and stripped whitespace")
        
        # Remove page number formats like "Page 1" or "Page 1 of X" (case-insensitive)
        processed = re.sub(r'(?i)Page\s+\d+(\s+of\s+\d+)?', '', processed)
        logger.debug("Removed page number formats")
        
        # Remove unnecessary multiple newlines (replace 2+ newlines with a single newline)
        processed = re.sub(r'\n{2,}', '\n', processed)
        logger.debug("Normalized multiple newlines")
        
        # Add more cleaning steps as needed
        logger.info(f"Corpus processing completed. Original length: {len(text)}, Processed length: {len(processed)}")
        return processed

    @staticmethod
    def semantic_chunk(text: str, chunk_size: int = 1000, chunk_overlap: int = 200) -> List[str]:
        """
        Chunk the text into semantically meaningful pieces using LangChain.
        Args:
            text (str): The processed text corpus.
            chunk_size (int): Max size of each chunk.
            chunk_overlap (int): Overlap between chunks.
        Returns:
            List[str]: List of text chunks.
        """
        logger.info(f"Starting semantic chunking with chunk_size={chunk_size}, chunk_overlap={chunk_overlap}")
        
        text_splitter = SemanticChunker(KontratarEmbeddings("http://ai.kontratar.com:5000"), min_chunk_size=chunk_size)
        logger.debug("Created SemanticChunker instance")
        
        docs = text_splitter.create_documents([text])
        logger.debug(f"Created {len(docs)} documents from text")
        
        chunks = [doc.page_content for doc in docs]
        logger.info(f"Semantic chunking completed. Created {len(chunks)} chunks")
        
        return chunks
        
    @staticmethod
    async def add_documents(collection_name: str, documents: List[str]) -> Optional[Tuple[List[str], Any]]:
        logger.info(f"Starting to add {len(documents)} documents to collection: {collection_name}")
        
        # Create metadata list with chunk_index for each document
        metadatas = [{"chunk_index": idx} for idx in range(len(documents))]
        logger.debug(f"Created metadata for {len(metadatas)} documents")
        
        chroma_service = ChromaService("http://ai.kontratar.com:5000")

        async for db in get_kontratar_db():
            result = await chroma_service.add_documents(db, collection_name, documents, metadatas)
            break

        if result is not None:
            logger.info(f"Successfully added {len(documents)} documents to new version of collection with base: {collection_name}")
            return result
        
        logger.info(f"Could not add {len(documents)} documents to new version of collection with base: {collection_name}")
        return None


# Example usage:
# text = open("large_corpus.txt").read()
# processed = DocumentProcessingService.process_corpus(text)
# chunks = DocumentProcessingService.semantic_chunk(processed)
