import asyncio
import json
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from sqlalchemy import select, and_, desc, func
from database import get_customer_db
from loguru import logger
from services.proposal.criticism_chain import <PERSON><PERSON><PERSON><PERSON>hain
from services.scheduler_service.schedule_lock_service import ScheduleLockService
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from controllers.customer.proposal_criticism_controller import ProposalCriticismController
from services.queue_service.proposal_criticism_queue_service import ProposalCriticismQueueService
from models.customer_models import CustomOppsTable


class ProposalCriticismSchedulerService:
    """
    Scheduler service for running criticism analysis on generated proposals.
    """
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
        self.is_enabled = True
        self.criticism_chain = CriticismChain()
        self.custom_controller = CustomOpportunitiesController
        self.criticism_controller = ProposalCriticismController
        self.criticism_queue_service = ProposalCriticismQueueService
        self.lock_service = ScheduleLockService("PROPOSAL_CRITICISM_LOCK", "PROPOSAL_CRITICISM")
        
        logger.info("ProposalCriticismSchedulerService initialized")
    
    async def start(self):
        """Start the criticism scheduler"""
        if self.is_running:
            logger.warning("Proposal criticism scheduler is already running")
            return
        
        try:
            # Schedule criticism analysis every 5 minutes
            self.scheduler.add_job(
                self.process_proposal_criticism,
                trigger=IntervalTrigger(minutes=5),
                id='proposal_criticism_processor',
                name='Proposal Criticism Processor',
                max_instances=1,
                coalesce=True
            )
            
            self.scheduler.start()
            self.is_running = True
            logger.info("Proposal criticism scheduler started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start proposal criticism scheduler: {e}")
            raise
    
    async def stop(self):
        """Stop the criticism scheduler"""
        if not self.is_running:
            logger.warning("Proposal criticism scheduler is not running")
            return
        
        try:
            self.scheduler.shutdown(wait=True)
            self.is_running = False
            logger.info("Proposal criticism scheduler stopped successfully")
            
        except Exception as e:
            logger.error(f"Failed to stop proposal criticism scheduler: {e}")
            raise
    
    async def process_proposal_criticism(self):
        """Process criticism queue items"""
        if not self.is_enabled:
            logger.info("Proposal criticism scheduler is disabled, skipping processing")
            return

        acquired = await self.lock_service.try_acquire_lock()
        if not acquired:
            logger.info("Proposal criticism lock not acquired, skipping this run")
            return

        try:
            logger.info("Starting proposal criticism queue processing cycle")

            async for db in get_customer_db():
                # Get new criticism queue items
                queue_items = await self.criticism_queue_service.get_new_items(db, limit=5)

                if not queue_items:
                    logger.info("No new criticism queue items found")
                    return

                logger.info(f"Found {len(queue_items)} criticism queue items to process")

                for queue_item in queue_items:
                    try:
                        # Mark as processing
                        await self.criticism_queue_service.update_status(
                            db, queue_item.id, "PROCESSING"
                        )

                        # Process the criticism analysis
                        await self._process_criticism_queue_item(db, queue_item)

                    except Exception as e:
                        logger.error(f"Failed to process criticism queue item {queue_item.id}: {e}")
                        # Mark as failed
                        await self.criticism_queue_service.update_status(
                            db, queue_item.id, "FAILED", error_message=str(e)
                        )
                        continue

                break

        except Exception as e:
            logger.error(f"Error in proposal criticism queue processing: {e}")

        finally:
            await self.lock_service.release_lock()

    async def _process_criticism_queue_item(self, db, queue_item):
        """Process a single criticism queue item"""
        start_time = datetime.now()

        try:
            logger.info(f"Processing criticism queue item {queue_item.id} for opportunity {queue_item.opportunity_id}")

            proposal_data = await self._get_proposal_data_for_criticism(db, queue_item)

            if not proposal_data:
                raise Exception(f"No proposal data found for opportunity {queue_item.opportunity_id}")

            criticism_results = await self._run_criticism_analysis(db, queue_item, proposal_data)

            processing_time = (datetime.now() - start_time).total_seconds()

            await self.criticism_controller.create_criticism_result(
                db, queue_item.opportunity_id, queue_item.tenant_id, criticism_results
            )

            await self.criticism_queue_service.update_status(
                db, queue_item.id, "COMPLETED",
                analysis_results=criticism_results,
                processing_time=processing_time
            )

            logger.info(f"Completed criticism analysis for queue item {queue_item.id} in {processing_time:.2f}s")

        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Error processing criticism queue item {queue_item.id}: {e}")

            await self.criticism_queue_service.update_status(
                db, queue_item.id, "FAILED",
                error_message=str(e),
                processing_time=processing_time
            )
            raise

    async def _get_proposal_data_for_criticism(self, db, queue_item):
        """Get proposal data for criticism analysis"""
        try:
            query = select(CustomOppsTable).where(
                CustomOppsTable.opportunity_id == queue_item.opportunity_id
            )

            result = await db.execute(query)
            opportunity = result.scalars().first()

            if not opportunity or not opportunity.draft:
                return None

            try:
                draft_content = json.loads(opportunity.draft)
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON in draft for opportunity {queue_item.opportunity_id}")
                return None

            # Ensure draft_content has the expected structure for criticism chain
            # The criticism chain expects a dictionary with 'draft' key containing sections
            if isinstance(draft_content, list):
                # If draft_content is a list, wrap it in the expected structure
                draft_structure = {'draft': draft_content}
            elif isinstance(draft_content, dict) and 'draft' in draft_content:
                # If it already has the correct structure, use it as is
                draft_structure = draft_content
            else:
                # If it's a dict but doesn't have 'draft' key, assume it's the draft itself
                draft_structure = {'draft': [draft_content] if isinstance(draft_content, dict) else draft_content}

            proposal_data = {
                'opportunity_id': queue_item.opportunity_id,
                'tenant_id': queue_item.tenant_id,
                'client_short_name': queue_item.client_short_name,
                'title': opportunity.title or '',
                'draft': draft_structure,  # Use the properly structured draft
                'last_mod_date': opportunity.last_mod_date.isoformat() if opportunity.last_mod_date else None,
                'generation_method': 'database_stored',
                'table_of_contents': []
            }

            try:
                toc_record = await self.custom_controller.get_table_of_contents(db, queue_item.opportunity_id)
                if toc_record and toc_record[0]:
                    proposal_data['table_of_contents'] = json.loads(str(toc_record[0]))
            except Exception as e:
                logger.warning(f"Could not get table of contents for {queue_item.opportunity_id}: {e}")

            return proposal_data

        except Exception as e:
            logger.error(f"Error getting proposal data for criticism: {e}")
            return None

    async def _run_criticism_analysis(self, db, queue_item, proposal_data):
        """Run criticism analysis on proposal data"""
        try:
            # Load compliance requirements
            logger.info(f"Loading compliance requirements for opportunity {queue_item.opportunity_id}")
            structure_compliance = await self._load_compliance_requirements_from_db(db, queue_item.opportunity_id, 'structure')
            content_compliance = await self._load_compliance_requirements_from_db(db, queue_item.opportunity_id, 'content')
            formatting_compliance = await self._load_compliance_requirements_from_db(db, queue_item.opportunity_id, 'formatting')
            technical_compliance = await self._load_compliance_requirements_from_db(db, queue_item.opportunity_id, 'technical')

            # Log compliance status
            compliance_status = {
                'structure': bool(structure_compliance),
                'content': bool(content_compliance),
                'formatting': bool(formatting_compliance),
                'technical': bool(technical_compliance)
            }
            logger.info(f"Compliance requirements loaded: {compliance_status}")

            # Check if we have any compliance requirements
            has_any_compliance = any(compliance_status.values())
            if not has_any_compliance:
                logger.warning(f"No compliance requirements found for opportunity {queue_item.opportunity_id}. Attempting to generate them.")

                # Try to generate missing compliance requirements
                compliance_generated = await self._generate_missing_compliance_requirements(
                    db, queue_item.opportunity_id, queue_item.tenant_id
                )

                if compliance_generated:
                    # Reload compliance requirements after generation
                    structure_compliance = await self._load_compliance_requirements_from_db(db, queue_item.opportunity_id, 'structure')
                    content_compliance = await self._load_compliance_requirements_from_db(db, queue_item.opportunity_id, 'content')
                    formatting_compliance = await self._load_compliance_requirements_from_db(db, queue_item.opportunity_id, 'formatting')
                    technical_compliance = await self._load_compliance_requirements_from_db(db, queue_item.opportunity_id, 'technical')

                    # Update compliance status
                    compliance_status = {
                        'structure': bool(structure_compliance),
                        'content': bool(content_compliance),
                        'formatting': bool(formatting_compliance),
                        'technical': bool(technical_compliance)
                    }
                    logger.info(f"Compliance requirements generated and reloaded: {compliance_status}")
                else:
                    logger.error(f"Failed to generate compliance requirements for opportunity {queue_item.opportunity_id}")
                    return {
                        'opportunity_id': queue_item.opportunity_id,
                        'tenant_id': queue_item.tenant_id,
                        'client_short_name': queue_item.client_short_name,
                        'generation_method': proposal_data.get('generation_method', 'unknown'),
                        'analyzed_sections_count': 0,
                        'error': 'No compliance requirements available and generation failed'
                    }

            criticism_results = {
                'opportunity_id': queue_item.opportunity_id,
                'tenant_id': queue_item.tenant_id,
                'client_short_name': queue_item.client_short_name,
                'generation_method': proposal_data.get('generation_method', 'unknown'),
                'analyzed_sections_count': len(proposal_data.get('draft', {}).get('draft', [])) if proposal_data.get('draft') else 0
            }

            analysis_type = queue_item.analysis_type

            # Extract just the proposal outline for criticism chain
            # The criticism chain expects the proposal structure, not the full metadata
            proposal_outline = proposal_data.get('draft', {})

            if analysis_type in ['full', 'structure'] and structure_compliance:
                try:
                    structure_criticism = self.criticism_chain.criticize_structure(
                        proposal_outline, structure_compliance
                    )
                    criticism_results['structure_criticism'] = structure_criticism
                    logger.info(f"Structure criticism completed for {queue_item.opportunity_id}")
                except Exception as e:
                    logger.error(f"Structure criticism failed for {queue_item.opportunity_id}: {e}")

            if analysis_type in ['full', 'content'] and content_compliance:
                try:
                    content_criticism = self.criticism_chain.criticize_content(
                        proposal_outline, content_compliance
                    )
                    criticism_results['content_criticism'] = content_criticism
                    logger.info(f"Content criticism completed for {queue_item.opportunity_id}")
                except Exception as e:
                    logger.error(f"Content criticism failed for {queue_item.opportunity_id}: {e}")

            if analysis_type in ['full', 'formatting'] and formatting_compliance:
                try:
                    formatting_criticism = self.criticism_chain.criticize_formatting(
                        proposal_outline, formatting_compliance
                    )
                    criticism_results['formatting_criticism'] = formatting_criticism
                    logger.info(f"Formatting criticism completed for {queue_item.opportunity_id}")
                except Exception as e:
                    logger.error(f"Formatting criticism failed for {queue_item.opportunity_id}: {e}")

            if analysis_type in ['full', 'technical'] and technical_compliance:
                try:
                    technical_criticism = self.criticism_chain.criticize_technical(
                        proposal_outline, technical_compliance
                    )
                    criticism_results['technical_criticism'] = technical_criticism
                    logger.info(f"Technical criticism completed for {queue_item.opportunity_id}")
                except Exception as e:
                    logger.error(f"Technical criticism failed for {queue_item.opportunity_id}: {e}")

            return criticism_results

        except Exception as e:
            logger.error(f"Error running criticism analysis: {e}")
            raise
    
    async def _get_proposals_for_criticism(self, db) -> List[Dict[str, Any]]:
        """
        Get proposals from database that need criticism analysis.

        Args:
            db: Database session

        Returns:
            List of proposal data for analysis
        """
        try:

            cutoff_time = datetime.now() - timedelta(hours=24)

            query = select(CustomOppsTable).where(
                and_(
                    CustomOppsTable.draft.isnot(None),
                    CustomOppsTable.draft != '',
                    CustomOppsTable.last_mod_date >= cutoff_time
                )
            ).order_by(desc(CustomOppsTable.last_mod_date))

            result = await db.execute(query)
            opportunities = result.scalars().all()

            proposals_to_analyze = []

            for opp in opportunities:
                try:
                    # Check if this opportunity has already been analyzed recently
                    already_analyzed = await self.criticism_controller.check_if_analyzed(
                        db, opp.opportunity_id, opp.tenant_id, hours=24
                    )

                    if already_analyzed:
                        logger.debug(f"Opportunity {opp.opportunity_id} already analyzed recently, skipping")
                        continue

                    # Parse the draft content
                    draft_content = None
                    if opp.draft:
                        try:
                            draft_content = json.loads(opp.draft)
                        except json.JSONDecodeError:
                            logger.warning(f"Invalid JSON in draft for opportunity {opp.opportunity_id}")
                            continue

                    if not draft_content:
                        continue

                    # Prepare proposal data for analysis
                    proposal_data = {
                        'opportunity_id': opp.opportunity_id,
                        'tenant_id': opp.tenant_id,
                        'client_short_name': getattr(opp, 'client_short_name', '') or '',
                        'title': opp.title or '',
                        'draft': draft_content,
                        'last_mod_date': opp.last_mod_date.isoformat() if opp.last_mod_date else None,
                        'generation_method': 'database_stored',
                        'table_of_contents': []
                    }

                    # Try to get table of contents
                    try:
                        toc_record = await self.custom_controller.get_table_of_contents(db, opp.opportunity_id)
                        if toc_record and toc_record[0]:
                            proposal_data['table_of_contents'] = json.loads(str(toc_record[0]))
                    except Exception as e:
                        logger.warning(f"Could not get table of contents for {opp.opportunity_id}: {e}")

                    proposals_to_analyze.append(proposal_data)

                except Exception as e:
                    logger.warning(f"Error processing opportunity {opp.opportunity_id}: {e}")
                    continue

            logger.info(f"Found {len(proposals_to_analyze)} proposals in database for criticism analysis")
            return proposals_to_analyze

        except Exception as e:
            logger.error(f"Error getting proposals from database for criticism: {e}")
            return []
    
    async def _analyze_proposal(self, db, proposal_data: Dict[str, Any]):
        """
        Analyze a single proposal using the criticism chain.

        Args:
            db: Database session
            proposal_data: Proposal data to analyze
        """
        opportunity_id = proposal_data.get('opportunity_id', 'unknown')
        tenant_id = proposal_data.get('tenant_id', 'unknown')

        logger.info(f"Analyzing proposal for opportunity {opportunity_id}")

        start_time = datetime.now()

        try:
            # Load compliance requirements from database or files
            structure_compliance = await self._load_compliance_requirements_from_db(db, opportunity_id, 'structure')
            content_compliance = await self._load_compliance_requirements_from_db(db, opportunity_id, 'content')
            formatting_compliance = await self._load_compliance_requirements_from_db(db, opportunity_id, 'formatting')
            technical_compliance = await self._load_compliance_requirements_from_db(db, opportunity_id, 'technical')

            # Prepare criticism results
            criticism_results = {
                'opportunity_id': opportunity_id,
                'tenant_id': tenant_id,
                'client_short_name': proposal_data.get('client_short_name', ''),
                'generation_method': proposal_data.get('generation_method', 'unknown'),
                'analyzed_sections_count': len(proposal_data.get('draft', {}).get('draft', [])) if proposal_data.get('draft') else 0
            }

            # Run criticism analysis
            analysis_count = 0

            # Structure criticism
            if structure_compliance:
                try:
                    structure_criticism = self.criticism_chain.criticize_structure(
                        proposal_data, structure_compliance
                    )
                    criticism_results['structure_criticism'] = structure_criticism
                    analysis_count += 1
                    logger.info(f"Structure criticism completed for {opportunity_id}")
                except Exception as e:
                    logger.error(f"Structure criticism failed for {opportunity_id}: {e}")

            # Content criticism
            if content_compliance:
                try:
                    content_criticism = self.criticism_chain.criticize_content(
                        proposal_data, content_compliance
                    )
                    criticism_results['content_criticism'] = content_criticism
                    analysis_count += 1
                    logger.info(f"Content criticism completed for {opportunity_id}")
                except Exception as e:
                    logger.error(f"Content criticism failed for {opportunity_id}: {e}")

            # Formatting criticism
            if formatting_compliance:
                try:
                    formatting_criticism = self.criticism_chain.criticize_formatting(
                        proposal_data, formatting_compliance
                    )
                    criticism_results['formatting_criticism'] = formatting_criticism
                    analysis_count += 1
                    logger.info(f"Formatting criticism completed for {opportunity_id}")
                except Exception as e:
                    logger.error(f"Formatting criticism failed for {opportunity_id}: {e}")

            # Technical criticism
            if technical_compliance:
                try:
                    technical_criticism = self.criticism_chain.criticize_technical(
                        proposal_data, technical_compliance
                    )
                    criticism_results['technical_criticism'] = technical_criticism
                    analysis_count += 1
                    logger.info(f"Technical criticism completed for {opportunity_id}")
                except Exception as e:
                    logger.error(f"Technical criticism failed for {opportunity_id}: {e}")

            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            criticism_results['processing_time_seconds'] = processing_time

            # Save criticism results to database
            if analysis_count > 0:
                await self.criticism_controller.create_criticism_result(
                    db, opportunity_id, tenant_id, criticism_results
                )
                logger.info(f"Criticism analysis completed for opportunity {opportunity_id} in {processing_time:.2f}s")
            else:
                logger.warning(f"No criticism analysis completed for opportunity {opportunity_id} - no compliance requirements found")

        except Exception as e:
            logger.error(f"Error analyzing proposal {opportunity_id}: {e}")
            raise

    async def _load_compliance_requirements_from_db(
        self,
        db,
        opportunity_id: str,
        compliance_type: str
    ) -> Optional[Dict[str, Any]]:
        """
        Load compliance requirements from database or fallback to files.

        Args:
            db: Database session
            opportunity_id: Opportunity identifier
            compliance_type: Type of compliance (structure, content, formatting, technical)

        Returns:
            Compliance requirements dictionary or None
        """
        try:

            # First try to get compliance from the opportunity record
            query = select(CustomOppsTable).where(
                CustomOppsTable.opportunity_id == opportunity_id
            )

            result = await db.execute(query)
            opportunity = result.scalars().first()

            if not opportunity:
                logger.warning(f"No opportunity found for {opportunity_id}")
                return None

            # Get compliance field based on type from database only
            compliance_field_map = {
                'structure': getattr(opportunity, 'structure_compliance', None),
                'content': getattr(opportunity, 'content_compliance', None),
                'formatting': getattr(opportunity, 'format_compliance', None),
                'technical': getattr(opportunity, 'content_compliance', None)  # Use content_compliance for technical
            }

            compliance_text = compliance_field_map.get(compliance_type)

            if compliance_text and compliance_text.strip():
                try:
                    # Try to parse as JSON first
                    parsed_compliance = json.loads(compliance_text)

                    # For technical compliance, extract technical_requirements if it exists
                    if compliance_type == 'technical' and isinstance(parsed_compliance, dict):
                        if 'technical_requirements' in parsed_compliance:
                            return {'requirements': parsed_compliance['technical_requirements']}
                        elif 'requirements' in parsed_compliance:
                            return parsed_compliance
                        else:
                            # Return the whole structure for technical analysis
                            return parsed_compliance
                    else:
                        return parsed_compliance

                except json.JSONDecodeError:
                    # If not JSON, return as text requirements
                    return {'requirements': compliance_text.strip()}
            else:
                logger.info(f"No {compliance_type} compliance requirements found in database for {opportunity_id}")
                return None

        except Exception as e:
            logger.error(f"Error loading compliance requirements from DB for {opportunity_id}: {e}")
            return None

    async def _generate_missing_compliance_requirements(
        self,
        db,
        opportunity_id: str,
        tenant_id: str
    ) -> bool:
        """
        Generate missing compliance requirements for an opportunity.

        Args:
            db: Database session
            opportunity_id: Opportunity identifier
            tenant_id: Tenant identifier

        Returns:
            True if compliance requirements were generated successfully, False otherwise
        """
        try:
            logger.info(f"Generating missing compliance requirements for opportunity {opportunity_id}")

            # Import the compliance services
            from services.proposal.structure_compliance import StructureComplianceService
            from services.proposal.content_compliance import ContentComplianceService
            from services.proposal.formatting_requirements import FormattingRequirementsService
            from services.proposal.technical_requirements import TechnicalRequirementsService

            # Initialize services
            structure_service = StructureComplianceService()
            content_service = ContentComplianceService()
            formatting_service = FormattingRequirementsService()
            technical_service = TechnicalRequirementsService()

            # Determine source (assume custom for criticism analysis)
            source = "custom"

            # Generate structure compliance
            try:
                logger.info(f"Generating structure compliance for {opportunity_id}")
                structure_result = await structure_service.generate_structure_compliance(
                    opportunity_id=opportunity_id,
                    tenant_id=tenant_id,
                    source=source,
                    max_tokens=2048
                )

                structure_content = structure_result.get("content", "")
                if hasattr(structure_content, 'content'):
                    structure_content_str = structure_content.content
                else:
                    structure_content_str = str(structure_content)

                # Update database
                await self.custom_controller.update_by_opportunity_id(
                    db, opportunity_id, {"structure_compliance": structure_content_str}
                )
                logger.info(f"Structure compliance generated and stored for {opportunity_id}")

            except Exception as e:
                logger.error(f"Failed to generate structure compliance for {opportunity_id}: {e}")

            # Generate content compliance
            try:
                logger.info(f"Generating content compliance for {opportunity_id}")
                content_result = await content_service.generate_content_compliance(
                    opportunity_id=opportunity_id,
                    tenant_id=tenant_id,
                    source=source,
                    is_rfp=True  # Assume RFP for now
                )

                content_compliance_content = content_result.get("content", "")
                if hasattr(content_compliance_content, 'content'):
                    content_compliance_str = content_compliance_content.content
                else:
                    content_compliance_str = str(content_compliance_content)

                # Update database
                await self.custom_controller.update_by_opportunity_id(
                    db, opportunity_id, {"content_compliance": content_compliance_str}
                )
                logger.info(f"Content compliance generated and stored for {opportunity_id}")

            except Exception as e:
                logger.error(f"Failed to generate content compliance for {opportunity_id}: {e}")

            # Generate formatting requirements
            try:
                logger.info(f"Generating formatting requirements for {opportunity_id}")
                formatting_result = await formatting_service.generate_formatting_requirements(
                    opportunity_id=opportunity_id,
                    tenant_id=tenant_id,
                    source=source,
                    max_tokens=1024
                )

                formatting_content = formatting_result.get("content", "")
                if hasattr(formatting_content, 'content'):
                    formatting_content_str = formatting_content.content
                else:
                    formatting_content_str = str(formatting_content)

                # Update database
                await self.custom_controller.update_by_opportunity_id(
                    db, opportunity_id, {"format_compliance": formatting_content_str}
                )
                logger.info(f"Formatting requirements generated and stored for {opportunity_id}")

            except Exception as e:
                logger.error(f"Failed to generate formatting requirements for {opportunity_id}: {e}")

            # Generate technical requirements
            try:
                logger.info(f"Generating technical requirements for {opportunity_id}")
                technical_result = await technical_service.generate_technical_requirements(
                    opportunity_id=opportunity_id,
                    tenant_id=tenant_id,
                    source=source,
                    max_tokens=2048
                )

                technical_content = technical_result.get("content", "")
                if hasattr(technical_content, 'content'):
                    technical_content_str = technical_content.content
                else:
                    technical_content_str = str(technical_content)

                # Store technical requirements in content_compliance field
                # Get the current opportunity record to check existing content_compliance
                query = select(CustomOppsTable).where(
                    CustomOppsTable.opportunity_id == opportunity_id
                )
                result = await db.execute(query)
                opportunity = result.scalars().first()

                existing_content_compliance = getattr(opportunity, 'content_compliance', None) if opportunity else None

                if existing_content_compliance and existing_content_compliance.strip():
                    try:
                        # Try to parse existing content compliance
                        existing_data = json.loads(existing_content_compliance)
                        if isinstance(existing_data, dict):
                            existing_data['technical_requirements'] = technical_content_str
                            combined_compliance = json.dumps(existing_data)
                        else:
                            # If existing data is not a dict, create new structure
                            combined_compliance = json.dumps({
                                'content_requirements': existing_content_compliance,
                                'technical_requirements': technical_content_str
                            })
                    except json.JSONDecodeError:
                        # If existing data is not JSON, treat as text
                        combined_compliance = json.dumps({
                            'content_requirements': existing_content_compliance,
                            'technical_requirements': technical_content_str
                        })
                else:
                    # No existing content compliance, create new
                    combined_compliance = json.dumps({
                        'technical_requirements': technical_content_str
                    })

                await self.custom_controller.update_by_opportunity_id(
                    db, opportunity_id, {"content_compliance": combined_compliance}
                )
                logger.info(f"Technical requirements generated and stored for {opportunity_id}")

            except Exception as e:
                logger.error(f"Failed to generate technical requirements for {opportunity_id}: {e}")

            logger.info(f"Compliance requirements generation completed for {opportunity_id}")
            return True

        except Exception as e:
            logger.error(f"Error generating compliance requirements for {opportunity_id}: {e}")
            return False
    

    

    
    def _log_criticism_metrics(self, criticism_results: Dict[str, Any]):
        """Log key criticism metrics for monitoring"""
        try:
            opportunity_id = criticism_results.get('opportunity_id', 'unknown')
            
            # Extract scores from different criticism types
            scores = {}
            
            for criticism_type in ['structure', 'content', 'formatting', 'technical']:
                criticism_key = f'{criticism_type}_criticism'
                if criticism_key in criticism_results:
                    criticism_data = criticism_results[criticism_key]
                    if isinstance(criticism_data, dict) and 'score' in criticism_data:
                        scores[criticism_type] = criticism_data['score']
            
            if scores:
                avg_score = sum(scores.values()) / len(scores)
                logger.info(f"CRITICISM_METRICS | {opportunity_id} | Average Score: {avg_score:.1f} | Scores: {scores}")
            
        except Exception as e:
            logger.warning(f"Error logging criticism metrics: {e}")
    
    def enable(self):
        """Enable criticism processing"""
        self.is_enabled = True
        logger.info("Proposal criticism scheduler enabled")
    
    def disable(self):
        """Disable criticism processing"""
        self.is_enabled = False
        logger.info("Proposal criticism scheduler disabled")
