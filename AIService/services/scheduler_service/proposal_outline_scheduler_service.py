import asyncio
from AIService.controllers.customer.proposal_outline_queue_controller import ProposalOutlineQueueController
from AIService.services.scheduler_service.schedule_lock_service import ScheduleLockService
from EbuyScrapper.db import db
from loguru import logger
from database import get_kontratar_db
from models.customer_models import ProposalOutlineQueue
from services.proposal.outline import ProposalOutlineService
from utils.semaphore import run_with_semaphore
from services.proposal.utilities import ProposalUtilities

from sqlalchemy import select, text
from controllers.kontratar.sam_opps_table_coontroller import OppsController
import json
from datetime import datetime
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from typing import Any, Dict, List
from sqlalchemy.ext.asyncio import AsyncSession


class ProposalOutlineSchedulerService:
    def __init__(self):
        self.outline_service = ProposalOutlineService()
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
        self.is_enabled = False
        self.lock_service = ScheduleLockService("PROPOSAL_OUTLINE_SCHEDULER_LOCK", "PROPOSAL_OUTLINE_SCHEDULER")
        self.proposal_outline_service = ProposalOutlineService()    

    async def process_outline_queue(self):
        if not self.is_enabled:
            logger.info("Proposal Outline Scheduler is disabled.")
            return

        acquired = await self.lock_service.try_acquire_lock()
        if not acquired:
            logger.info("Proposal Outline Scheduler lock not acquired, skipping this run")
            return

        try:
            async for db in get_kontratar_db():
                items = await ProposalOutlineQueueController.claim_new_queue_items(db, limit=6)
                break

            if not items:
                logger.info("No outline queue items to process.")
                return
            
            async def _on_enter(item):
                async for db in get_kontratar_db():
                    await ProposalOutlineQueueController.update_queue_status(
                        db, item.id, "PROCESSING"
                    )
                    break

            async def worker(item):
                try:
                    await self._generate_proposal_outline(item)
                    async for db in get_kontratar_db():
                        await ProposalOutlineQueueController.complete_and_notify(db, item.id)
                        break
                    logger.info(f"Outline generated for {item.opps_id}")
                except Exception as e:
                    logger.error(f"Error generating outline for {item.opps_id}: {e}")
                    async for db in get_kontratar_db():
                        await ProposalOutlineQueueController.update_queue_status(
                            db, item.id, "FAILED", error_message=str(e)
                        )
                        break
                    
            await run_with_semaphore(
                items=items,
                max_jobs=2,
                on_enter=_on_enter,
                worker=worker
            )
        finally:
            await self.lock_service.release_lock()
        
    async def _generate_proposal_outline(self, item):
        """Generate proposal outline for each TOC and store in OppsTable"""
        logger.info(f"Generating Proposal Outline for SAM opportunity: {item.opps_id}")

        try:
            # Retrieve all TOCs from DB
            query = text(
                "SELECT toc_text, toc_text2, toc_text3, toc_text4, toc_text5 "
                "FROM kontratar_main.oppstable WHERE notice_id = :opps_id"
            )
            async for db in get_kontratar_db():
                result = await db.execute(query, {"opps_id": item.opps_id})
                break
            row = result.first()
            if not row:
                logger.warning(f"No TOC found for SAM opportunity: {item.opps_id}")
                return

            toc_fields = ["toc_text", "toc_text2", "toc_text3", "toc_text4", "toc_text5"]
            outline_fields = [
            "proposal_outline_1", "proposal_outline_2", "proposal_outline_3",
            "proposal_outline_4", "proposal_outline_5"
        ]
            update_fields = {}

            for idx, toc_text in enumerate(row):
                if not toc_text:
                    continue
                try:
                    toc_json = json.loads(toc_text)
                except Exception:
                    logger.warning(f"TOC field {toc_fields[idx]} is not valid JSON for SAM opportunity: {item.opps_id}")
                    continue

                table_of_contents = toc_json
                if not table_of_contents:
                    logger.warning(f"No table_of_contents in {toc_fields[idx]} for SAM opportunity: {item.opps_id}")
                    continue
                logger.debug(f"Generating outline for TOC field {toc_fields[idx]}: {table_of_contents}")
                # Generate the outline using the ProposalOutlineService
                outline_result = await self.proposal_outline_service.generate_outline(
                    opportunity_id=item.opps_id,
                    tenant_id=getattr(item, "tenant_id", None) or "default",
                    source=getattr(item, "opps_source", None) or "sam",
                    table_of_contents=table_of_contents
                )
                
                outline_content = outline_result.get("outlines", "")
                
                
                logger.debug(f"Outline content for {toc_fields[idx]}: {str(outline_content)[:100]}...")
                if not outline_content:
                    logger.warning(f"No outline generated for TOC field {toc_fields[idx]} in SAM opportunity: {item.opps_id}")
                    continue  # Don't return, just skip this one
                
                # Store the outline JSON string in the appropriate field
                outline_content_json_str = json.dumps(outline_content, ensure_ascii=False)
                update_fields[outline_fields[idx]] = outline_content_json_str

            if update_fields:
                async for db in get_kontratar_db():
                    updated_record = await OppsController.update_by_opportunity_id(
                        db=db,
                        opportunity_id=item.opps_id,
                        update_fields=update_fields
                    )
                    break 
                if updated_record:
                    logger.info(f"Successfully stored Proposal Outline for SAM opportunity: {item.opps_id}")
                else:
                    logger.error(f"Failed to update OppsTable with Proposal Outline for SAM opportunity: {item.opps_id}")

        except Exception as e:
            logger.error(f"Error generating Proposal Outline for SAM opportunity {item.opps_id}: {e}")

    def enable(self):
        self.is_enabled = True

    def disable(self):
        self.is_enabled = False

    def is_scheduler_enabled(self) -> bool:
        return self.is_enabled
    
    def start(self, interval_seconds: int = 30):
        if self.is_running:
            logger.warning("proposal outline scheduler is already running")
            return
        self.scheduler.add_job(
            self.process_outline_queue,
            IntervalTrigger(seconds=interval_seconds),
            id="process_proposal_outline_queue",
            name="Process Proposal Outline Queue"
        )
        self.scheduler.start()
        self.is_running = True
        logger.info(f"Proposal Outline Queue scheduler started with {interval_seconds} second interval")

    def stop(self):
        if not self.is_running:
            logger.warning("Proposal Outline Queue scheduler is not running")
            return
        self.scheduler.shutdown()
        self.is_running = False
        logger.info("Proposal Outline Queue scheduler stopped")

    def restart(self, interval_seconds: int = 30):
        logger.info("Restarting Proposal Outline Queue scheduler...")
        self.stop()
        self.start(interval_seconds)
    
    def get_status(self) -> Dict[str, Any]:
        return {
            "is_running": self.is_running,
            "is_enabled": self.is_enabled,
            "jobs": self.get_jobs()
        }
    
    def get_jobs(self) -> List[Dict[str, Any]]:
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        return jobs
  