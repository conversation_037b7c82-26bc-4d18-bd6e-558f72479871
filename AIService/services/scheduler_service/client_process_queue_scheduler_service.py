import asyncio
import json
from typing import Any, Dict, List
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from utils.embedding_model import KontratarEmbeddings
from utils.hashing import generate_hash
from controllers.customer.datametastore_controller import DataMetastoreController
from services.data_load.process_document import DocumentProcessingService
from database import get_customer_db, get_kontratar_db
from loguru import logger
from models.customer_models import ClientProcessQueue
from services.chroma.chroma_service import ChromaService
from controllers.kontratar.chromadb_mapping_controller import ChromaDBMappingController
from controllers.customer.client_process_queue_controller import Client<PERSON>rocessQueueController
from services.scheduler_service.schedule_lock_service import ScheduleLockService

class ClientProcessQueueSchedulerService:
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
        self.is_enabled = False
        self.lock_service = ScheduleLockService("CLIENT_PROCESS_QUEUE_SCHEDULER_LOCK", "CLIENT_PROCESS_QUEUE_SCHEDULER")

    async def process_client_process_queue(self):
        if not self.is_enabled:
            logger.info("Client process queue scheduler is disabled, skipping processing")
            return
        acquired = await self.lock_service.try_acquire_lock()
        if not acquired:
            logger.info("Client process queue scheduler lock not acquired, skipping this run")
            return
        

        try:
            async for db in get_customer_db():
                items = await ClientProcessQueueController.get_new_queue_items(db, limit=10)
                if not items:
                    logger.info("No new client process queue items found")
                    return

                logger.info(f"Processing {len(items)} new client process queue items")
                for item in items:
                    try:
                        job_id = str(item.job_id)
                        await ClientProcessQueueController.update_queue_status(db,job_id , "PROCESSING")
                        # TODO: Add your processing logic here
                        await self._process_queue_item(item)
                        
                        await ClientProcessQueueController.update_queue_status(db, job_id, "COMPLETED")
                        logger.info(f"Successfully processed client process queue item {job_id}")
                    except Exception as e:
                        logger.error(f"Error processing client process queue item {job_id}: {e}")
                        await ClientProcessQueueController.update_queue_status(db, job_id, "FAILED")
        except Exception as e:
            logger.error(f"Error in process_client_process_queue: {e}")
        finally:
            await self.lock_service.release_lock()
            logger.info("Released lock for client process queue scheduler")

    async def _process_queue_item(self, item: ClientProcessQueue):
        job_instruction = json.loads(str(item.job_instruction))
        profile_id = job_instruction.get("profileId", None)
        client_short_name = job_instruction.get("clientShortName", None)
        tenant_id = job_instruction.get("tenantId", None)

        async for db in get_customer_db():
            if profile_id is not None:
                documents = await DataMetastoreController.get_client_documents(db, tenant_id, client_short_name, profile_id)
            else:
                documents = await DataMetastoreController.get_all_client_documents(db, client_short_name, tenant_id)
            break

        for document in documents:
            
            collection_name = f"{document.tenant_id}_{document.record_identifier}"
            raw_text = document.raw_text_document
            if isinstance(raw_text, bytes):
                plain_text = raw_text.decode("utf-8")
            else:
                plain_text = raw_text

            cleaned_text = DocumentProcessingService.process_corpus(str(plain_text))
            text_chunks = DocumentProcessingService.semantic_chunk(cleaned_text)

            if str(document.record_identifier) != client_short_name:
                await self.add_profile_documents(collection_name, text_chunks)
            else:
                await DocumentProcessingService.add_documents(collection_name, text_chunks)

    async def add_profile_documents(self, collection_name: str, documents: List[str]):
        logger.info(f"Starting add_profile_documents for collection_name: {collection_name} with {len(documents)} documents")
        chroma_instance_service = ChromaDBMappingController()
        chroma_service = ChromaService("http://ai.kontratar.com:5000")

        hashed_collection_name = generate_hash(collection_name)
        logger.debug(f"Hashed collection_name: {hashed_collection_name}")

        parts = collection_name.split("_")
        async for db in get_kontratar_db():
            tenant_id = parts[0]
            unique_id = parts[1]
            logger.debug(f"Looking up ChromaDB mapping for unique_id: {unique_id}, tenant_id: {tenant_id}")
            chroma_instance = await chroma_instance_service.get_by_unique_id_and_tenant(db, unique_id, tenant_id)

            if chroma_instance is None:
                logger.info(f"No ChromaDB mapping found for unique_id: {unique_id}, tenant_id: {tenant_id}. Creating new mapping.")
                # Try getting from the parent 
                base_unique_id = unique_id.split(".")[0]
                chroma_instance = await chroma_instance_service.add(
                    db = db,
                    unique_id=unique_id,
                    tenant_id = tenant_id,
                    chroma_instance_url="http://3.217.236.185:9004",
                    collection_name = hashed_collection_name
                )
                if chroma_instance:
                    logger.info(f"Created new ChromaDB mapping for unique_id: {unique_id}, tenant_id: {tenant_id}")
                else:
                    logger.error(f"Failed to create ChromaDB mapping for unique_id: {unique_id}, tenant_id: {tenant_id}")
            else:
                logger.info(f"Found existing ChromaDB mapping for unique_id: {unique_id}, tenant_id: {tenant_id}")
            break

        # 1. Create collection and get collection ID
        collection_metadata = {
            "tenant_id": tenant_id,
            "unique_id": unique_id
        }

        if chroma_instance is None:
            logger.error("chroma_instance is None after lookup/creation. Aborting add_profile_documents.")
            return None

        chroma_url = str(chroma_instance.chroma_instance_url)
        logger.debug(f"Using ChromaDB URL: {chroma_url}")
        logger.info(f"Creating collection in ChromaDB with name: {hashed_collection_name} and metadata: {collection_metadata}")
        collection_id = await chroma_service.create_collection(chroma_url, hashed_collection_name, collection_metadata)

        if collection_id is None:
            logger.error(f"Failed to create collection for collection_name: {hashed_collection_name}")
            return None

        logger.info(f"Embedding {len(documents)} documents for collection_id: {collection_id}")
        embeddings = chroma_service.embeddings.embed_documents(documents)

        # 3. Add documents to the collection
        metadatas = None  # or provide a list of metadata dicts if needed
        logger.info(f"Adding documents to ChromaDB collection_id: {collection_id}")
        await chroma_service.add_documents_to_collection(
            chroma_url,
            collection_id,
            documents,
            embeddings,
            metadatas
        )
        logger.info(f"Successfully added {len(documents)} documents to ChromaDB collection_id: {collection_id}")

    def start(self, interval_seconds: int = 30):
        if self.is_running:
            logger.warning("Client process queue scheduler is already running")
            return
        self.scheduler.add_job(
            self.process_client_process_queue,
            IntervalTrigger(seconds=interval_seconds),
            id="process_client_process_queue",
            name="Process Client Process Queue"
        )
        self.scheduler.start()
        self.is_running = True
        logger.info(f"Client process queue scheduler started with {interval_seconds} second interval")

    def stop(self):
        if not self.is_running:
            logger.warning("Client process queue scheduler is not running")
            return
        self.scheduler.shutdown()
        self.is_running = False
        logger.info("Client process queue scheduler stopped")

    def is_scheduler_enabled(self) -> bool:
        """Check if datametastore queue scheduler is enabled"""
        return self.is_enabled

    def enable(self):
        """Enable the datametastore queue scheduler (allows jobs to run)"""
        self.is_enabled = True
        logger.info("Datametastore queue scheduler enabled")

    def disable(self):
        """Disable the datametastore queue scheduler (prevents jobs from running)"""
        self.is_enabled = False
        logger.info("Datametastore queue scheduler disabled")

    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive datametastore queue scheduler status"""
        return {
            "is_running": self.is_running,
            "is_enabled": self.is_enabled,
            "jobs": self.get_jobs()
        }

    def get_jobs(self) -> List[Dict[str, Any]]:
        """Get information about scheduled jobs"""
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        return jobs 