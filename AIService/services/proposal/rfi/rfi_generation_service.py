import json
import logging
from typing import Any, Dict, List

from services.proposal.outline import ProposalOutlineService
from models.customer_models import ProposalOutlineQueue
from controllers.customer.proposal_outline_queue_controller import ProposalOutlineQueueController
from controllers.customer.proposals_format_queue_controller import ProposalsFormatQueueController
from controllers.customer.proposals_in_review_controller import ProposalsInReviewController
from services.proposal.proposal_decoding_service import ProposalDecodingService
from services.proposal.structure_compliance import StructureComplianceService
from services.proposal.content_compliance import ContentComplianceService
from services.proposal.utilities import ProposalUtilities
from services.proposal.rfi.rfi_section import RFISection
from services.queue_service.proposal_criticism_queue_service import ProposalCriticismQueueService
from database import get_customer_db

from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from controllers.kontratar.ebuy_opps_controller import EBUYOppsController
from controllers.kontratar.opps_table_controller import OppsTableController

logger = logging.getLogger(__name__)

class RFIGenerationService:
    """
    Service for generating and saving RFI documents (single volume) for review.
    """
    def __init__(self):

        self.sam_service = OppsTableController()
        self.ebuy_service = EBUYOppsController()
        self.custom_service = CustomOpportunitiesController()

        self.structure_service = StructureComplianceService()
        self.content_compliance_service = ContentComplianceService()
        self.outline_service = ProposalOutlineService()

        self.proposal_decoding_service = ProposalDecodingService()
        self.proposal_in_review = ProposalsInReviewController()
        self.proposal_format_queue = ProposalsFormatQueueController()

    async def get_opportunity(self, opportunity_id: str, tenant_id: str, source: str):
        record = None
        # Normalize source to lowercase for consistent handling
        source = source.lower()

        if source == "sam":
            from database import get_kontratar_db
            async for db in get_kontratar_db():
                record = await self.sam_service.get_by_notice_id(db, opportunity_id)
                break
        elif source == "ebuy":
            from database import get_kontratar_db
            async for db in get_kontratar_db():
                record = await self.ebuy_service.get_by_rfq_id(db, opportunity_id)
                break
        elif source == "custom":
            async for db in get_customer_db():
                record = await self.custom_service.get_by_opportunity_id(db, opportunity_id)
                break
        else:
            logger.error(f"Invalid source type: {source}. Valid types are: sam, ebuy, custom")
            raise ValueError(f"Invalid source type: {source}. Valid types are: sam, ebuy, custom")
        if record is None:
            raise ValueError("Error getting opportunity metadata")
        return record

    async def _check_outline_queue_status(self, opps_id: str, tenant_id: str) -> str:
        """Check the status of outline in proposal_outline_queue"""
        try:
            from database import get_kontratar_db
            from sqlalchemy import select
            async for db in get_kontratar_db():
                query = select(ProposalOutlineQueue).where(
                    ProposalOutlineQueue.opps_id == opps_id,
                    ProposalOutlineQueue.tenant_id == tenant_id
                ).order_by(ProposalOutlineQueue.created_date.desc())
                result = await db.execute(query)
                queue_item = result.scalar_one_or_none()
                if queue_item:
                    return queue_item.status
                break
            return "NOT_FOUND"
        except Exception as e:
            logger.error(f"Error checking outline queue status for {opps_id}: {e}")
            return "ERROR"

    async def _add_to_outline_queue(self, opps_id: str, tenant_id: str, source: str):
        """Add opportunity to proposal_outline_queue"""
        try:
            from database import get_kontratar_db
            from sqlalchemy import select
            async for db in get_kontratar_db():
                # Check if already exists to avoid duplicates
                existing_query = select(ProposalOutlineQueue).where(
                    ProposalOutlineQueue.opps_id == opps_id,
                    ProposalOutlineQueue.tenant_id == tenant_id,
                    ProposalOutlineQueue.status.in_(["NEW", "PROCESSING"])
                )
                existing_result = await db.execute(existing_query)
                existing_item = existing_result.scalar_one_or_none()

                if not existing_item:
                    new_queue_item = ProposalOutlineQueue(
                        opps_id=opps_id,
                        tenant_id=tenant_id,
                        status="NEW",
                        outline_type=source.upper(),
                        first_request=True
                    )
                    db.add(new_queue_item)
                    await db.commit()
                    logger.info(f"Added {opps_id} to proposal_outline_queue")
                else:
                    logger.info(f"Outline queue item already exists for {opps_id}")
                break
        except Exception as e:
            logger.error(f"Error adding {opps_id} to outline queue: {e}")

    async def _update_outline_queue_status(self, opps_id: str, tenant_id: str, new_status: str):
        """Update the status of an outline queue item"""
        try:
            from database import get_kontratar_db
            from sqlalchemy import update
            from datetime import datetime
            async for db in get_kontratar_db():
                update_stmt = update(ProposalOutlineQueue).where(
                    ProposalOutlineQueue.opps_id == opps_id
                ).values(
                    status=new_status,
                    created_date=datetime.utcnow(),
                    error_message=None if new_status != "FAILED" else ProposalOutlineQueue.error_message
                )
                await db.execute(update_stmt)
                await db.commit()
                logger.info(f"Updated outline queue status for {opps_id} to {new_status}")
                break
        except Exception as e:
            logger.error(f"Error updating outline queue status for {opps_id}: {e}")

    async def _update_outline_queue_timestamp(self, opps_id: str, tenant_id: str):
        """Update the timestamp of an outline queue item to prioritize it"""
        try:
            from database import get_kontratar_db
            from sqlalchemy import update
            from datetime import datetime
            async for db in get_kontratar_db():
                update_stmt = update(ProposalOutlineQueue).where(
                    ProposalOutlineQueue.opps_id == opps_id
                ).values(created_date=datetime.utcnow())
                await db.execute(update_stmt)
                await db.commit()
                logger.info(f"Updated outline queue timestamp for {opps_id} to prioritize processing")
                break
        except Exception as e:
            logger.error(f"Error updating outline queue timestamp for {opps_id}: {e}")

    async def _wait_for_outline_or_queue(self, opps_id: str, tenant_id: str, source: str) -> tuple[bool, str]:
        """
        Check if outline is ready or needs to be queued.
        Updates queue timestamps and status as needed.
        """
        try:
            # Check outline queue status
            queue_status = await self._check_outline_queue_status(opps_id, tenant_id)

            if queue_status == "COMPLETED":
                logger.info(f"Outline generation completed for {opps_id}")
                return True, "Outline ready"
            elif queue_status == "PROCESSING":
                logger.info(f"Outline generation in progress for {opps_id}")
                return False, "Outline generation in progress, please try again later"
            elif queue_status == "FAILED":
                logger.info(f"Outline generation failed for {opps_id}, updating to NEW status")
                await self._update_outline_queue_status(opps_id, tenant_id, "NEW")
                return False, "Outline generation failed, requeued for retry"
            elif queue_status == "NEW":
                logger.info(f"Outline generation queued but not started for {opps_id}, updating timestamp")
                await self._update_outline_queue_timestamp(opps_id, tenant_id)
                return False, "Outline generation queued, please try again later"
            else:
                # Not in queue - add to queue
                logger.info(f"Adding {opps_id} to outline queue")
                await self._add_to_outline_queue(opps_id, tenant_id, source)
                return False, "Outline generation queued, please try again later"

        except Exception as e:
            logger.error(f"Error checking outline availability for {opps_id}: {e}")
            return False, f"Error checking outline availability: {str(e)}"

    async def update_generated_field(self, source: str, opportunity_id: str, updated_fields: dict):
        # Normalize source to lowercase for consistent handling
        source = source.lower()

        if source == "sam":
            from database import get_kontratar_db
            async for db in get_kontratar_db():
                record = await self.sam_service.update_by_notice_id(db, opportunity_id, updated_fields)
                break
        elif source == "ebuy":
            from database import get_kontratar_db
            async for db in get_kontratar_db():
                record = await self.ebuy_service.update_by_rfq_id(db, opportunity_id, updated_fields)
                break
        elif source == "custom":
            async for db in get_customer_db():
                record = await self.custom_service.update_by_opportunity_id(db, opportunity_id, updated_fields)
                break
        else:
            logger.error(f"Invalid source type: {source}. Valid types are: sam, ebuy, custom")
            raise ValueError(f"Invalid source type: {source}. Valid types are: sam, ebuy, custom")
        return record

    async def get_content_compliance(self, opportunity_id: str, tenant_id: str, source: str):
        record = await self.get_opportunity(opportunity_id, tenant_id, source)
        if record and getattr(record, "content_compliance", None):
            return str(record.content_compliance)
        else:
            generated_compliance = await self.content_compliance_service.generate_content_compliance(
                opportunity_id, tenant_id, source, is_rfp=False
            )
            updated_fields = {"content_compliance": json.dumps(generated_compliance)}
            await self.update_generated_field(source, opportunity_id, updated_fields)
            return str(generated_compliance)

    async def get_structure_compliance(self, opportunity_id: str, tenant_id: str, source: str) -> Dict[str, Any]:
        structure_compliance = await self.structure_service.generate_structure_compliance(
            opportunity_id, tenant_id, source
        )
        structure = structure_compliance.get("content")
        if not structure:
            return {}

        structure = ProposalUtilities.extract_json_from_brackets(structure)

        if not structure:
            return {}
        
        structure_compliance = structure.get("structure", [{}])
        return structure_compliance[0]

    async def get_tenant(self, tenant_id: str):
        from controllers.customer.tenant_controller import TenantController
        async for db in get_customer_db():
            tenant = await TenantController().get_by_tenant_id(db, tenant_id)
        if tenant is None:
            return None
        return json.dumps(tenant.as_dict())

    async def generate_rfi(self, job_instruction: str, job_submitted_by: str):
        job = json.loads(job_instruction)
        opportunity_id = job.get("opportunityId")
        tenant_id = job.get("tenantId")
        client_short_name = job.get("clientShortName")
        opportunity_type = job.get("opportunityType")
        source_documents = job.get("sourceDocuments", [])
        profile_id = job.get("profileId", None)
        set_for_review = job.get("setForReview", True)
        grading_criteria = job.get("gradingCriteria", "")
        job_instruction_str = job_instruction

        cover_page = job.get("coverPage", None)
        export_type = job.get("exportType", None)

        is_rfp = False

        # Get record - normalize opportunity_type to source format
        source = opportunity_type.lower() if opportunity_type else "custom"
        record = await self.get_opportunity(opportunity_id, tenant_id, source)

        # Safely access toc_text and proposal_outline_1 with fallbacks
        existing_table_of_contents = getattr(record, 'toc_text', None)
        outline = getattr(record, 'proposal_outline_1', None)

        # Generate content_compliance and structure_compliance
        content_compliance = await self.get_content_compliance(opportunity_id, tenant_id, opportunity_type)
        structure_compliance = await self.get_structure_compliance(opportunity_id, tenant_id, opportunity_type)

        # Generate toc_text if missing
        if existing_table_of_contents is None:
            generated_table_of_contents = await self.outline_service.generate_table_of_contents(
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                source=opportunity_type,
                content_compliance=content_compliance,
                is_rfp=is_rfp,
                volume_information=json.dumps(structure_compliance),
            )
            table_of_contents = generated_table_of_contents.get("content")
            await self.update_generated_field(opportunity_type, opportunity_id, {"toc_text": table_of_contents })
            
            if table_of_contents is None:
                return []
            
            table_of_contents = json.loads(table_of_contents)
        else:
            table_of_contents = json.loads(str(existing_table_of_contents))
        

        # Check outline availability using queue system
        if outline is None:
            outline_ready, message = await self._wait_for_outline_or_queue(
                opportunity_id, tenant_id, opportunity_type
            )
            if not outline_ready:
                logger.warning(f"Cannot proceed with RFI generation for {opportunity_id}: {message}")
                raise ValueError(f"Outline not ready: {message}")

            # If we reach here, outline should be ready, try to get it again
            record = await self.get_opportunity(opportunity_id, tenant_id, opportunity_type)
            outline = getattr(record, 'proposal_outline_1', None)

            if outline is None:
                # No fallback generation - outline must be processed through the queue system
                logger.error(f"Outline queue indicates ready but outline not found in database for {opportunity_id}")
                raise ValueError(f"Outline not found in database despite queue status being COMPLETED. This indicates a system error.")
        
        outline_data = outline.get("outlines", []) if isinstance(outline, dict) else outline

        # Get metadata
        try:
            opportunity_metadata = json.dumps(record.as_dict()) if hasattr(record, 'as_dict') else json.dumps({})
        except Exception as e:
            logger.warning(f"Could not serialize record metadata: {e}")
            opportunity_metadata = json.dumps({})
        tenant_metadata = await self.get_tenant(tenant_id)

        # Generate RFI sections using RFISection
        rfi_section_generator = RFISection(
            opportunity_metadata=opportunity_metadata,
            technical_requirements="",  # Add if needed
            tenant_id=tenant_id,
            client=client_short_name,
            tenant_metadata=tenant_metadata or '',
            grading_criteria=grading_criteria,
            internet_search_results=None,
            source_documents_context=None,
            profile_id=profile_id
        )

        proposal = []
        for idx, item in enumerate(table_of_contents):
            title = item.get("title", "")
            description = item.get("description", "")

            outline_guide = outline_data[idx].get("content", "")

            logger.info(f"Generating RFI section header for '{title}'")
            header = await rfi_section_generator.generate_rfi_section_header(title, description, outline_guide)
            section = ProposalUtilities.extract_json_from_brackets(header)
            
            if section is None:
                logger.error(f"Failed to generate header for section '{title}'. Exiting pipeline.")
                proposal.append(None)
                continue
            
            section["subsections"] = []
            for i, subsection in enumerate(item.get("subsections", [])):
                sub_title = subsection.get("title", "")
                sub_description = subsection.get("description", "")
                try:
                    sub_outline_guide = outline_data[idx].get("subsections", [])[i]
                except (IndexError, KeyError, TypeError):
                    sub_outline_guide = ""
                logger.info(f"Generating RFI subsection for '{sub_title}'")
                subsection_content = await rfi_section_generator.generate_rfi_subsection(sub_title, sub_description, sub_outline_guide)
                subsection_json = ProposalUtilities.extract_json_from_brackets(subsection_content)
                section["subsections"].append(subsection_json)
            proposal.append(section)

        # Encrypt and save to proposals in review
        if set_for_review:
            await self.move_to_review(
                volumes_data=proposal,
                tenant_id=tenant_id,
                opportunity_id=opportunity_id,
                source=opportunity_type,
                client_short_name=client_short_name,
                job_instruction=job_instruction_str,
                volume_number=1
            )
        else:
            await self.move_to_format(
                volume=proposal,
                tenant_id=tenant_id,
                opportunity_id=opportunity_id,
                source=opportunity_type,
                client_short_name=client_short_name,
                cover_page=cover_page,
                format_type=export_type,
                job_submitted_by=job_submitted_by
            )

        # # Automatically queue criticism analysis after successful proposal generation
        # await self._queue_criticism_analysis(
        #     opportunity_id=opportunity_id,
        #     tenant_id=tenant_id,
        #     client_short_name=client_short_name,
        #     submitted_by=job_submitted_by
        # )

    async def move_to_format(self, volume: List[Dict[str, Any]], tenant_id: str, opportunity_id: str, source: str, client_short_name: str, cover_page: int, format_type: int, job_submitted_by: str):
        """
        Move all proposal volumes to review
        """
        volume_content = json.dumps(volume).encode('utf-8')
                    
        async for db in get_customer_db():
            # Encrypt the volume data
            encrypted_data = await self.proposal_decoding_service.encrypt_with_public_key(
                db, tenant_id, volume_content
            )
            current_version = await self.proposal_format_queue.get_current_version(db, tenant_id, opportunity_id)
            next_version = current_version + 1 if current_version is not None else 1
            
            await self.proposal_format_queue.add(
                db=db,
                client_short_name=client_short_name,
                tenant_id=tenant_id,
                opps_id=opportunity_id,
                version=next_version,
                format_type=format_type,  # You may want to set this dynamically
                proposal_data=encrypted_data,
                job_submitted_by=job_submitted_by,  # Or another field if available
                is_rfp=True,
                cover_page=cover_page,  # Set as needed
                opp_source=source.upper()
            )
    
    async def move_to_review(self, volumes_data: List[Dict[str, Any]], tenant_id: str, opportunity_id: str, source: str, client_short_name: str, job_instruction: str, volume_number: int = 1):
        results = []
        async for db in get_customer_db():
            try:
                for section_number, volume_data in enumerate(volumes_data, 1):
                    if not volume_data:
                        continue
                    volume_content = json.dumps(volume_data).encode('utf-8')
                    encrypted_data = await self.proposal_decoding_service.encrypt_with_public_key(
                        db, tenant_id, volume_content
                    )
                    current_version = await ProposalsInReviewController.get_current_version(
                        db, tenant_id, opportunity_id
                    )
                    next_version = current_version + 1 if current_version is not None else 1
                    review_record = await ProposalsInReviewController.add(
                        db=db,
                        client_short_name=client_short_name,
                        tenant_id=tenant_id,
                        section_number=str(section_number),
                        opps_id=opportunity_id,
                        volume_number=volume_number,
                        version=next_version,
                        opps_type=source.upper(),
                        proposal_data=encrypted_data,
                        job_instruction=job_instruction
                    )
                    if review_record:
                        logger.info(f"Successfully moved RFI to review for opportunity {opportunity_id}")
                        results.append(review_record)
                    else:
                        logger.error(f"Failed to save RFI to review table")
            except Exception as e:
                logger.error(f"Error moving RFI to review: {e}")
            finally:
                break
        return results

    # async def _queue_criticism_analysis(
    #     self,
    #     opportunity_id: str,
    #     tenant_id: str,
    #     client_short_name: str,
    #     submitted_by: str = "system"
    # ):
    #     """
    #     Automatically queue criticism analysis for the generated proposal.
    #     This enables continuous model improvement through automated feedback.
    #     """
    #     try:
    #         async for db in get_customer_db():
    #             criticism_item = await ProposalCriticismQueueService.create_item(
    #                 db=db,
    #                 opportunity_id=opportunity_id,
    #                 tenant_id=tenant_id,
    #                 client_short_name=client_short_name,
    #                 priority=1,
    #                 analysis_type="full",
    #                 submitted_by=f"auto_{submitted_by}"
    #             )

    #             if criticism_item:
    #                 logger.info(f"Successfully queued criticism analysis for RFI opportunity {opportunity_id}")
    #             else:
    #                 logger.warning(f"Failed to queue criticism analysis for RFI opportunity {opportunity_id}")
    #             break

    #     except Exception as e:
    #         logger.error(f"Error queuing criticism analysis for RFI opportunity {opportunity_id}: {e}")
