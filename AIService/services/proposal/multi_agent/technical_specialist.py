"""
Technical Specialist Agent

Specialized agent for generating technical content including approaches,
methodologies, architectures, and implementation details.
"""

import logging
from typing import Dict, Any, List
from langchain_core.messages import BaseMessage

from .base_agent import BaseAgent
from .agent_state import DraftState, AgentResult, AgentRole

logger = logging.getLogger(__name__)


class TechnicalSpecialist(BaseAgent):
    """
    Generates technical content for proposals.
    
    Responsibilities:
    - Create technical approach sections
    - Develop implementation methodologies
    - Design system architectures
    - Address technical requirements
    - Provide technical risk assessments
    """
    
    def __init__(self):
        """Initialize the technical specialist"""
        super().__init__(AgentRole.TECHNICAL_SPECIALIST)
    
    async def process(self, state: DraftState) -> AgentResult:
        """
        Generate technical content based on requirements.
        
        Args:
            state: Current draft state with context and requirements
            
        Returns:
            AgentResult with generated technical content
        """
        if not self._validate_state(state):
            return self._create_failure_result("Invalid draft state provided")
        
        try:
            # Generate technical content based on section type
            content = await self._generate_technical_content(state)
            
            # Prepare result metadata
            metadata = {
                'section_type': state.section_type.value,
                'content_length': len(content),
                'opportunity_id': state.opportunity_id,
                'generation_approach': 'technical_focused',
                'complexity_level': state.complexity_level.value if hasattr(state, 'complexity_level') else 'medium'
            }
            
            return self._create_success_result(content=content, metadata=metadata)
            
        except Exception as e:
            logger.error(f"TechnicalSpecialist: Content generation failed: {e}")
            # NO FALLBACK - fail cleanly to ensure high quality
            raise e
    
    async def _generate_technical_content(self, state: DraftState) -> str:
        """Generate comprehensive technical content"""
        
        # Prepare context for LLM
        context_str = self._format_context_for_llm(
            state.context_data.to_dict() if state.context_data else {}
        )
        
        # Get key requirements
        key_requirements = self._extract_key_requirements(state.section_content)
        
        # Determine technical focus based on section type
        technical_focus = self._get_technical_focus(state.section_type.value)
        
        system_prompt = f"""You are a senior technical architect and proposal specialist with expertise in {technical_focus}.

Your technical expertise includes:
- System architecture and design
- Implementation methodologies and best practices
- Technology stack selection and integration
- Risk assessment and mitigation strategies
- Performance optimization and scalability
- Security and compliance considerations
- Quality assurance and testing approaches

Create technical content that:
- Demonstrates deep technical understanding
- Provides clear, implementable solutions
- Addresses technical risks and mitigations
- Shows innovation and best practices
- Maintains technical accuracy and feasibility
- Balances detail with clarity for mixed audiences"""

        user_content = f"""Generate technical content for the {state.section_type.value} section:

Opportunity: {state.opportunity_id}
Client: {state.client_short_name}
Technical Focus: {technical_focus}

Section Requirements:
{state.section_content or f'Standard {state.section_type.value} technical requirements'}

Key Requirements:
{chr(10).join(f"- {req}" for req in key_requirements) if key_requirements else "- Address all technical requirements comprehensively"}

Relevant Context:
{context_str}

Technical Guidelines:
{chr(10).join(f"- {inst}" for inst in self._get_agent_specific_instructions())}

Please generate comprehensive technical content that:
1. Provides a clear technical approach and methodology
2. Addresses all technical requirements systematically
3. Demonstrates technical expertise and innovation
4. Includes implementation details and timelines
5. Addresses potential risks and mitigation strategies
6. Shows understanding of technical constraints
7. Provides measurable technical outcomes

Structure the content with clear sections and technical depth appropriate for the audience."""

        messages = self._create_messages(system_prompt, user_content)
        return await self._call_llm(messages)
    
    def _get_technical_focus(self, section_type: str) -> str:
        """Determine technical focus based on section type"""
        focus_mapping = {
            "technical_approach": "solution architecture and implementation methodology",
            "pricing": "technical cost estimation and resource planning",
            "appendix": "detailed technical specifications and documentation",
            "compliance": "technical compliance and security requirements",
            "management_plan": "technical project management and delivery",
            "past_performance": "technical achievements and case studies"
        }
        
        return focus_mapping.get(section_type, "comprehensive technical solutions")
    
    def _get_agent_specific_instructions(self) -> List[str]:
        """Get technical specialist specific instructions"""
        return [
            "Provide detailed technical solutions and approaches",
            "Include specific methodologies and implementation steps",
            "Address technical risks and mitigation strategies",
            "Demonstrate technical innovation and best practices",
            "Ensure technical feasibility and accuracy",
            "Include performance and scalability considerations",
            "Address security and compliance requirements",
            "Provide clear technical documentation and specifications",
            "Balance technical depth with accessibility",
            "Include measurable technical outcomes and metrics"
        ]
    
    async def generate_architecture_overview(self, state: DraftState) -> str:
        """
        Generate a technical architecture overview.
        
        Args:
            state: Draft state with context
            
        Returns:
            Technical architecture description
        """
        try:
            system_prompt = """You are a senior system architect specializing in solution design.

Create a clear, comprehensive architecture overview that:
- Describes the overall system architecture
- Explains key components and their interactions
- Addresses scalability and performance
- Includes security and compliance considerations
- Shows integration points and data flows
- Demonstrates technical best practices"""

            context_str = self._format_context_for_llm(
                state.context_data.to_dict() if state.context_data else {}
            )

            user_content = f"""Create a technical architecture overview for:

Opportunity: {state.opportunity_id}
Client: {state.client_short_name}

Requirements:
{state.section_content or 'Standard architecture requirements'}

Context:
{context_str}

Generate a comprehensive architecture overview that explains the technical solution design and implementation approach."""

            messages = self._create_messages(system_prompt, user_content)
            return await self._call_llm(messages)
            
        except Exception as e:
            logger.error(f"TechnicalSpecialist: Architecture overview generation failed: {e}")
            raise e
    
    async def generate_implementation_methodology(self, state: DraftState) -> str:
        """
        Generate implementation methodology and approach.
        
        Args:
            state: Draft state with context
            
        Returns:
            Implementation methodology description
        """
        try:
            system_prompt = """You are an expert in technical implementation methodologies.

Create a detailed implementation methodology that:
- Outlines the step-by-step implementation approach
- Includes project phases and milestones
- Addresses risk management and quality assurance
- Shows resource allocation and timeline
- Demonstrates proven methodologies
- Includes testing and validation approaches"""

            context_str = self._format_context_for_llm(
                state.context_data.to_dict() if state.context_data else {}
            )

            user_content = f"""Create an implementation methodology for:

Opportunity: {state.opportunity_id}
Client: {state.client_short_name}

Requirements:
{state.section_content or 'Standard implementation requirements'}

Context:
{context_str}

Generate a comprehensive implementation methodology that shows how the technical solution will be delivered successfully."""

            messages = self._create_messages(system_prompt, user_content)
            return await self._call_llm(messages)
            
        except Exception as e:
            logger.error(f"TechnicalSpecialist: Implementation methodology generation failed: {e}")
            raise e
    
    async def generate_risk_assessment(self, state: DraftState) -> str:
        """
        Generate technical risk assessment and mitigation strategies.
        
        Args:
            state: Draft state with context
            
        Returns:
            Risk assessment and mitigation plan
        """
        try:
            system_prompt = """You are a technical risk management expert.

Create a comprehensive risk assessment that:
- Identifies potential technical risks
- Assesses risk probability and impact
- Provides specific mitigation strategies
- Includes contingency planning
- Shows risk monitoring approaches
- Demonstrates proactive risk management"""

            context_str = self._format_context_for_llm(
                state.context_data.to_dict() if state.context_data else {}
            )

            user_content = f"""Create a technical risk assessment for:

Opportunity: {state.opportunity_id}
Client: {state.client_short_name}

Technical Requirements:
{state.section_content or 'Standard technical requirements'}

Context:
{context_str}

Generate a comprehensive risk assessment that identifies technical risks and provides detailed mitigation strategies."""

            messages = self._create_messages(system_prompt, user_content)
            return await self._call_llm(messages)
            
        except Exception as e:
            logger.error(f"TechnicalSpecialist: Risk assessment generation failed: {e}")
            raise e
