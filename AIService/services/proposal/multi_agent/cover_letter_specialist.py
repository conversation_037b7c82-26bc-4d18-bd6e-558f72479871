"""
Cover Letter Specialist Agent

Specialized agent for generating executive summaries, cover letters, and
high-level proposal content that appeals to decision makers.
"""

import logging
from typing import Dict, Any, List
from langchain_core.messages import BaseMessage

from .base_agent import BaseAgent
from .agent_state import DraftState, AgentResult, AgentRole

logger = logging.getLogger(__name__)


class CoverLetterSpecialist(BaseAgent):
    """
    Generates executive-level content including cover letters and summaries.
    
    Responsibilities:
    - Create compelling cover letters
    - Generate executive summaries
    - Develop value propositions
    - Craft decision-maker focused messaging
    """
    
    def __init__(self):
        """Initialize the cover letter specialist"""
        super().__init__(AgentRole.COVER_LETTER_SPECIALIST)
    
    async def process(self, state: DraftState) -> AgentResult:
        """
        Generate cover letter or executive summary content.
        
        Args:
            state: Current draft state with context and requirements
            
        Returns:
            AgentResult with generated content
        """
        if not self._validate_state(state):
            return self._create_failure_result("Invalid draft state provided")
        
        try:
            # Generate content based on section type
            if state.section_type.value in ["cover_letter", "executive_summary"]:
                content = await self._generate_executive_content(state)
            else:
                content = await self._generate_section_content(state)
            
            # Prepare result metadata
            metadata = {
                'section_type': state.section_type.value,
                'content_length': len(content),
                'opportunity_id': state.opportunity_id,
                'generation_approach': 'executive_focused'
            }
            
            return self._create_success_result(content=content, metadata=metadata)
            
        except Exception as e:
            logger.error(f"CoverLetterSpecialist: Content generation failed: {e}")
            # NO FALLBACK - fail cleanly to ensure high quality
            raise e
    
    async def _generate_executive_content(self, state: DraftState) -> str:
        """Generate executive-level content (cover letters, summaries)"""
        
        # Prepare context for LLM
        context_str = self._format_context_for_llm(
            state.context_data.to_dict() if state.context_data else {}
        )
        
        # Get key requirements
        key_requirements = self._extract_key_requirements(state.section_content)
        
        system_prompt = f"""You are an expert proposal writer specializing in executive-level content for {state.section_type.value} sections.

Your expertise includes:
- Crafting compelling value propositions
- Executive-level messaging and tone
- Strategic positioning and differentiation
- Decision-maker focused communication
- Professional proposal writing standards

Create content that:
- Captures executive attention immediately
- Clearly articulates value and benefits
- Demonstrates understanding of client needs
- Positions the organization as the ideal choice
- Maintains professional, confident tone"""

        user_content = f"""Generate a {state.section_type.value} for this proposal:

Opportunity: {state.opportunity_id}
Client: {state.client_short_name}

Section Requirements:
{state.section_content or 'Standard executive summary requirements'}

Key Requirements:
{chr(10).join(f"- {req}" for req in key_requirements) if key_requirements else "- Address client needs comprehensively"}

Relevant Context:
{context_str}

Special Instructions:
{chr(10).join(f"- {inst}" for inst in self._get_agent_specific_instructions())}

Please generate a compelling, executive-focused {state.section_type.value} that:
1. Opens with a strong value proposition
2. Demonstrates clear understanding of client needs
3. Highlights key differentiators and capabilities
4. Provides confidence in successful delivery
5. Concludes with a compelling call to action

Ensure the content is professional, persuasive, and tailored to executive decision-makers."""

        messages = self._create_messages(system_prompt, user_content)
        return await self._call_llm(messages)
    
    async def _generate_section_content(self, state: DraftState) -> str:
        """Generate content for other section types with executive focus"""
        
        context_str = self._format_context_for_llm(
            state.context_data.to_dict() if state.context_data else {}
        )
        
        key_requirements = self._extract_key_requirements(state.section_content)
        
        system_prompt = f"""You are an expert proposal writer with executive communication expertise, now working on a {state.section_type.value} section.

Apply executive-level communication principles:
- Clear, strategic messaging
- Focus on value and outcomes
- Professional, confident tone
- Decision-maker perspective
- Strategic positioning

Ensure the content demonstrates:
- Deep understanding of requirements
- Strategic approach to solutions
- Clear value proposition
- Professional expertise
- Compelling differentiation"""

        user_content = f"""Create content for the {state.section_type.value} section:

Opportunity: {state.opportunity_id}
Client: {state.client_short_name}

Section Requirements:
{state.section_content or f'Standard {state.section_type.value} requirements'}

Key Requirements:
{chr(10).join(f"- {req}" for req in key_requirements) if key_requirements else "- Address all section requirements comprehensively"}

Relevant Context:
{context_str}

Generate comprehensive {state.section_type.value} content that:
1. Addresses all requirements systematically
2. Demonstrates expertise and capability
3. Provides clear, actionable solutions
4. Maintains executive-level quality
5. Differentiates from competitors

Ensure the content is detailed, professional, and compelling while maintaining strategic focus."""

        messages = self._create_messages(system_prompt, user_content)
        return await self._call_llm(messages)
    
    def _get_agent_specific_instructions(self) -> List[str]:
        """Get cover letter specialist specific instructions"""
        return [
            "Focus on executive-level messaging and tone",
            "Emphasize value proposition and benefits",
            "Demonstrate clear understanding of client needs",
            "Highlight key differentiators and competitive advantages",
            "Maintain professional, confident communication style",
            "Structure content for maximum executive impact",
            "Include compelling calls to action",
            "Ensure strategic positioning throughout"
        ]
    
    async def generate_value_proposition(self, state: DraftState) -> str:
        """
        Generate a focused value proposition.
        
        Args:
            state: Draft state with context
            
        Returns:
            Compelling value proposition text
        """
        try:
            system_prompt = """You are an expert at crafting compelling value propositions for proposals.

Create a concise, powerful value proposition that:
- Clearly states the unique value offered
- Addresses specific client needs
- Differentiates from competitors
- Focuses on outcomes and benefits
- Uses compelling, professional language"""

            context_str = self._format_context_for_llm(
                state.context_data.to_dict() if state.context_data else {}
            )

            user_content = f"""Create a value proposition for:

Opportunity: {state.opportunity_id}
Client: {state.client_short_name}
Section: {state.section_type.value}

Context:
{context_str}

Generate a compelling 2-3 sentence value proposition that captures why this client should choose our solution."""

            messages = self._create_messages(system_prompt, user_content)
            return await self._call_llm(messages)
            
        except Exception as e:
            logger.error(f"CoverLetterSpecialist: Value proposition generation failed: {e}")
            raise e
    
    async def generate_executive_summary_points(self, state: DraftState) -> List[str]:
        """
        Generate key executive summary points.
        
        Args:
            state: Draft state with context
            
        Returns:
            List of key executive summary points
        """
        try:
            system_prompt = """You are an expert at creating executive summary bullet points.

Generate 5-7 key points that executives need to know:
- Strategic value and benefits
- Key capabilities and differentiators
- Risk mitigation and assurance
- Implementation approach
- Expected outcomes

Each point should be concise but compelling."""

            context_str = self._format_context_for_llm(
                state.context_data.to_dict() if state.context_data else {}
            )

            user_content = f"""Generate executive summary points for:

Opportunity: {state.opportunity_id}
Client: {state.client_short_name}

Context:
{context_str}

Create 5-7 bullet points that capture the most important information for executive decision-makers."""

            messages = self._create_messages(system_prompt, user_content)
            response = await self._call_llm(messages)
            
            # Parse response into list of points
            points = [line.strip() for line in response.split('\n') if line.strip() and ('•' in line or '-' in line or line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.')))]
            
            return points[:7]  # Limit to 7 points
            
        except Exception as e:
            logger.error(f"CoverLetterSpecialist: Executive summary points generation failed: {e}")
            raise e
