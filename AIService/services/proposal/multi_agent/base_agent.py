"""
Base Agent Class

Provides the foundational functionality for all specialized agents in the
multi-agent proposal generation system.
"""

import time
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from langchain_core.messages import SystemMessage, HumanMessage, BaseMessage

from .agent_state import DraftState, AgentResult, AgentRole
from .robust_llm import RobustLLMManager

logger = logging.getLogger(__name__)


class BaseAgent(ABC):
    """
    Abstract base class for all agents in the multi-agent system.
    
    Provides common functionality including:
    - Robust LLM management
    - Standardized logging
    - Error handling patterns
    - Result formatting
    """
    
    def __init__(self, agent_role: AgentR<PERSON>):
        """Initialize the base agent"""
        self.agent_role = agent_role
        self.llm_manager = RobustLLMManager()
        self.logger = logging.getLogger(f"{__name__}.{agent_role.value}")
        
        self.logger.info(f"{agent_role.value}: Initialized robust LLM manager")
    
    @abstractmethod
    async def process(self, state: DraftState) -> AgentResult:
        """
        Process the draft state and return results.
        
        This method must be implemented by all concrete agent classes.
        
        Args:
            state: Current draft state containing all context and progress
            
        Returns:
            AgentResult with the processing outcome
        """
        pass
    
    async def _call_llm(self, messages: List[BaseMessage]) -> str:
        """
        Make a robust LLM call with error handling.
        
        Args:
            messages: List of messages to send to the LLM
            
        Returns:
            Generated content string
            
        Raises:
            Exception: If LLM call fails after all retries
        """
        try:
            return await self.llm_manager.generate_content(messages)
        except Exception as e:
            self.logger.error(f"{self.agent_role.value}: LLM call failed: {e}")
            # NO FALLBACK - fail cleanly to ensure high quality
            raise e
    
    def _create_system_message(self, system_prompt: str) -> SystemMessage:
        """Create a system message with the given prompt"""
        return SystemMessage(content=system_prompt)
    
    def _create_human_message(self, content: str) -> HumanMessage:
        """Create a human message with the given content"""
        return HumanMessage(content=content)
    
    def _create_messages(self, system_prompt: str, user_content: str) -> List[BaseMessage]:
        """Create a standard message list with system and user messages"""
        return [
            self._create_system_message(system_prompt),
            self._create_human_message(user_content)
        ]
    
    def _format_context_for_llm(self, context_data: Dict[str, List[str]]) -> str:
        """
        Format context data for inclusion in LLM prompts.
        
        Args:
            context_data: Dictionary of context categories and their content
            
        Returns:
            Formatted context string
        """
        if not context_data:
            return "No specific context available."
        
        formatted_sections = []
        
        for category, items in context_data.items():
            if items:
                section = f"\n{category.replace('_', ' ').title()}:\n"
                for i, item in enumerate(items[:5], 1):  # Limit to top 5 items
                    section += f"{i}. {item}\n"
                formatted_sections.append(section)
        
        return "\n".join(formatted_sections) if formatted_sections else "No specific context available."
    
    def _extract_key_requirements(self, section_content: str) -> List[str]:
        """
        Extract key requirements from section content.
        
        Args:
            section_content: Raw section content to analyze
            
        Returns:
            List of key requirements
        """
        if not section_content:
            return []
        
        # Simple extraction - can be enhanced with NLP
        lines = section_content.split('\n')
        requirements = []
        
        for line in lines:
            line = line.strip()
            if line and (
                'must' in line.lower() or 
                'shall' in line.lower() or 
                'required' in line.lower() or
                'should' in line.lower()
            ):
                requirements.append(line)
        
        return requirements[:10]  # Limit to top 10
    
    def _create_success_result(self, content: str, metadata: Optional[Dict[str, Any]] = None) -> AgentResult:
        """
        Create a successful agent result.
        
        Args:
            content: Generated content
            metadata: Optional metadata about the processing
            
        Returns:
            AgentResult indicating success
        """
        return AgentResult(
            agent_role=self.agent_role,
            success=True,
            content=content,
            metadata=metadata or {},
            processing_time=0.0  # Will be set by caller if needed
        )
    
    def _create_failure_result(self, error_message: str, metadata: Optional[Dict[str, Any]] = None) -> AgentResult:
        """
        Create a failed agent result.
        
        Args:
            error_message: Description of the failure
            metadata: Optional metadata about the processing
            
        Returns:
            AgentResult indicating failure
        """
        return AgentResult(
            agent_role=self.agent_role,
            success=False,
            error_message=error_message,
            metadata=metadata or {},
            processing_time=0.0  # Will be set by caller if needed
        )
    
    async def _timed_process(self, state: DraftState) -> AgentResult:
        """
        Execute the process method with timing.
        
        Args:
            state: Draft state to process
            
        Returns:
            AgentResult with processing time included
        """
        start_time = time.time()
        
        try:
            result = await self.process(state)
            result.processing_time = time.time() - start_time
            
            if result.success:
                self.logger.info(f"{self.agent_role.value}: Processing completed successfully in {result.processing_time:.2f}s")
            else:
                self.logger.error(f"{self.agent_role.value}: Processing failed after {result.processing_time:.2f}s: {result.error_message}")
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Unexpected error during processing: {str(e)}"
            
            self.logger.error(f"{self.agent_role.value}: {error_msg} (after {processing_time:.2f}s)")
            
            result = self._create_failure_result(error_msg)
            result.processing_time = processing_time
            return result
    
    def get_health_status(self) -> Dict[str, Any]:
        """
        Get health status of the agent and its LLM manager.
        
        Returns:
            Dictionary containing health information
        """
        return {
            'agent_role': self.agent_role.value,
            'llm_health': self.llm_manager.get_health_status(),
            'status': 'healthy'  # Can be enhanced with more sophisticated health checks
        }
    
    def _validate_state(self, state: DraftState) -> bool:
        """
        Validate that the draft state contains required information.
        
        Args:
            state: Draft state to validate
            
        Returns:
            True if state is valid, False otherwise
        """
        if not state:
            self.logger.error(f"{self.agent_role.value}: Draft state is None")
            return False
        
        if not state.opportunity_id:
            self.logger.error(f"{self.agent_role.value}: Missing opportunity_id in state")
            return False
        
        if not state.tenant_id:
            self.logger.error(f"{self.agent_role.value}: Missing tenant_id in state")
            return False
        
        return True
    
    def _get_agent_specific_instructions(self) -> List[str]:
        """
        Get agent-specific instructions for content generation.

        Returns:
            List of instructions specific to this agent type
        """
        # Base implementation - should be overridden by specific agents
        return [
            "Generate high-quality, professional content",
            "Ensure accuracy and relevance to the opportunity",
            "Follow industry best practices",
            "Maintain consistency with proposal standards"
        ]
