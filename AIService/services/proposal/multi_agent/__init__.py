"""
Multi-Agent Proposal Generation System

This module provides a sophisticated multi-agent system for generating high-quality
proposal content using specialized AI agents.

Key Components:
- CoordinatorAgent: Orchestrates the overall workflow
- ContextRetrievalAgent: Retrieves relevant context from knowledge bases
- CoverLetterSpecialist: Generates executive summaries and cover letters
- TechnicalSpecialist: Creates technical content and solutions
- ManagementSpecialist: Develops project management and organizational content
- ComplianceAgent: Ensures regulatory and requirement compliance
- QualityAssuranceAgent: Reviews and validates content quality

Features:
- Robust LLM management with automatic retries
- Clean failure handling (no fallback content)
- Comprehensive logging and monitoring
- Modular and extensible architecture
"""

from .coordinator_agent import CoordinatorAgent
from .context_retrieval_agent import ContextRetrievalAgent
from .cover_letter_specialist import CoverLetterSpecialist
from .technical_specialist import TechnicalSpecialist
from .management_specialist import ManagementSpecialist
from .compliance_agent import ComplianceAgent
from .quality_assurance_agent import QualityAssuranceAgent
from .workflow import MultiAgentWorkflow
from .agent_state import DraftState, AgentResult
from .base_agent import BaseAgent
from .robust_llm import RobustLLMManager
from .llm_config import LLMConfig

__all__ = [
    'CoordinatorAgent',
    'ContextRetrievalAgent', 
    'CoverLetterSpecialist',
    'TechnicalSpecialist',
    'ManagementSpecialist',
    'ComplianceAgent',
    'QualityAssuranceAgent',
    'MultiAgentWorkflow',
    'DraftState',
    'AgentResult',
    'BaseAgent',
    'RobustLLMManager',
    'LLMConfig'
]

__version__ = "1.0.0"
