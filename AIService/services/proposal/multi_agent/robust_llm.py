"""
Robust LLM Manager

Provides intelligent retry logic, health monitoring, and endpoint management
for LLM interactions. Ensures high availability and graceful failure handling.
"""

import asyncio
import time
import random
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
import logging
from langchain_ollama import ChatOllama
from langchain_core.messages import BaseMessage
from .llm_config import LLMConfig, LLMEndpoint

logger = logging.getLogger(__name__)


class EndpointStatus(Enum):
    """Health status of an LLM endpoint"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class EndpointHealth:
    """Health tracking for an LLM endpoint"""
    status: EndpointStatus = EndpointStatus.UNKNOWN
    last_check: float = field(default_factory=time.time)
    consecutive_failures: int = 0
    consecutive_successes: int = 0
    total_requests: int = 0
    total_failures: int = 0
    average_response_time: float = 0.0


class RobustLLMManager:
    """
    Manages LLM interactions with intelligent retry logic and health monitoring.
    
    Features:
    - Automatic retry with exponential backoff
    - Health monitoring of endpoints
    - Load balancing across multiple endpoints
    - Clean failure handling (no fallback content)
    """
    
    def __init__(self, endpoints: Optional[List[LLMEndpoint]] = None):
        """Initialize the robust LLM manager"""
        self.endpoints = endpoints or LLMConfig.get_default_endpoints()
        self.retry_config = LLMConfig.get_retry_config()
        self.health_config = LLMConfig.get_health_check_config()
        
        # Initialize health tracking
        self.endpoint_health: Dict[str, EndpointHealth] = {}
        self.llm_instances: Dict[str, ChatOllama] = {}
        
        # Initialize LLM instances and health tracking
        for endpoint in self.endpoints:
            self._initialize_endpoint(endpoint)
        
        logger.info(f"RobustLLMManager: Initialized with {len(self.endpoints)} endpoints")
    
    def _initialize_endpoint(self, endpoint: LLMEndpoint) -> None:
        """Initialize a single LLM endpoint"""
        try:
            # Create LLM instance
            llm = ChatOllama(
                base_url=endpoint.url,
                model=endpoint.model,
                timeout=endpoint.timeout
            )
            
            self.llm_instances[endpoint.url] = llm
            self.endpoint_health[endpoint.url] = EndpointHealth()
            
            logger.info(f"RobustLLMManager: Initialized LLM instance for {endpoint.url}")
            
        except Exception as e:
            logger.error(f"RobustLLMManager: Failed to initialize endpoint {endpoint.url}: {e}")
            self.endpoint_health[endpoint.url] = EndpointHealth(status=EndpointStatus.UNHEALTHY)
    
    def _get_healthy_endpoints(self) -> List[LLMEndpoint]:
        """Get list of currently healthy endpoints"""
        healthy = []
        for endpoint in self.endpoints:
            health = self.endpoint_health.get(endpoint.url)
            if health and health.status != EndpointStatus.UNHEALTHY:
                healthy.append(endpoint)
        
        # If no endpoints are marked healthy, try all (they might have recovered)
        return healthy if healthy else self.endpoints
    
    def _select_endpoint(self) -> Optional[LLMEndpoint]:
        """Select the best available endpoint using weighted random selection"""
        healthy_endpoints = self._get_healthy_endpoints()
        
        if not healthy_endpoints:
            return None
        
        # Simple random selection for now (can be enhanced with weighted selection)
        return random.choice(healthy_endpoints)
    
    def _update_endpoint_health(self, endpoint_url: str, success: bool, response_time: float = 0.0) -> None:
        """Update health status for an endpoint"""
        health = self.endpoint_health.get(endpoint_url)
        if not health:
            return
        
        health.last_check = time.time()
        health.total_requests += 1
        
        if success:
            health.consecutive_failures = 0
            health.consecutive_successes += 1
            
            # Update average response time
            if health.total_requests == 1:
                health.average_response_time = response_time
            else:
                health.average_response_time = (
                    (health.average_response_time * (health.total_requests - 1) + response_time) 
                    / health.total_requests
                )
            
            # Mark as healthy if enough consecutive successes
            if health.consecutive_successes >= self.health_config['recovery_threshold']:
                health.status = EndpointStatus.HEALTHY
                
        else:
            health.consecutive_successes = 0
            health.consecutive_failures += 1
            health.total_failures += 1
            
            # Mark as unhealthy if too many consecutive failures
            if health.consecutive_failures >= self.health_config['failure_threshold']:
                health.status = EndpointStatus.UNHEALTHY
                logger.warning(f"RobustLLMManager: Marking endpoint {endpoint_url} as unhealthy")
    
    async def _call_llm_with_retry(self, messages: List[BaseMessage]) -> str:
        """Call LLM with intelligent retry logic"""
        max_attempts = self.retry_config['max_attempts']
        initial_delay = self.retry_config['initial_delay']
        max_delay = self.retry_config['max_delay']
        exponential_base = self.retry_config['exponential_base']
        jitter = self.retry_config['jitter']
        
        last_exception = None
        
        for attempt in range(max_attempts):
            # Select endpoint
            endpoint = self._select_endpoint()
            if not endpoint:
                raise Exception("No healthy LLM endpoints available")
            
            llm = self.llm_instances.get(endpoint.url)
            if not llm:
                continue
            
            try:
                start_time = time.time()
                
                # Make the LLM call
                response = llm.invoke(messages)
                
                response_time = time.time() - start_time
                
                # Update health status (success)
                self._update_endpoint_health(endpoint.url, True, response_time)
                
                # Extract content from response
                if hasattr(response, 'content'):
                    return response.content
                else:
                    return str(response)
                    
            except Exception as e:
                last_exception = e
                
                # Update health status (failure)
                self._update_endpoint_health(endpoint.url, False)
                
                logger.warning(f"RobustLLMManager: Attempt {attempt + 1} failed for {endpoint.url}: {e}")
                
                # Don't retry on the last attempt
                if attempt == max_attempts - 1:
                    break
                
                # Calculate delay with exponential backoff and jitter
                delay = min(initial_delay * (exponential_base ** attempt), max_delay)
                if jitter:
                    delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter
                
                logger.info(f"RobustLLMManager: Retrying in {delay:.2f} seconds...")
                await asyncio.sleep(delay)
        
        # All attempts failed
        logger.error(f"RobustLLMManager: All {max_attempts} attempts failed")
        raise last_exception or Exception("LLM call failed after all retry attempts")
    
    async def generate_content(self, messages: List[BaseMessage]) -> str:
        """
        Generate content using the LLM with robust error handling.
        
        Args:
            messages: List of messages to send to the LLM
            
        Returns:
            Generated content string
            
        Raises:
            Exception: If all retry attempts fail
        """
        try:
            return await self._call_llm_with_retry(messages)
        except Exception as e:
            logger.error(f"RobustLLMManager: Content generation failed: {e}")
            # NO FALLBACK - fail cleanly to ensure high quality
            raise e
    
    def get_health_status(self) -> Dict[str, Dict[str, Any]]:
        """Get health status of all endpoints"""
        status = {}
        for endpoint in self.endpoints:
            health = self.endpoint_health.get(endpoint.url, EndpointHealth())
            status[endpoint.url] = {
                'status': health.status.value,
                'last_check': health.last_check,
                'consecutive_failures': health.consecutive_failures,
                'consecutive_successes': health.consecutive_successes,
                'total_requests': health.total_requests,
                'total_failures': health.total_failures,
                'success_rate': (
                    (health.total_requests - health.total_failures) / health.total_requests 
                    if health.total_requests > 0 else 0.0
                ),
                'average_response_time': health.average_response_time
            }
        return status
