"""
Context Retrieval Agent

Specialized agent for retrieving and organizing relevant context from various
knowledge bases and data sources for proposal content generation.
"""

import logging
from typing import Dict, Any, List, Optional
import asyncio

from .base_agent import BaseAgent
from .agent_state import DraftState, AgentResult, AgentRole, ContextData

logger = logging.getLogger(__name__)


class ContextRetrievalAgent(BaseAgent):
    """
    Retrieves and organizes context from multiple sources.
    
    Responsibilities:
    - Query opportunity-specific information
    - Retrieve client background and preferences
    - Gather technical context and past performance
    - Organize context for optimal use by specialists
    """
    
    def __init__(self):
        """Initialize the context retrieval agent"""
        super().__init__(AgentRole.CONTEXT_RETRIEVAL)
    
    async def process(self, state: DraftState) -> AgentResult:
        """
        Retrieve and organize context for the proposal section.
        
        Args:
            state: Current draft state
            
        Returns:
            AgentResult with organized context data
        """
        if not self._validate_state(state):
            return self._create_failure_result("Invalid draft state provided")
        
        try:
            # Retrieve context from multiple sources
            context_data = await self._retrieve_all_context(state)
            
            # Update state with context
            state.update_context(context_data)
            
            # Prepare result metadata
            metadata = {
                'context_categories': list(context_data.to_dict().keys()),
                'total_context_items': sum(len(items) for items in context_data.to_dict().values()),
                'opportunity_id': state.opportunity_id,
                'client': state.client_short_name
            }
            
            return self._create_success_result(
                content="Context retrieved and organized successfully",
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"ContextRetrievalAgent: Context retrieval failed: {e}")
            # NO FALLBACK - fail cleanly to ensure high quality
            raise e
    
    async def _retrieve_all_context(self, state: DraftState) -> ContextData:
        """
        Retrieve context from all available sources.
        
        Args:
            state: Draft state containing retrieval parameters
            
        Returns:
            ContextData with organized context information
        """
        # Run all context retrieval operations concurrently
        tasks = [
            self._retrieve_opportunity_context(state),
            self._retrieve_client_context(state),
            self._retrieve_technical_context(state),
            self._retrieve_compliance_context(state),
            self._retrieve_past_performance_context(state)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results and handle any exceptions
        opportunity_context = results[0] if not isinstance(results[0], Exception) else []
        client_context = results[1] if not isinstance(results[1], Exception) else []
        technical_context = results[2] if not isinstance(results[2], Exception) else []
        compliance_context = results[3] if not isinstance(results[3], Exception) else []
        past_performance_context = results[4] if not isinstance(results[4], Exception) else []
        
        # Log any exceptions
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                context_types = ["opportunity", "client", "technical", "compliance", "past_performance"]
                logger.warning(f"ContextRetrievalAgent: Failed to retrieve {context_types[i]} context: {result}")
        
        return ContextData(
            opportunity_context=opportunity_context,
            client_context=client_context,
            technical_context=technical_context,
            compliance_context=compliance_context,
            past_performance_context=past_performance_context
        )
    
    async def _retrieve_opportunity_context(self, state: DraftState) -> List[str]:
        """Retrieve opportunity-specific context"""
        try:
            # This would integrate with your ChromaDB or other knowledge base
            # For now, return structured placeholder that indicates the type of context needed
            
            context_items = [
                f"Opportunity {state.opportunity_id} requirements and specifications",
                f"Section-specific requirements for {state.section_type.value}",
                "Key deliverables and success criteria",
                "Timeline and milestone requirements",
                "Budget and resource constraints"
            ]
            
            logger.info(f"ContextRetrievalAgent: Retrieved {len(context_items)} opportunity context items")
            return context_items
            
        except Exception as e:
            logger.error(f"ContextRetrievalAgent: Opportunity context retrieval failed: {e}")
            return []
    
    async def _retrieve_client_context(self, state: DraftState) -> List[str]:
        """Retrieve client-specific context"""
        try:
            context_items = [
                f"Client {state.client_short_name} background and history",
                "Previous successful proposals and approaches",
                "Client preferences and communication style",
                "Organizational structure and key stakeholders",
                "Industry-specific considerations and challenges"
            ]
            
            logger.info(f"ContextRetrievalAgent: Retrieved {len(context_items)} client context items")
            return context_items
            
        except Exception as e:
            logger.error(f"ContextRetrievalAgent: Client context retrieval failed: {e}")
            return []
    
    async def _retrieve_technical_context(self, state: DraftState) -> List[str]:
        """Retrieve technical context and capabilities"""
        try:
            context_items = [
                "Relevant technical capabilities and expertise",
                "Technology stack and implementation approaches",
                "Technical methodologies and best practices",
                "Integration requirements and considerations",
                "Security and compliance technical requirements"
            ]
            
            logger.info(f"ContextRetrievalAgent: Retrieved {len(context_items)} technical context items")
            return context_items
            
        except Exception as e:
            logger.error(f"ContextRetrievalAgent: Technical context retrieval failed: {e}")
            return []
    
    async def _retrieve_compliance_context(self, state: DraftState) -> List[str]:
        """Retrieve compliance and regulatory context"""
        try:
            context_items = [
                "Regulatory requirements and compliance standards",
                "Industry-specific regulations and guidelines",
                "Security and privacy requirements",
                "Quality assurance and certification requirements",
                "Risk management and mitigation strategies"
            ]
            
            logger.info(f"ContextRetrievalAgent: Retrieved {len(context_items)} compliance context items")
            return context_items
            
        except Exception as e:
            logger.error(f"ContextRetrievalAgent: Compliance context retrieval failed: {e}")
            return []
    
    async def _retrieve_past_performance_context(self, state: DraftState) -> List[str]:
        """Retrieve past performance and case studies"""
        try:
            context_items = [
                "Relevant past performance examples and case studies",
                "Similar project outcomes and success metrics",
                "Client testimonials and references",
                "Lessons learned and best practices",
                "Team experience and qualifications"
            ]
            
            logger.info(f"ContextRetrievalAgent: Retrieved {len(context_items)} past performance context items")
            return context_items
            
        except Exception as e:
            logger.error(f"ContextRetrievalAgent: Past performance context retrieval failed: {e}")
            return []
    
    def _get_agent_specific_instructions(self) -> List[str]:
        """Get context retrieval specific instructions"""
        return [
            "Retrieve comprehensive and relevant context",
            "Organize context by category and relevance",
            "Ensure context accuracy and currency",
            "Prioritize high-value context items",
            "Maintain context traceability and sources"
        ]
    
    async def retrieve_specific_context(self, 
                                      state: DraftState, 
                                      context_type: str, 
                                      query: str) -> List[str]:
        """
        Retrieve specific context based on query.
        
        Args:
            state: Draft state for context
            context_type: Type of context to retrieve
            query: Specific query for context retrieval
            
        Returns:
            List of relevant context items
        """
        try:
            # This method can be used by other agents for targeted context retrieval
            logger.info(f"ContextRetrievalAgent: Retrieving {context_type} context for query: {query}")
            
            # Implementation would depend on your specific knowledge base
            # For now, return a structured response
            context_items = [
                f"Context item 1 for {context_type}: {query}",
                f"Context item 2 for {context_type}: {query}",
                f"Context item 3 for {context_type}: {query}"
            ]
            
            return context_items
            
        except Exception as e:
            logger.error(f"ContextRetrievalAgent: Specific context retrieval failed: {e}")
            return []
