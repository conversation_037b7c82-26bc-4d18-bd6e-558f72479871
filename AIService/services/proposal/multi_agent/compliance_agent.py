"""
Compliance Agent

Specialized agent for ensuring regulatory compliance, requirement adherence,
and quality standards in proposal content.
"""

import logging
from typing import Dict, Any, List
from langchain_core.messages import BaseMessage

from .base_agent import BaseAgent
from .agent_state import DraftState, AgentResult, AgentRole

logger = logging.getLogger(__name__)


class ComplianceAgent(BaseAgent):
    """
    Ensures compliance and requirement adherence in proposals.
    
    Responsibilities:
    - Verify requirement compliance
    - Check regulatory adherence
    - Validate quality standards
    - Generate compliance matrices
    - Ensure documentation standards
    """
    
    def __init__(self):
        """Initialize the compliance agent"""
        super().__init__(AgentRole.COMPLIANCE_AGENT)
    
    async def process(self, state: DraftState) -> AgentResult:
        """
        Generate compliance-focused content and validation.
        
        Args:
            state: Current draft state with context and requirements
            
        Returns:
            AgentResult with compliance content and validation
        """
        if not self._validate_state(state):
            return self._create_failure_result("Invalid draft state provided")
        
        try:
            # Generate compliance content based on section type
            content = await self._generate_compliance_content(state)
            
            # Prepare result metadata
            metadata = {
                'section_type': state.section_type.value,
                'content_length': len(content),
                'opportunity_id': state.opportunity_id,
                'generation_approach': 'compliance_focused',
                'compliance_areas': self._identify_compliance_areas(state)
            }
            
            return self._create_success_result(content=content, metadata=metadata)
            
        except Exception as e:
            logger.error(f"ComplianceAgent: Content generation failed: {e}")
            # NO FALLBACK - fail cleanly to ensure high quality
            raise e
    
    async def _generate_compliance_content(self, state: DraftState) -> str:
        """Generate comprehensive compliance content"""
        
        # Prepare context for LLM
        context_str = self._format_context_for_llm(
            state.context_data.to_dict() if state.context_data else {}
        )
        
        # Get key requirements
        key_requirements = self._extract_key_requirements(state.section_content)
        
        # Identify compliance areas
        compliance_areas = self._identify_compliance_areas(state)
        
        system_prompt = f"""You are a compliance and regulatory expert specializing in proposal requirements and standards.

Your compliance expertise includes:
- Regulatory compliance and adherence
- Quality standards and certifications
- Documentation requirements and standards
- Risk assessment and mitigation
- Audit trails and verification
- Industry-specific regulations
- Security and privacy compliance
- Contract compliance and terms

Create compliance content that:
- Addresses all regulatory requirements
- Demonstrates adherence to standards
- Provides clear compliance documentation
- Shows risk mitigation strategies
- Ensures audit trail and verification
- Maintains quality and accuracy
- Addresses industry-specific needs
- Provides compliance assurance"""

        user_content = f"""Generate compliance content for the {state.section_type.value} section:

Opportunity: {state.opportunity_id}
Client: {state.client_short_name}

Section Requirements:
{state.section_content or f'Standard {state.section_type.value} compliance requirements'}

Key Requirements:
{chr(10).join(f"- {req}" for req in key_requirements) if key_requirements else "- Address all compliance requirements comprehensively"}

Compliance Areas:
{chr(10).join(f"- {area}" for area in compliance_areas)}

Relevant Context:
{context_str}

Compliance Guidelines:
{chr(10).join(f"- {inst}" for inst in self._get_agent_specific_instructions())}

Please generate comprehensive compliance content that:
1. Addresses all regulatory and requirement compliance
2. Provides clear compliance documentation and evidence
3. Shows adherence to quality standards and certifications
4. Includes risk assessment and mitigation strategies
5. Demonstrates audit trails and verification processes
6. Addresses industry-specific compliance needs
7. Provides compliance assurance and guarantees

Structure the content with clear compliance sections and demonstrate thorough adherence to all requirements."""

        messages = self._create_messages(system_prompt, user_content)
        return await self._call_llm(messages)
    
    def _identify_compliance_areas(self, state: DraftState) -> List[str]:
        """Identify relevant compliance areas for the proposal"""
        base_areas = [
            "Requirement adherence and traceability",
            "Quality standards and certifications",
            "Documentation and reporting standards",
            "Risk management and mitigation"
        ]
        
        # Add section-specific compliance areas
        section_specific = {
            "technical_approach": [
                "Technical standards compliance",
                "Security and privacy requirements",
                "Performance and scalability standards"
            ],
            "management_plan": [
                "Project management standards",
                "Resource allocation compliance",
                "Timeline and milestone adherence"
            ],
            "compliance": [
                "Regulatory compliance matrix",
                "Industry-specific regulations",
                "Audit and verification processes"
            ],
            "pricing": [
                "Cost accounting standards",
                "Pricing transparency requirements",
                "Budget compliance and controls"
            ]
        }
        
        areas = base_areas.copy()
        areas.extend(section_specific.get(state.section_type.value, []))
        
        return areas
    
    def _get_agent_specific_instructions(self) -> List[str]:
        """Get compliance agent specific instructions"""
        return [
            "Ensure complete requirement traceability and adherence",
            "Verify compliance with all regulatory standards",
            "Provide clear compliance documentation and evidence",
            "Address all quality standards and certifications",
            "Include comprehensive risk assessment and mitigation",
            "Ensure audit trails and verification processes",
            "Address industry-specific compliance requirements",
            "Provide compliance matrices and cross-references",
            "Ensure documentation meets all standards",
            "Demonstrate continuous compliance monitoring"
        ]
    
    async def generate_compliance_matrix(self, state: DraftState) -> str:
        """
        Generate a compliance matrix showing requirement adherence.
        
        Args:
            state: Draft state with context
            
        Returns:
            Compliance matrix content
        """
        try:
            system_prompt = """You are a compliance expert specializing in requirement traceability.

Create a comprehensive compliance matrix that:
- Lists all requirements systematically
- Shows where each requirement is addressed
- Provides compliance status and evidence
- Includes risk assessment for each requirement
- Shows verification and validation approaches
- Demonstrates complete traceability"""

            context_str = self._format_context_for_llm(
                state.context_data.to_dict() if state.context_data else {}
            )

            user_content = f"""Create a compliance matrix for:

Opportunity: {state.opportunity_id}
Client: {state.client_short_name}

Requirements:
{state.section_content or 'Standard compliance requirements'}

Context:
{context_str}

Generate a comprehensive compliance matrix that demonstrates complete requirement adherence and traceability."""

            messages = self._create_messages(system_prompt, user_content)
            return await self._call_llm(messages)
            
        except Exception as e:
            logger.error(f"ComplianceAgent: Compliance matrix generation failed: {e}")
            raise e
    
    async def validate_content_compliance(self, content: str, requirements: List[str]) -> Dict[str, Any]:
        """
        Validate content against compliance requirements.
        
        Args:
            content: Content to validate
            requirements: List of requirements to check against
            
        Returns:
            Validation results with compliance status
        """
        try:
            system_prompt = """You are a compliance validation expert.

Review the provided content against the specified requirements and provide:
- Compliance status for each requirement
- Areas of non-compliance or gaps
- Recommendations for improvement
- Risk assessment for any gaps
- Overall compliance score"""

            user_content = f"""Validate this content for compliance:

Content to Review:
{content[:2000]}...  # Truncate for LLM limits

Requirements to Check:
{chr(10).join(f"- {req}" for req in requirements)}

Provide a detailed compliance validation report."""

            messages = self._create_messages(system_prompt, user_content)
            validation_result = await self._call_llm(messages)
            
            return {
                'validation_result': validation_result,
                'requirements_checked': len(requirements),
                'content_length': len(content),
                'validation_timestamp': 'current'
            }
            
        except Exception as e:
            logger.error(f"ComplianceAgent: Content validation failed: {e}")
            raise e
    
    async def generate_risk_assessment(self, state: DraftState) -> str:
        """
        Generate compliance risk assessment.
        
        Args:
            state: Draft state with context
            
        Returns:
            Risk assessment content
        """
        try:
            system_prompt = """You are a compliance risk assessment expert.

Create a comprehensive risk assessment that:
- Identifies compliance risks and vulnerabilities
- Assesses risk probability and impact
- Provides specific mitigation strategies
- Includes monitoring and control measures
- Shows contingency planning
- Demonstrates proactive risk management"""

            context_str = self._format_context_for_llm(
                state.context_data.to_dict() if state.context_data else {}
            )

            user_content = f"""Create a compliance risk assessment for:

Opportunity: {state.opportunity_id}
Client: {state.client_short_name}

Requirements:
{state.section_content or 'Standard compliance requirements'}

Context:
{context_str}

Generate a comprehensive compliance risk assessment that identifies risks and provides detailed mitigation strategies."""

            messages = self._create_messages(system_prompt, user_content)
            return await self._call_llm(messages)
            
        except Exception as e:
            logger.error(f"ComplianceAgent: Risk assessment generation failed: {e}")
            raise e
