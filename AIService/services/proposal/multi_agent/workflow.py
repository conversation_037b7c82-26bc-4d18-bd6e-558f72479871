"""
Multi-Agent Workflow Orchestrator

Coordinates the execution of the multi-agent proposal generation workflow,
managing the sequence of agent interactions and state transitions.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from .agent_state import DraftState, Agent<PERSON><PERSON>ult, AgentRole, SectionType
from .coordinator_agent import CoordinatorAgent
from .context_retrieval_agent import ContextR<PERSON>rievalAgent
from .cover_letter_specialist import Cover<PERSON>etterSpecialist
from .technical_specialist import TechnicalSpecialist
from .management_specialist import ManagementSpecialist
from .compliance_agent import ComplianceAgent
from .quality_assurance_agent import QualityAssuranceAgent

logger = logging.getLogger(__name__)


class MultiAgentWorkflow:
    """
    Orchestrates the multi-agent proposal generation workflow.
    
    Manages the execution sequence, state transitions, and coordination
    between specialized agents to generate high-quality proposal content.
    """
    
    def __init__(self):
        """Initialize the workflow with all agents"""
        self.coordinator = CoordinatorAgent()
        self.context_retrieval = ContextRetrievalAgent()
        self.cover_letter_specialist = CoverLetterSpecialist()
        self.technical_specialist = TechnicalSpecialist()
        self.management_specialist = ManagementSpecialist()
        self.compliance_agent = ComplianceAgent()
        self.quality_assurance = QualityAssuranceAgent()
        
        # Agent registry for dynamic access
        self.agents = {
            AgentRole.COORDINATOR: self.coordinator,
            AgentRole.CONTEXT_RETRIEVAL: self.context_retrieval,
            AgentRole.COVER_LETTER_SPECIALIST: self.cover_letter_specialist,
            AgentRole.TECHNICAL_SPECIALIST: self.technical_specialist,
            AgentRole.MANAGEMENT_SPECIALIST: self.management_specialist,
            AgentRole.COMPLIANCE_AGENT: self.compliance_agent,
            AgentRole.QUALITY_ASSURANCE: self.quality_assurance
        }
        
        logger.info("MultiAgentWorkflow: Initialized with all specialist agents")
    
    async def generate_section_content(self, 
                                     opportunity_id: str,
                                     tenant_id: str,
                                     section_type: str,
                                     section_content: str = "",
                                     client_short_name: str = "") -> Dict[str, Any]:
        """
        Generate content for a specific proposal section using the multi-agent workflow.
        
        Args:
            opportunity_id: Unique identifier for the opportunity
            tenant_id: Tenant identifier
            section_type: Type of section to generate
            section_content: Section requirements and content
            client_short_name: Client short name
            
        Returns:
            Dictionary containing generated content and workflow results
        """
        # Initialize draft state
        state = DraftState(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            section_type=section_type,
            section_content=section_content,
            client_short_name=client_short_name
        )
        
        logger.info(f"MultiAgentWorkflow: Starting content generation for {section_type} (opportunity: {opportunity_id})")
        
        try:
            # Execute the workflow
            final_state = await self._execute_workflow(state)
            
            # Prepare results
            results = {
                'success': final_state.is_complete(),
                'content': final_state.final_content,
                'quality_score': final_state.quality_score,
                'workflow_summary': final_state.get_progress_summary(),
                'agent_results': {
                    role.value: result.to_dict() 
                    for role, result in final_state.agent_results.items()
                },
                'processing_metadata': final_state.processing_metadata
            }
            
            if final_state.is_complete():
                logger.info(f"MultiAgentWorkflow: Successfully completed {section_type} generation")
            else:
                logger.warning(f"MultiAgentWorkflow: Workflow incomplete for {section_type}")
            
            return results
            
        except Exception as e:
            logger.error(f"MultiAgentWorkflow: Workflow failed for {section_type}: {e}")
            return {
                'success': False,
                'error': str(e),
                'content': None,
                'workflow_summary': state.get_progress_summary()
            }
    
    async def _execute_workflow(self, state: DraftState) -> DraftState:
        """
        Execute the complete multi-agent workflow.
        
        Args:
            state: Initial draft state
            
        Returns:
            Final draft state with results
        """
        workflow_start = datetime.now()
        
        try:
            # Phase 1: Coordination and Planning
            logger.info(f"MultiAgentWorkflow: Phase 1 - Coordination and Planning")
            await self._execute_coordination_phase(state)
            
            # Phase 2: Context Retrieval
            logger.info(f"MultiAgentWorkflow: Phase 2 - Context Retrieval")
            await self._execute_context_phase(state)
            
            # Phase 3: Content Generation
            logger.info(f"MultiAgentWorkflow: Phase 3 - Content Generation")
            await self._execute_content_generation_phase(state)
            
            # Phase 4: Compliance and Quality Assurance
            logger.info(f"MultiAgentWorkflow: Phase 4 - Compliance and QA")
            await self._execute_quality_assurance_phase(state)
            
            # Record workflow completion
            workflow_duration = (datetime.now() - workflow_start).total_seconds()
            state.processing_metadata['workflow_duration'] = workflow_duration
            state.processing_metadata['workflow_completed'] = True
            
            logger.info(f"MultiAgentWorkflow: Completed in {workflow_duration:.2f} seconds")
            
            return state
            
        except Exception as e:
            workflow_duration = (datetime.now() - workflow_start).total_seconds()
            state.processing_metadata['workflow_duration'] = workflow_duration
            state.processing_metadata['workflow_failed'] = True
            state.processing_metadata['failure_reason'] = str(e)
            
            logger.error(f"MultiAgentWorkflow: Failed after {workflow_duration:.2f} seconds: {e}")
            raise e
    
    async def _execute_coordination_phase(self, state: DraftState) -> None:
        """Execute coordination and planning phase"""
        try:
            result = await self.coordinator._timed_process(state)
            state.add_agent_result(result)
            
            if not result.success:
                raise Exception(f"Coordination failed: {result.error_message}")
                
        except Exception as e:
            logger.error(f"MultiAgentWorkflow: Coordination phase failed: {e}")
            raise e
    
    async def _execute_context_phase(self, state: DraftState) -> None:
        """Execute context retrieval phase"""
        try:
            result = await self.context_retrieval._timed_process(state)
            state.add_agent_result(result)
            
            if not result.success:
                logger.warning(f"Context retrieval failed: {result.error_message}")
                # Continue workflow even if context retrieval fails
                
        except Exception as e:
            logger.warning(f"MultiAgentWorkflow: Context phase failed: {e}")
            # Continue workflow - context failure shouldn't stop content generation
    
    async def _execute_content_generation_phase(self, state: DraftState) -> None:
        """Execute content generation phase"""
        try:
            # Determine primary specialist from workflow plan
            if state.workflow_plan:
                primary_specialist_role = state.workflow_plan.primary_specialist
            else:
                # Fallback to section-based selection
                primary_specialist_role = self._get_default_specialist(state.section_type)
            
            # Execute primary specialist
            primary_agent = self.agents.get(primary_specialist_role)
            if not primary_agent:
                raise Exception(f"Primary specialist {primary_specialist_role} not available")
            
            result = await primary_agent._timed_process(state)
            state.add_agent_result(result)
            
            if not result.success:
                raise Exception(f"Primary specialist failed: {result.error_message}")
            
            # Clean and fix styling issues in the content
            cleaned_content = self._clean_content_styling(result.content)

            # Set the cleaned content as final content
            state.set_final_content(cleaned_content)
            
        except Exception as e:
            logger.error(f"MultiAgentWorkflow: Content generation phase failed: {e}")
            raise e
    
    async def _execute_quality_assurance_phase(self, state: DraftState) -> None:
        """Execute compliance and quality assurance phase"""
        try:
            # Run compliance and QA in parallel
            tasks = []
            
            # Add compliance check if needed
            if state.section_type != SectionType.COMPLIANCE:
                tasks.append(self.compliance_agent._timed_process(state))
            
            # Always run quality assurance
            tasks.append(self.quality_assurance._timed_process(state))
            
            # Execute tasks
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process results
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        logger.warning(f"QA phase task {i} failed: {result}")
                    else:
                        state.add_agent_result(result)
                        
                        # Update quality score if available
                        if (result.agent_role == AgentRole.QUALITY_ASSURANCE and 
                            result.success and 
                            result.metadata.get('quality_score')):
                            state.set_final_content(
                                state.final_content, 
                                result.metadata['quality_score']
                            )
            
        except Exception as e:
            logger.warning(f"MultiAgentWorkflow: QA phase failed: {e}")
            # Don't fail the entire workflow for QA issues
    
    def _get_default_specialist(self, section_type: SectionType) -> AgentRole:
        """Get default specialist for section type"""
        mapping = {
            SectionType.COVER_LETTER: AgentRole.COVER_LETTER_SPECIALIST,
            SectionType.EXECUTIVE_SUMMARY: AgentRole.COVER_LETTER_SPECIALIST,
            SectionType.TECHNICAL_APPROACH: AgentRole.TECHNICAL_SPECIALIST,
            SectionType.MANAGEMENT_PLAN: AgentRole.MANAGEMENT_SPECIALIST,
            SectionType.PAST_PERFORMANCE: AgentRole.MANAGEMENT_SPECIALIST,
            SectionType.PRICING: AgentRole.TECHNICAL_SPECIALIST,
            SectionType.COMPLIANCE: AgentRole.COMPLIANCE_AGENT,
            SectionType.APPENDIX: AgentRole.TECHNICAL_SPECIALIST,
            SectionType.CUSTOM: AgentRole.TECHNICAL_SPECIALIST
        }
        
        return mapping.get(section_type, AgentRole.TECHNICAL_SPECIALIST)
    
    def get_workflow_status(self) -> Dict[str, Any]:
        """Get status of all agents in the workflow"""
        status = {
            'workflow_initialized': True,
            'agents_available': len(self.agents),
            'agent_health': {}
        }
        
        for role, agent in self.agents.items():
            try:
                status['agent_health'][role.value] = agent.get_health_status()
            except Exception as e:
                status['agent_health'][role.value] = {
                    'status': 'error',
                    'error': str(e)
                }
        
        return status
    
    async def validate_workflow_readiness(self) -> Dict[str, Any]:
        """Validate that the workflow is ready for execution"""
        validation_results = {
            'ready': True,
            'issues': [],
            'agent_status': {}
        }
        
        # Check each agent
        for role, agent in self.agents.items():
            try:
                health = agent.get_health_status()
                validation_results['agent_status'][role.value] = health
                
                # Check for any critical issues
                if health.get('status') == 'error':
                    validation_results['ready'] = False
                    validation_results['issues'].append(f"Agent {role.value} has health issues")
                    
            except Exception as e:
                validation_results['ready'] = False
                validation_results['issues'].append(f"Agent {role.value} validation failed: {e}")
                validation_results['agent_status'][role.value] = {'status': 'error', 'error': str(e)}
        
        return validation_results

    def _clean_content_styling(self, content: str) -> str:
        """
        Clean content to fix styling issues that don't render correctly in PDFs.

        Addresses issues like:
        - Asterisk markdown not rendering: *Compliance Readiness Review (CRR)*
        - Other markdown formatting issues

        Args:
            content: Raw content from LLM

        Returns:
            Cleaned content with proper formatting
        """
        if not content:
            return content

        import re

        # Fix asterisk markdown that doesn't render properly in PDFs
        # Convert *text* to **text** for proper bold formatting
        content = re.sub(r'\*([^*]+)\*', r'**\1**', content)

        # Fix other common markdown issues
        # Ensure proper spacing around headers
        content = re.sub(r'^(#{1,6})\s*(.+)$', r'\1 \2', content, flags=re.MULTILINE)

        # Clean up extra whitespace
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)  # Multiple newlines to double
        content = re.sub(r'[ \t]+$', '', content, flags=re.MULTILINE)  # Trailing spaces

        # Fix bullet point formatting
        content = re.sub(r'^[\s]*[-*+]\s+', '• ', content, flags=re.MULTILINE)

        # Ensure proper paragraph spacing
        content = content.strip()

        return content

    def _enhance_letter_content(self, content: str, section_title: str) -> str:
        """
        Enhance letter content to ensure it's appropriate for the specific letter type.

        This prevents the issue where LLM generates cover letter content for
        other types of letters (Tentative/Contingent Offer Letter, etc.)

        Args:
            content: Generated content
            section_title: Title of the section

        Returns:
            Enhanced content appropriate for the letter type
        """
        if not content:
            return content

        title_lower = section_title.lower()

        # Add specific instructions for different letter types
        if 'tentative' in title_lower or 'contingent' in title_lower:
            # Ensure content is appropriate for tentative/contingent offers
            if 'cover letter' in content.lower() or 'proposal' in content.lower():
                # Content seems to be generic cover letter - add specific context
                content = f"# {section_title}\n\n{content}"
                content += "\n\n*Note: This letter is contingent upon final contract negotiations and approval.*"

        elif 'offer letter' in title_lower:
            # Ensure content is appropriate for offer letters
            content = f"# {section_title}\n\n{content}"

        return content
