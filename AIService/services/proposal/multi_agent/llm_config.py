"""
LLM Configuration Management

Centralized configuration for LLM endpoints and settings used across
the multi-agent proposal generation system.
"""

import os
from typing import List, Dict, Any
from dataclasses import dataclass
from config import settings


@dataclass
class LLMEndpoint:
    """Configuration for a single LLM endpoint"""
    url: str
    model: str
    provider: str = "ollama"  # "ollama" or "gemini"
    timeout: int = 30
    max_retries: int = 3
    weight: float = 1.0  # For load balancing

    def __post_init__(self):
        """Validate endpoint configuration"""
        if not self.model:
            raise ValueError("LLM model cannot be empty")
        if self.timeout <= 0:
            raise ValueError("Timeout must be positive")
        if self.max_retries < 0:
            raise ValueError("Max retries cannot be negative")
        if self.provider not in ["ollama", "gemini"]:
            raise ValueError("Provider must be 'ollama' or 'gemini'")
        if self.provider == "ollama" and not self.url:
            raise ValueError("LLM endpoint URL cannot be empty for Ollama provider")


class LLMConfig:
    """Central configuration for LLM settings"""

    @staticmethod
    def get_default_endpoints() -> List[LLMEndpoint]:
        """Get default LLM endpoints from environment or hardcoded defaults"""

        # Use settings from config
        provider = settings.llm_provider
        model = settings.llm_model

        if provider.lower() == "gemini":
            return [
                LLMEndpoint(
                    url="",  # Not needed for Gemini
                    model=model,
                    provider="gemini",
                    timeout=60,
                    max_retries=5,
                    weight=1.0
                )
            ]
        else:  # ollama
            llm_url = os.getenv('LLM_URL', 'http://ai.kontratar.com:11434')
            return [
                LLMEndpoint(
                    url=llm_url,
                    model=model,
                    provider="ollama",
                    timeout=60,
                    max_retries=5,
                    weight=1.0
                )
            ]
    
    @staticmethod
    def get_retry_config() -> Dict[str, Any]:
        """Get retry configuration for LLM calls"""
        return {
            'max_attempts': 5,
            'initial_delay': 1.0,
            'max_delay': 30.0,
            'exponential_base': 2.0,
            'jitter': True
        }
    
    @staticmethod
    def get_health_check_config() -> Dict[str, Any]:
        """Get health check configuration"""
        return {
            'check_interval': 30,  # seconds
            'timeout': 10,  # seconds
            'failure_threshold': 3,  # consecutive failures before marking unhealthy
            'recovery_threshold': 2  # consecutive successes before marking healthy
        }
