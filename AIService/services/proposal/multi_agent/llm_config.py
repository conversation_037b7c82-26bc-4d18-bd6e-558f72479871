"""
LLM Configuration Management

Centralized configuration for LLM endpoints and settings used across
the multi-agent proposal generation system.
"""

import os
from typing import List, Dict, Any
from dataclasses import dataclass


@dataclass
class LLMEndpoint:
    """Configuration for a single LLM endpoint"""
    url: str
    model: str
    timeout: int = 30
    max_retries: int = 3
    weight: float = 1.0  # For load balancing
    
    def __post_init__(self):
        """Validate endpoint configuration"""
        if not self.url:
            raise ValueError("LLM endpoint URL cannot be empty")
        if not self.model:
            raise ValueError("LLM model cannot be empty")
        if self.timeout <= 0:
            raise ValueError("Timeout must be positive")
        if self.max_retries < 0:
            raise ValueError("Max retries cannot be negative")


class LLMConfig:
    """Central configuration for LLM settings"""
    
    @staticmethod
    def get_default_endpoints() -> List[LLMEndpoint]:
        """Get default LLM endpoints from environment or hardcoded defaults"""
        
        # Try to get from environment first
        llm_url = os.getenv('LLM_URL', 'http://ai.kontratar.com:11434')
        llm_model = os.getenv('LLM_MODEL', 'gemma3:27b')  # Fixed: Use correct model
        
        return [
            LLMEndpoint(
                url=llm_url,
                model=llm_model,
                timeout=60,
                max_retries=5,
                weight=1.0
            )
        ]
    
    @staticmethod
    def get_retry_config() -> Dict[str, Any]:
        """Get retry configuration for LLM calls"""
        return {
            'max_attempts': 5,
            'initial_delay': 1.0,
            'max_delay': 30.0,
            'exponential_base': 2.0,
            'jitter': True
        }
    
    @staticmethod
    def get_health_check_config() -> Dict[str, Any]:
        """Get health check configuration"""
        return {
            'check_interval': 30,  # seconds
            'timeout': 10,  # seconds
            'failure_threshold': 3,  # consecutive failures before marking unhealthy
            'recovery_threshold': 2  # consecutive successes before marking healthy
        }
