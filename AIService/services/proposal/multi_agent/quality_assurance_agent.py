"""
Quality Assurance Agent

Specialized agent for reviewing, validating, and ensuring the quality
of generated proposal content across all sections.
"""

import logging
from typing import Dict, Any, List, Optional
from langchain_core.messages import BaseMessage

from .base_agent import BaseAgent
from .agent_state import DraftState, AgentResult, AgentRole

logger = logging.getLogger(__name__)


class QualityAssuranceAgent(BaseAgent):
    """
    Ensures quality and consistency of proposal content.
    
    Responsibilities:
    - Review content quality and accuracy
    - Validate consistency across sections
    - Check grammar, style, and formatting
    - Ensure professional standards
    - Provide quality scoring and feedback
    """
    
    def __init__(self):
        """Initialize the quality assurance agent"""
        super().__init__(AgentRole.QUALITY_ASSURANCE)
    
    async def process(self, state: DraftState) -> AgentResult:
        """
        Review and validate content quality.
        
        Args:
            state: Current draft state with content to review
            
        Returns:
            AgentResult with quality assessment and recommendations
        """
        if not self._validate_state(state):
            return self._create_failure_result("Invalid draft state provided")
        
        try:
            # Get content to review (could be from final_content or agent results)
            content_to_review = self._get_content_for_review(state)
            
            if not content_to_review:
                return self._create_failure_result("No content available for quality review")
            
            # Perform quality assessment
            quality_assessment = await self._assess_content_quality(content_to_review, state)
            
            # Generate quality improvement recommendations
            recommendations = await self._generate_quality_recommendations(content_to_review, state)
            
            # Calculate overall quality score
            quality_score = self._calculate_quality_score(quality_assessment)
            
            # Prepare result metadata
            metadata = {
                'quality_score': quality_score,
                'assessment_areas': list(quality_assessment.keys()),
                'recommendations_count': len(recommendations),
                'content_length': len(content_to_review),
                'section_type': state.section_type.value
            }
            
            # Create quality report
            quality_report = self._create_quality_report(quality_assessment, recommendations, quality_score)
            
            return self._create_success_result(content=quality_report, metadata=metadata)
            
        except Exception as e:
            logger.error(f"QualityAssuranceAgent: Quality assessment failed: {e}")
            # NO FALLBACK - fail cleanly to ensure high quality
            raise e
    
    def _get_content_for_review(self, state: DraftState) -> Optional[str]:
        """Get content that needs quality review"""
        # Check if there's final content
        if state.final_content:
            return state.final_content
        
        # Otherwise, get content from the most recent successful agent result
        for agent_role, result in state.agent_results.items():
            if result.success and result.content:
                return result.content
        
        return None
    
    async def _assess_content_quality(self, content: str, state: DraftState) -> Dict[str, Dict[str, Any]]:
        """
        Assess content quality across multiple dimensions.
        
        Args:
            content: Content to assess
            state: Draft state for context
            
        Returns:
            Quality assessment results
        """
        assessment_areas = [
            "accuracy_and_relevance",
            "clarity_and_readability", 
            "professional_tone",
            "completeness",
            "consistency",
            "technical_quality"
        ]
        
        assessment_results = {}
        
        for area in assessment_areas:
            try:
                assessment_results[area] = await self._assess_quality_area(content, area, state)
            except Exception as e:
                logger.warning(f"QualityAssuranceAgent: Failed to assess {area}: {e}")
                assessment_results[area] = {
                    'score': 7.0,  # Default moderate score
                    'feedback': f"Assessment failed for {area}",
                    'issues': []
                }
        
        return assessment_results
    
    async def _assess_quality_area(self, content: str, area: str, state: DraftState) -> Dict[str, Any]:
        """Assess a specific quality area"""
        
        area_prompts = {
            "accuracy_and_relevance": "Assess the accuracy and relevance of the content to the opportunity requirements",
            "clarity_and_readability": "Evaluate the clarity, readability, and logical flow of the content",
            "professional_tone": "Review the professional tone, language, and appropriateness for the audience",
            "completeness": "Check if the content comprehensively addresses all requirements",
            "consistency": "Verify consistency in terminology, style, and messaging",
            "technical_quality": "Assess technical accuracy, feasibility, and depth of solutions"
        }
        
        system_prompt = f"""You are a senior proposal quality assurance expert specializing in {area}.

{area_prompts.get(area, f"Assess the {area} of the content")}

Provide your assessment as:
1. Score (1-10 scale, where 10 is excellent)
2. Specific feedback on strengths and weaknesses
3. List of specific issues or areas for improvement
4. Recommendations for enhancement

Be thorough but constructive in your assessment."""

        user_content = f"""Assess the {area} of this proposal content:

Section Type: {state.section_type.value}
Opportunity: {state.opportunity_id}
Client: {state.client_short_name}

Content to Review:
{content[:1500]}...  # Truncate for LLM limits

Provide a detailed assessment focusing specifically on {area}."""

        try:
            messages = self._create_messages(system_prompt, user_content)
            assessment_text = await self._call_llm(messages)
            
            # Parse the assessment (simplified parsing)
            score = self._extract_score_from_assessment(assessment_text)
            
            return {
                'score': score,
                'feedback': assessment_text,
                'issues': self._extract_issues_from_assessment(assessment_text)
            }
            
        except Exception as e:
            logger.error(f"QualityAssuranceAgent: Assessment failed for {area}: {e}")
            raise e
    
    def _extract_score_from_assessment(self, assessment_text: str) -> float:
        """Extract numerical score from assessment text"""
        import re
        
        # Look for score patterns like "Score: 8", "8/10", "8.5"
        score_patterns = [
            r'score[:\s]+(\d+\.?\d*)',
            r'(\d+\.?\d*)[/\s]*10',
            r'(\d+\.?\d*)\s*out\s*of\s*10'
        ]
        
        for pattern in score_patterns:
            match = re.search(pattern, assessment_text.lower())
            if match:
                try:
                    score = float(match.group(1))
                    return min(max(score, 1.0), 10.0)  # Clamp between 1-10
                except ValueError:
                    continue
        
        # Default score if no pattern found
        return 7.0
    
    def _extract_issues_from_assessment(self, assessment_text: str) -> List[str]:
        """Extract specific issues from assessment text"""
        # Simple extraction - look for bullet points or numbered lists
        lines = assessment_text.split('\n')
        issues = []
        
        for line in lines:
            line = line.strip()
            if line and (line.startswith('-') or line.startswith('•') or 
                        line.startswith(tuple(f"{i}." for i in range(1, 10)))):
                issues.append(line)
        
        return issues[:10]  # Limit to 10 issues
    
    async def _generate_quality_recommendations(self, content: str, state: DraftState) -> List[str]:
        """Generate specific quality improvement recommendations"""
        
        system_prompt = """You are a proposal improvement expert.

Based on the content review, provide specific, actionable recommendations for improvement.

Focus on:
- Concrete steps to enhance quality
- Specific areas that need attention
- Suggestions for better alignment with requirements
- Ways to improve clarity and impact
- Professional presentation improvements

Provide 5-7 specific, actionable recommendations."""

        user_content = f"""Provide quality improvement recommendations for this content:

Section Type: {state.section_type.value}
Opportunity: {state.opportunity_id}

Content:
{content[:1500]}...

Generate specific recommendations to improve the quality and effectiveness of this content."""

        try:
            messages = self._create_messages(system_prompt, user_content)
            recommendations_text = await self._call_llm(messages)
            
            # Parse recommendations into list
            recommendations = []
            lines = recommendations_text.split('\n')
            
            for line in lines:
                line = line.strip()
                if line and (line.startswith('-') or line.startswith('•') or 
                           line.startswith(tuple(f"{i}." for i in range(1, 10)))):
                    recommendations.append(line)
            
            return recommendations[:7]  # Limit to 7 recommendations
            
        except Exception as e:
            logger.error(f"QualityAssuranceAgent: Recommendations generation failed: {e}")
            return ["Review content for overall quality and alignment with requirements"]
    
    def _calculate_quality_score(self, assessment: Dict[str, Dict[str, Any]]) -> float:
        """Calculate overall quality score from individual assessments"""
        if not assessment:
            return 7.0
        
        scores = [area_data.get('score', 7.0) for area_data in assessment.values()]
        return round(sum(scores) / len(scores), 1)
    
    def _create_quality_report(self, 
                             assessment: Dict[str, Dict[str, Any]], 
                             recommendations: List[str], 
                             quality_score: float) -> str:
        """Create a comprehensive quality report"""
        
        report_sections = [
            f"QUALITY ASSESSMENT REPORT",
            f"Overall Quality Score: {quality_score}/10",
            "",
            "ASSESSMENT BY AREA:"
        ]
        
        for area, data in assessment.items():
            area_name = area.replace('_', ' ').title()
            score = data.get('score', 0)
            report_sections.extend([
                f"\n{area_name}: {score}/10",
                f"Feedback: {data.get('feedback', 'No feedback available')[:200]}..."
            ])
        
        if recommendations:
            report_sections.extend([
                "\nRECOMMENDATIONS FOR IMPROVEMENT:",
                *[f"- {rec}" for rec in recommendations]
            ])
        
        return '\n'.join(report_sections)
    
    def _get_agent_specific_instructions(self) -> List[str]:
        """Get quality assurance specific instructions"""
        return [
            "Conduct thorough quality assessment across all dimensions",
            "Provide specific, actionable feedback and recommendations",
            "Ensure content meets professional proposal standards",
            "Verify accuracy, clarity, and completeness",
            "Check consistency in tone, style, and messaging",
            "Assess technical accuracy and feasibility",
            "Evaluate alignment with opportunity requirements",
            "Provide constructive improvement suggestions",
            "Maintain high quality standards throughout",
            "Document quality metrics and scores"
        ]
    
    async def review_final_proposal(self, state: DraftState) -> Dict[str, Any]:
        """
        Conduct final comprehensive quality review of complete proposal.
        
        Args:
            state: Complete draft state
            
        Returns:
            Comprehensive quality review results
        """
        try:
            if not state.final_content:
                return {
                    'status': 'error',
                    'message': 'No final content available for review'
                }
            
            # Conduct comprehensive assessment
            quality_assessment = await self._assess_content_quality(state.final_content, state)
            recommendations = await self._generate_quality_recommendations(state.final_content, state)
            quality_score = self._calculate_quality_score(quality_assessment)
            
            # Update state with quality score
            state.set_final_content(state.final_content, quality_score)
            
            return {
                'status': 'complete',
                'quality_score': quality_score,
                'assessment': quality_assessment,
                'recommendations': recommendations,
                'content_length': len(state.final_content),
                'review_timestamp': 'current'
            }
            
        except Exception as e:
            logger.error(f"QualityAssuranceAgent: Final review failed: {e}")
            raise e
