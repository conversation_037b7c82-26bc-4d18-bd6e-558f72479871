"""
Management Specialist Agent

Specialized agent for generating project management, organizational,
and operational content for proposals.
"""

import logging
from typing import Dict, Any, List
from langchain_core.messages import BaseMessage

from .base_agent import BaseAgent
from .agent_state import DraftState, AgentResult, AgentRole

logger = logging.getLogger(__name__)


class ManagementSpecialist(BaseAgent):
    """
    Generates management and organizational content for proposals.
    
    Responsibilities:
    - Create project management plans
    - Develop organizational structures
    - Design resource allocation strategies
    - Generate past performance narratives
    - Create operational procedures
    """
    
    def __init__(self):
        """Initialize the management specialist"""
        super().__init__(AgentRole.MANAGEMENT_SPECIALIST)
    
    async def process(self, state: DraftState) -> AgentResult:
        """
        Generate management-focused content.
        
        Args:
            state: Current draft state with context and requirements
            
        Returns:
            AgentResult with generated management content
        """
        if not self._validate_state(state):
            return self._create_failure_result("Invalid draft state provided")
        
        try:
            # Generate management content based on section type
            content = await self._generate_management_content(state)
            
            # Prepare result metadata
            metadata = {
                'section_type': state.section_type.value,
                'content_length': len(content),
                'opportunity_id': state.opportunity_id,
                'generation_approach': 'management_focused',
                'complexity_level': state.complexity_level.value if hasattr(state, 'complexity_level') else 'medium'
            }
            
            return self._create_success_result(content=content, metadata=metadata)
            
        except Exception as e:
            logger.error(f"ManagementSpecialist: Content generation failed: {e}")
            # NO FALLBACK - fail cleanly to ensure high quality
            raise e
    
    async def _generate_management_content(self, state: DraftState) -> str:
        """Generate comprehensive management content"""
        
        # Prepare context for LLM
        context_str = self._format_context_for_llm(
            state.context_data.to_dict() if state.context_data else {}
        )
        
        # Get key requirements
        key_requirements = self._extract_key_requirements(state.section_content)
        
        # Determine management focus based on section type
        management_focus = self._get_management_focus(state.section_type.value)
        
        system_prompt = f"""You are a senior project management and organizational expert specializing in {management_focus}.

Your management expertise includes:
- Project management methodologies (Agile, Waterfall, Hybrid)
- Organizational design and team structure
- Resource planning and allocation
- Risk management and quality assurance
- Stakeholder management and communication
- Performance measurement and reporting
- Change management and process improvement
- Budget management and cost control

Create management content that:
- Demonstrates proven project management capabilities
- Shows clear organizational structure and accountability
- Provides detailed resource and timeline planning
- Addresses risk management and quality control
- Shows effective communication and reporting
- Demonstrates successful past performance
- Ensures stakeholder alignment and satisfaction"""

        user_content = f"""Generate management content for the {state.section_type.value} section:

Opportunity: {state.opportunity_id}
Client: {state.client_short_name}
Management Focus: {management_focus}

Section Requirements:
{state.section_content or f'Standard {state.section_type.value} management requirements'}

Key Requirements:
{chr(10).join(f"- {req}" for req in key_requirements) if key_requirements else "- Address all management requirements comprehensively"}

Relevant Context:
{context_str}

Management Guidelines:
{chr(10).join(f"- {inst}" for inst in self._get_agent_specific_instructions())}

Please generate comprehensive management content that:
1. Provides clear project management approach and methodology
2. Details organizational structure and team composition
3. Shows resource allocation and timeline planning
4. Addresses risk management and quality assurance
5. Demonstrates communication and reporting strategies
6. Includes performance metrics and success measures
7. Shows proven management capabilities and past success

Structure the content with clear management sections and demonstrate leadership excellence."""

        messages = self._create_messages(system_prompt, user_content)
        return await self._call_llm(messages)
    
    def _get_management_focus(self, section_type: str) -> str:
        """Determine management focus based on section type"""
        focus_mapping = {
            "management_plan": "comprehensive project management and organizational leadership",
            "past_performance": "successful project delivery and management achievements",
            "technical_approach": "technical project management and delivery coordination",
            "compliance": "compliance management and quality assurance",
            "pricing": "budget management and resource optimization",
            "executive_summary": "strategic management and organizational capabilities"
        }
        
        return focus_mapping.get(section_type, "project management and organizational excellence")
    
    def _get_agent_specific_instructions(self) -> List[str]:
        """Get management specialist specific instructions"""
        return [
            "Demonstrate proven project management methodologies",
            "Show clear organizational structure and accountability",
            "Provide detailed resource planning and allocation",
            "Include comprehensive risk management strategies",
            "Show effective stakeholder communication approaches",
            "Demonstrate quality assurance and control measures",
            "Include performance metrics and success indicators",
            "Show past performance and management achievements",
            "Address change management and process improvement",
            "Ensure budget control and cost management"
        ]
    
    async def generate_project_management_plan(self, state: DraftState) -> str:
        """
        Generate a comprehensive project management plan.
        
        Args:
            state: Draft state with context
            
        Returns:
            Project management plan description
        """
        try:
            system_prompt = """You are a certified project management professional (PMP) with extensive experience.

Create a comprehensive project management plan that:
- Outlines project phases and milestones
- Details project management methodology
- Shows resource allocation and scheduling
- Includes risk management strategies
- Demonstrates quality assurance approaches
- Shows communication and reporting plans
- Addresses stakeholder management"""

            context_str = self._format_context_for_llm(
                state.context_data.to_dict() if state.context_data else {}
            )

            user_content = f"""Create a project management plan for:

Opportunity: {state.opportunity_id}
Client: {state.client_short_name}

Requirements:
{state.section_content or 'Standard project management requirements'}

Context:
{context_str}

Generate a comprehensive project management plan that demonstrates proven project delivery capabilities."""

            messages = self._create_messages(system_prompt, user_content)
            return await self._call_llm(messages)
            
        except Exception as e:
            logger.error(f"ManagementSpecialist: Project management plan generation failed: {e}")
            raise e
    
    async def generate_organizational_structure(self, state: DraftState) -> str:
        """
        Generate organizational structure and team composition.
        
        Args:
            state: Draft state with context
            
        Returns:
            Organizational structure description
        """
        try:
            system_prompt = """You are an organizational design expert specializing in project teams.

Create a clear organizational structure that:
- Shows team hierarchy and reporting relationships
- Details roles and responsibilities
- Demonstrates appropriate skill mix
- Shows clear accountability and decision-making
- Includes communication channels
- Addresses team coordination and collaboration"""

            context_str = self._format_context_for_llm(
                state.context_data.to_dict() if state.context_data else {}
            )

            user_content = f"""Create an organizational structure for:

Opportunity: {state.opportunity_id}
Client: {state.client_short_name}

Requirements:
{state.section_content or 'Standard organizational requirements'}

Context:
{context_str}

Generate a comprehensive organizational structure that shows effective team design and management."""

            messages = self._create_messages(system_prompt, user_content)
            return await self._call_llm(messages)
            
        except Exception as e:
            logger.error(f"ManagementSpecialist: Organizational structure generation failed: {e}")
            raise e
    
    async def generate_past_performance_narrative(self, state: DraftState) -> str:
        """
        Generate past performance narrative and case studies.
        
        Args:
            state: Draft state with context
            
        Returns:
            Past performance narrative
        """
        try:
            system_prompt = """You are an expert at crafting compelling past performance narratives.

Create a past performance narrative that:
- Highlights relevant successful projects
- Shows measurable outcomes and achievements
- Demonstrates management excellence
- Includes client satisfaction and references
- Shows problem-solving and innovation
- Demonstrates continuous improvement"""

            context_str = self._format_context_for_llm(
                state.context_data.to_dict() if state.context_data else {}
            )

            user_content = f"""Create a past performance narrative for:

Opportunity: {state.opportunity_id}
Client: {state.client_short_name}

Requirements:
{state.section_content or 'Standard past performance requirements'}

Context:
{context_str}

Generate a compelling past performance narrative that demonstrates proven management success and client satisfaction."""

            messages = self._create_messages(system_prompt, user_content)
            return await self._call_llm(messages)
            
        except Exception as e:
            logger.error(f"ManagementSpecialist: Past performance narrative generation failed: {e}")
            raise e
