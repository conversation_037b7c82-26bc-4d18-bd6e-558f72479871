import json
import logging
from typing import Dict, List, Any, Optional
import asyncio

from langchain_ollama import ChatOllama
from services.proposal.validation.auto_fixing_validator import AutoFixingValidator

logger = logging.getLogger(__name__)

class EnhancedDraftGenerator:
    """
    Enhanced proposal draft generator with integrated auto-fixing validation
    """
    
    def __init__(self, llm_api_url: str = "http://ai.kontratar.com:11434"):
        self.llm = ChatOllama(
            model="gemma3:27b", 
            num_ctx=6300, 
            temperature=0, 
            base_url=llm_api_url
        )
        self.validator = AutoFixingValidator(llm_api_url)
        logger.info(f"ENHANCED GENERATOR: Initialized with auto-fixing validation")
    
    async def generate_validated_section(
        self,
        section_title: str,
        section_description: str,
        context: str,
        company_context: str = "",
        tenant_metadata: str = "",
        max_validation_retries: int = 2
    ) -> Dict[str, Any]:
        """
        Generate a proposal section with automatic validation and fixing
        
        Returns:
            {
                "content": "final_content",
                "validation_passed": bool,
                "issues_found": [],
                "fixes_applied": int,
                "generation_attempts": int
            }
        """
        logger.info(f"ENHANCED: Generating validated section '{section_title}'")
        
        try:
            # Generate initial content
            initial_content = await self._generate_initial_content(
                section_title, section_description, context, company_context, tenant_metadata
            )
            
            if not initial_content:
                logger.error(f"ENHANCED: Failed to generate initial content for '{section_title}'")
                return {
                    "content": "",
                    "validation_passed": False,
                    "issues_found": ["Failed to generate content"],
                    "fixes_applied": 0,
                    "generation_attempts": 1
                }
            
            logger.info(f"ENHANCED: Initial content generated ({len(initial_content)} chars)")
            
            # Validate and fix content
            final_content, remaining_issues = await self.validator.validate_and_fix_content(
                initial_content, 
                section_title, 
                section_description,
                max_validation_retries
            )
            
            validation_passed = len(remaining_issues) == 0
            fixes_applied = max_validation_retries - len(remaining_issues) if remaining_issues else max_validation_retries
            
            result = {
                "content": final_content,
                "validation_passed": validation_passed,
                "issues_found": [issue.message for issue in remaining_issues],
                "fixes_applied": fixes_applied,
                "generation_attempts": max_validation_retries + 1
            }
            
            if validation_passed:
                logger.info(f"ENHANCED: Section '{section_title}' generated with perfect quality!")
            else:
                logger.warning(f"ENHANCED: Section '{section_title}' has {len(remaining_issues)} remaining issues")
                for issue in remaining_issues:
                    logger.warning(f"   - {issue.issue_type}: {issue.message}")
            
            return result
            
        except Exception as e:
            logger.error(f"ENHANCED: Error generating section '{section_title}' - {str(e)}")
            return {
                "content": "",
                "validation_passed": False,
                "issues_found": [f"Generation error: {str(e)}"],
                "fixes_applied": 0,
                "generation_attempts": 1
            }
    
    async def _generate_initial_content(
        self,
        section_title: str,
        section_description: str,
        context: str,
        company_context: str,
        tenant_metadata: str
    ) -> str:
        """Generate initial high-quality content"""
        
        try:
            system_prompt = f"""You are an expert GOVERNMENT PROPOSAL writer creating 100% compliant content.

CRITICAL GOVERNMENT PROPOSAL REQUIREMENTS:
1. ZERO placeholders, brackets, TBD, TODO, or incomplete information
2. NO contact information in technical sections (only in designated contact sections)
3. NO repetition of RFP administrative requirements, deadlines, or formatting rules
4. NO generic marketing language - everything must be specific and evidence-based
5. NO boilerplate content that could apply to any contractor
6. FOCUS ONLY on demonstrating specific capability for THIS government requirement
7. ADDRESS ONLY what is explicitly requested in the section description
8. DEMONSTRATE understanding of the actual government work to be performed
9. PROVIDE concrete methodologies, processes, and measurable outcomes
10. USE professional, direct language appropriate for government evaluators

SECTION: {section_title}
DESCRIPTION: {section_description}

GOVERNMENT PROPOSAL CONTENT STANDARDS:
- Write ONLY about the specific capability requested in this section
- Demonstrate HOW you will perform the work with specific processes
- Show concrete understanding of government requirements
- Use relevant examples from similar government work (if applicable)
- Provide specific methodologies, tools, and success criteria
- Focus on CAPABILITY DEMONSTRATION, not company marketing
- Ensure every sentence adds value to the government's evaluation
- Write for government evaluators who need to assess your technical capability

Write substantive, government-compliant content that directly addresses ONLY the section requirements.
NO marketing language, NO contact info, NO RFP repetition - ONLY capability demonstration."""

            user_prompt = f"""Write professional proposal content for this section:

SECTION: {section_title}
REQUIREMENTS: {section_description}

CONTEXT FROM RFP:
{context[:2000] if context else "No specific context provided"}

COMPANY INFORMATION:
{company_context[:1000] if company_context else ""}

TENANT DETAILS:
{tenant_metadata[:500] if tenant_metadata else ""}

Generate complete, professional content that demonstrates capability and value.
NO placeholders, NO contact info repetition, NO administrative details."""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            result = self.llm.invoke(messages)
            content = result.content.strip()
            
            if content.startswith('```'):
                lines = content.split('\n')
                content = '\n'.join(lines[1:-1])
            
            return content
            
        except Exception as e:
            logger.error(f"ENHANCED: Error generating initial content - {str(e)}")
            return ""
    
    async def generate_validated_proposal(
        self,
        table_of_contents: List[Dict[str, Any]],
        opportunity_context: str,
        company_context: str = "",
        tenant_metadata: str = ""
    ) -> Dict[str, Any]:
        """
        Generate a complete validated proposal with all sections
        """
        logger.info(f"ENHANCED: Generating complete validated proposal with {len(table_of_contents)} sections")
        
        proposal_sections = []
        total_issues = 0
        total_fixes = 0
        
        for i, section in enumerate(table_of_contents, 1):
            section_title = section.get("title", f"Section {i}")
            section_description = section.get("description", "")
            
            logger.info(f"ENHANCED: Processing section {i}/{len(table_of_contents)}: '{section_title}'")
            
            # Generate validated section
            section_result = await self.generate_validated_section(
                section_title,
                section_description,
                opportunity_context,
                company_context,
                tenant_metadata
            )
            
            # Track statistics
            total_issues += len(section_result["issues_found"])
            total_fixes += section_result["fixes_applied"]
            
            # Add to proposal
            proposal_sections.append({
                "title": section_title,
                "description": section_description,
                "content": section_result["content"],
                "validation_passed": section_result["validation_passed"],
                "issues_found": section_result["issues_found"],
                "fixes_applied": section_result["fixes_applied"]
            })
            
            status = "PERFECT" if section_result["validation_passed"] else "HAS ISSUES"
            logger.info(f"   {status}: '{section_title}' - {len(section_result['content'])} chars, {section_result['fixes_applied']} fixes")
        
        # Calculate overall quality score
        total_sections = len(table_of_contents)
        perfect_sections = sum(1 for s in proposal_sections if s["validation_passed"])
        quality_score = (perfect_sections / total_sections) * 100 if total_sections > 0 else 0
        
        result = {
            "sections": proposal_sections,
            "total_sections": total_sections,
            "perfect_sections": perfect_sections,
            "quality_score": quality_score,
            "total_issues_found": total_issues,
            "total_fixes_applied": total_fixes,
            "validation_summary": {
                "passed": perfect_sections == total_sections,
                "score": f"{quality_score:.1f}%",
                "issues": total_issues,
                "fixes": total_fixes
            }
        }
        
        logger.info(f"   ENHANCED: Proposal generation complete!")
        logger.info(f"   Quality Score: {quality_score:.1f}%")
        logger.info(f"   Perfect Sections: {perfect_sections}/{total_sections}")
        logger.info(f"   Total Issues Found: {total_issues}")
        logger.info(f"   Total Fixes Applied: {total_fixes}")
        
        return result
