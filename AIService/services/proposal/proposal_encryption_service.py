from sqlalchemy.ext.asyncio import AsyncSession
from controllers.customer.proposal_encryption_controller import ProposalEncryptionController

class ProposalEncryptionService:
    """Service for proposal encryption business logic"""

    @staticmethod
    async def get_encryption(db: AsyncSession, tenant_id: str):
        return await ProposalEncryptionController.get_by_tenant_id(db, tenant_id)

    @staticmethod
    async def save_encryption(db, tenant_id, public_key, encrypted_private_key, salt, passphrase_hash):
        return await ProposalEncryptionController.add(
            db, tenant_id, public_key, encrypted_private_key, salt, passphrase_hash
        )

    @staticmethod
    async def update_encryption(db, tenant_id, public_key=None, encrypted_private_key=None, salt=None, passphrase_hash=None):
        return await ProposalEncryptionController.update(
            db, tenant_id, public_key, encrypted_private_key, salt, passphrase_hash
        )

    @staticmethod
    async def delete_encryption(db, tenant_id):
        return await ProposalEncryptionController.delete(db, tenant_id)

    @staticmethod
    async def exists_by_tenant_id(db, tenant_id):
        return await ProposalEncryptionController.exists_by_tenant_id(db, tenant_id)