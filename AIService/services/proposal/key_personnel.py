from typing import Any, Dict, List, Optional
from services.proposal.utilities import ProposalUtilities
from services.chroma.chroma_service import ChromaService
from database import get_kontratar_db, get_customer_db
from controllers.customer.key_personnel_uploads_controller import KeyPersonnelUploadsController
from langchain_ollama import ChatOllama
from loguru import logger
import base64

class KeyPersonnelService:
    """
    Service for generating key personnel requirements context and LLM output using ChromaDB and an LLM.
    """
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.chroma_service = ChromaService(embedding_api_url, None)
        #self.llm = KontratarLLM(api_url=llm_api_url, api_key=None)
        self.llm = ChatOllama(model="gemma3:27b", num_ctx = 5000, temperature=0.0, base_url=llm_api_url)

    async def extract_key_personnel(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
    ) -> Dict[str, Any]:
        """
        Generate key personnel requirements output using ChromaDB and LLM.
        Returns a dict with 'content' (parsed JSON) and 'context' (list of cleaned chunks).
        Raises an exception if a valid JSON is not produced after 3 attempts.
        """

        logger.info(f"Extracting key personnel for opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")

        chroma_query = '''
            Return excerpts from government solicitation documents that specify key personnel requirements including:
            - Required job titles or positions
            - Key personnel qualifications
            - Staffing requirements
            - Personnel experience requirements
            - Key personnel resumes or CVs needed
            - Project manager, technical lead, or other critical roles
        '''
        
        async for db in get_kontratar_db():
            max_chunks = 5
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            logger.info(f"Using collection_name: '{collection_name}' for ChromaDB retrieval")
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, chroma_query, n_results=max_chunks
            )
            logger.info(f"Retrieved {len(relevant_chunks)} relevant chunks from ChromaDB for key personnel extraction")
            requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            logger.debug(f"Requirements context chunks: {requirements_context}")
            context_str = "\n".join(requirements_context)
            logger.debug(f"Context string for LLM prompt: {context_str[:500]}{'...' if len(context_str) > 500 else ''}")
            break
        
        system_prompt = '''
        Role: Government Solicitation Key Personnel Expert
        Task: Generate a JSON response with key personnel requirements.

        Rules:
        1. **Output Format** :
            {{
                "resumes_needed": boolean,
                "personnel": [
                    {{
                        "title": string,
                        "responsibilities": string
                    }}
                ]
            }}

            - Use the information found in <context> to generate the key personnel requirements in this structure.
            - "resumes_needed" should be true if the solicitation explicitly requires resumes, CVs, or personnel qualifications.
            - "personnel" should contain an array of objects, each with:
                - "title": the job title or position mentioned in the solicitation (e.g., "Project Manager").
                - "responsibilities": a concise summary of the key responsibilities or duties for that role, as described or implied in the solicitation.

            Example Response:
            {{
                "resumes_needed": true,
                "personnel": [
                    {{
                        "title": "Project Manager",
                        "responsibilities": "Oversees project execution, manages team deliverables, and serves as the primary point of contact."
                    }},
                    {{
                        "title": "Technical Lead",
                        "responsibilities": "Leads technical solution development and ensures compliance with technical requirements."
                    }},
                    {{
                        "title": "Senior Developer",
                        "responsibilities": "Designs and implements software components and supports integration efforts."
                    }},
                    {{
                        "title": "Quality Assurance Specialist",
                        "responsibilities": "Ensures deliverables meet quality standards and conducts testing and validation."
                    }}
                ]
            }}
        '''
        user_prompt = f'''
        Generate key personnel requirements for this solicitation in JSON format using the following context:

        <context>
            {context_str}
        </context>

        **Constraints**:

        - DO NOT add any text before or after the JSON requirements, just generate the requirement in the correct structure
        - For "resumes_needed", set to true if the document mentions resumes, CVs, personnel qualifications, or key personnel submissions
        - For "personnel", extract all job titles, positions, or roles mentioned as required or key personnel
        - Use clear, professional job titles (e.g., "Project Manager", "Technical Lead", "Senior Developer")
        - If no specific personnel requirements are found, return an empty array for personnel and false for resumes_needed

        **Defaults**:
        - "resumes_needed": false
        - "personnel": []
        ''' 

        logger.info("Constructed system and user prompts for LLM key personnel extraction")
        logger.debug(f"System prompt: {system_prompt}")
        logger.debug(f"User prompt: {user_prompt[:500]}{'...' if len(user_prompt) > 500 else ''}")

        messages = [
            ("system", system_prompt),
            ("human", user_prompt)
        ]
        logger.info("Invoking LLM for key personnel extraction")
        result = self.llm.invoke(messages)
        logger.info("LLM invocation complete")
        logger.debug(f"LLM raw result: {str(result.content)[:500]}{'...' if len(str(result.content)) > 500 else ''}")
        
        personnel = ProposalUtilities.extract_json_from_brackets(str(result.content))
        logger.info(f"Extracted personnel JSON: {personnel}")

        return personnel if personnel is not None else {}

    async def get_personnel_resumes(
        self,
        opportunity_id: str,
        tenant_id: str
    ) -> List[Dict[str, Any]]:
        """
        Fetch real resume data from KeyPersonnelUploads table.
        Returns a list of personnel with their resume data.
        """
        logger.info(f"Fetching personnel resumes for opportunity_id={opportunity_id}, tenant_id={tenant_id}")

        try:
            async for db in get_customer_db():
                resumes = await KeyPersonnelUploadsController.get_all_by_tenant_and_opportunity(
                    db=db,
                    tenant_id=tenant_id,
                    opportunity_id=opportunity_id
                )

                logger.info(f"Retrieved {len(resumes)} personnel resumes from database")

                # Process resume data for LLM consumption
                processed_resumes = []
                for resume in resumes:
                    processed_resume = {
                        "job_title": resume.get("job_title", ""),
                        "resume_text": resume.get("resume_text", ""),
                        "has_resume_file": resume.get("resume_file") is not None
                    }

                    if not processed_resume["resume_text"] and processed_resume["has_resume_file"]:
                        processed_resume["resume_text"] = "[Resume content available in file format - text extraction may be needed]"

                    processed_resumes.append(processed_resume)

                return processed_resumes

        except Exception as e:
            logger.error(f"Error fetching personnel resumes: {e}")
            return []

    async def generate_key_personnel_content(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        section_requirements: str = ""
    ) -> Dict[str, Any]:
        """
        Generate key personnel section content using real resume data from KeyPersonnelUploads.
        Returns formatted content for proposal inclusion.
        """
        logger.info(f"Generating key personnel content for opportunity_id={opportunity_id}, tenant_id={tenant_id}")

        requirements = await self.extract_key_personnel(opportunity_id, tenant_id, source)

        personnel_resumes = await self.get_personnel_resumes(opportunity_id, tenant_id)

        if not personnel_resumes:
            logger.warning("No personnel resumes found - generating content based on requirements only")
            return {
                "content": self._generate_requirements_only_content(requirements, section_requirements),
                "personnel_count": 0,
                "has_real_resumes": False
            }

        content = await self._generate_content_with_resumes(
            requirements, personnel_resumes, section_requirements
        )

        return {
            "content": content,
            "personnel_count": len(personnel_resumes),
            "has_real_resumes": True,
            "personnel_titles": [resume["job_title"] for resume in personnel_resumes if resume["job_title"]]
        }

    def _generate_requirements_only_content(
        self,
        requirements: Dict[str, Any],
        section_requirements: str
    ) -> str:
        """Generate content when no real resumes are available."""

        personnel_list = requirements.get("personnel", [])
        resumes_needed = requirements.get("resumes_needed", False)

        if not personnel_list:
            return """
## Key Personnel

Our organization maintains a qualified pool of professionals and is committed to providing personnel who meet all project requirements. We will assign experienced professionals with the appropriate skills and certifications as specified in the solicitation.

### Staffing Approach

**Personnel Selection Process:**
1. **Requirements Analysis**: Thorough review of all personnel specifications and qualifications
2. **Candidate Identification**: Selection from our qualified talent pool based on project needs
3. **Qualification Verification**: Validation of credentials, certifications, and relevant experience
4. **Team Assembly**: Formation of project-specific teams optimized for success
5. **Client Approval**: All personnel assignments subject to client review and approval

**Quality Assurance:**
- All assigned personnel will meet or exceed the qualifications outlined in the solicitation
- Backup personnel are identified to ensure project continuity
- Regular performance monitoring and feedback mechanisms are in place
- Detailed personnel information and resumes will be provided upon contract award

Our personnel management approach ensures optimal skill-to-requirement matching while maintaining the highest standards of professional competency.
"""

        content = "## Key Personnel\n\n"
        content += "Our organization has identified the following key personnel positions required for this project and will assign qualified professionals to each role:\n\n"

        for i, person in enumerate(personnel_list, 1):
            if isinstance(person, dict):
                title = person.get("title", f"Position {i}")
                responsibilities = person.get("responsibilities", "")
                content += f"### {title}\n\n"
                if responsibilities:
                    content += f"**Role and Responsibilities:** {responsibilities}\n\n"
                content += "**Qualification Approach:**\n"
                content += "- Personnel will be selected from our qualified talent pool\n"
                content += "- All candidates undergo rigorous vetting to ensure RFP compliance\n"
                content += "- Specific qualifications and experience will align with project requirements\n"
                content += "- Security clearances and certifications will be verified as required\n\n"
            else:
                content += f"### {person}\n\n"
                content += "**Qualification Approach:**\n"
                content += "- Personnel will be selected from our qualified talent pool\n"
                content += "- All candidates undergo rigorous vetting to ensure RFP compliance\n"
                content += "- Specific qualifications and experience will align with project requirements\n\n"

        if resumes_needed:
            content += "### Documentation and Verification\n\n"
            content += "**Resume Submission Process:**\n"
            content += "- Detailed resumes for all key personnel will be provided as required\n"
            content += "- All documentation will demonstrate qualifications and relevant experience\n"
            content += "- Personnel assignments are subject to client approval\n"
            content += "- Additional personnel information available upon request\n\n"

        return content

    async def _generate_content_with_resumes(
        self,
        requirements: Dict[str, Any],
        personnel_resumes: List[Dict[str, Any]],
        section_requirements: str
    ) -> str:
        """Generate key personnel content using real resume data."""

        logger.info(f"Generating content with {len(personnel_resumes)} real resumes")

        # Prepare resume context for LLM
        resume_context = ""
        for i, resume in enumerate(personnel_resumes, 1):
            job_title = resume.get("job_title", f"Personnel {i}")
            resume_text = resume.get("resume_text", "")

            resume_context += f"\n--- {job_title} ---\n"
            if resume_text and resume_text != "[Resume content available in file format - text extraction may be needed]":
                # Truncate very long resumes for LLM context
                truncated_text = resume_text[:1500] + "..." if len(resume_text) > 1500 else resume_text
                resume_context += truncated_text
            else:
                resume_context += f"Resume file available for {job_title} - detailed qualifications on file."
            resume_context += "\n"

        # Prepare requirements context
        requirements_context = ""
        personnel_list = requirements.get("personnel", [])
        if personnel_list:
            requirements_context = "Required Personnel Positions:\n"
            for person in personnel_list:
                if isinstance(person, dict):
                    title = person.get("title", "")
                    responsibilities = person.get("responsibilities", "")
                    requirements_context += f"- {title}: {responsibilities}\n"
                else:
                    requirements_context += f"- {person}\n"

        system_prompt = """
You are a government proposal key personnel expert. Generate a professional key personnel section using the actual resume data provided.

**CRITICAL REQUIREMENTS:**
1. Use ONLY the real resume data provided - NO fabricated information
2. Match personnel to required positions based on their actual qualifications
3. Highlight specific experience and qualifications from the resumes
4. Maintain professional government proposal format
5. NO placeholders, brackets, or incomplete information
6. Focus on demonstrating how each person meets the specific requirements
7. NO repetitive phrases like "detailed qualifications repeated" or "see prior response"
8. NO meta-commentary about the content being generated
9. Write fresh, unique content for each section without referencing other sections

**FORMAT REQUIREMENTS:**
- Use clear section headers
- Include specific qualifications and experience for each person
- Highlight relevant government/contract experience when available
- Show how the team collectively meets all requirements
- Use professional, confident language
- Each person's description should be self-contained and complete
"""

        user_prompt = f"""
Generate a comprehensive Key Personnel section using the following information:

**SECTION REQUIREMENTS:**
{section_requirements}

**RFP PERSONNEL REQUIREMENTS:**
{requirements_context}

**ACTUAL PERSONNEL RESUMES:**
{resume_context}

**INSTRUCTIONS:**
1. Create a professional key personnel section that showcases our actual team
2. Match each person to the most appropriate required position
3. Highlight specific qualifications, experience, and achievements from their resumes
4. Show how the team collectively addresses all requirements
5. Include relevant certifications, education, and experience
6. Maintain government proposal standards and formatting

Generate the complete key personnel section content.
"""

        try:
            messages = [
                ("system", system_prompt),
                ("human", user_prompt)
            ]
            result = self.llm.invoke(messages)
            content = str(result.content).strip()

            logger.info(f"Generated key personnel content with real resumes ({len(content)} characters)")
            return content

        except Exception as e:
            logger.error(f"Error generating content with resumes: {e}")
            # Fallback to basic content
            return self._generate_basic_resume_content(personnel_resumes)

    def _generate_basic_resume_content(self, personnel_resumes: List[Dict[str, Any]]) -> str:
        """Generate basic key personnel content when LLM fails."""

        content = "## Key Personnel\n\n"
        content += "Our organization has assembled a qualified team of professionals for this project:\n\n"

        for i, resume in enumerate(personnel_resumes, 1):
            job_title = resume.get("job_title", f"Team Member {i}")
            has_text = resume.get("resume_text") and resume.get("resume_text") != "[Resume content available in file format - text extraction may be needed]"

            content += f"### {job_title}\n\n"

            if has_text:
                content += "**Qualifications**: Experienced professional with demonstrated expertise in relevant areas. "
                content += "Detailed qualifications and experience documented in submitted resume.\n\n"
            else:
                content += "**Qualifications**: Qualified professional with relevant experience. "
                content += "Complete resume and qualifications available upon request.\n\n"

        content += "### Team Commitment\n\n"
        content += "All key personnel are committed to the successful completion of this project and "
        content += "possess the necessary qualifications to meet all specified requirements.\n\n"

        return content

    @staticmethod
    def is_valid_key_personnel_requirements(data: dict) -> bool:
        try:
            # Check if all required keys are present
            required_keys = ["resumes_needed", "personnel"]
            for key in required_keys:
                if key not in data:
                    return False
            
            # Validate resumes_needed is boolean
            if not isinstance(data["resumes_needed"], bool):
                return False
            
            # Validate personnel is a list
            if not isinstance(data["personnel"], list):
                return False
            
            # Validate personnel items (can be strings or dicts)
            for item in data["personnel"]:
                if isinstance(item, dict):
                    # If it's a dict, it should have title and responsibilities
                    if "title" not in item:
                        return False
                elif not isinstance(item, str):
                    return False
            
            return True
        except Exception:
            return False