import json
from typing import Dict, List, Any, Optional
from loguru import logger
from langchain_ollama import ChatOllama
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type

from .proposal_validator import ProposalValidator, ValidationReport
from .rfp_compliance_checker import RFPCompliance<PERSON>hecker, RFPRequirement
from .validation_logger import validation_logger

class EnhancedDraftGenerator:
    """
    Enhanced draft generator with integrated validation
    """
    
    def __init__(self, llm_api_url: str = "http://ai.kontratar.com:11434"):
        self.llm = ChatOllama(model="gemma3:27b", temperature=0.0, base_url=llm_api_url)
        self.validator = ProposalValidator()
        self.compliance_checker = RFPComplianceChecker(llm_api_url)

    async def generate_validated_draft(
        self,
        section_title: str,
        section_desc: str,
        section_number: str,
        rfp_context: str,
        company_context: str,
        sow_tasks: List[Dict[str, Any]] = None,
        rfp_requirements: List[RFPRequirement] = None,
        max_retries: int = 3
    ) -> Dict[str, Any]:
        """
        Generate a draft with integrated validation and quality control
        """
        validation_logger.start_section_validation(section_title, section_number)
        validation_logger.log_validation_step("Starting enhanced draft generation", {
            "section_title": section_title,
            "section_number": section_number,
            "max_retries": max_retries
        })
        
        best_draft = None
        best_validation_score = 0
        
        for attempt in range(max_retries):
            try:
                validation_logger.log_validation_step(f"Draft generation attempt {attempt + 1}")
                
                # Generate draft with enhanced prompts
                draft_content = await self._generate_draft_with_enhanced_prompts(
                    section_title, section_desc, rfp_context, company_context, sow_tasks
                )
                
                # Validate the generated content
                validation_logger.log_validation_step("Validating generated content")
                validation_report = await self._validate_draft_content(
                    draft_content, section_title, section_number, rfp_context, 
                    sow_tasks, rfp_requirements
                )
                
                # Calculate validation score
                validation_score = self._calculate_validation_score(validation_report)
                validation_logger.log_validation_step("Validation complete", {
                    "validation_score": validation_score,
                    "is_valid": validation_report.is_valid,
                    "total_issues": validation_report.total_issues
                })
                
                # If validation passes or this is the best attempt so far
                if validation_report.is_valid or validation_score > best_validation_score:
                    best_draft = {
                        "title": f"{section_number} {section_title}",
                        "content": draft_content,
                        "number": section_number,
                        "validation_report": validation_report,
                        "validation_score": validation_score
                    }
                    best_validation_score = validation_score
                    
                    if validation_report.is_valid:
                        validation_logger.log_validation_step("Draft passed validation - using this version")
                        break
                
                # If validation failed, log issues and retry
                if not validation_report.is_valid:
                    validation_logger.log_validation_step(f"Draft failed validation - attempt {attempt + 1}", {
                        "critical_issues": len(validation_report.critical_issues),
                        "high_issues": len(validation_report.high_issues)
                    })
                    
                    for issue in validation_report.critical_issues + validation_report.high_issues:
                        validation_logger.log_validation_issue(issue)
                
            except Exception as e:
                validation_logger.log_critical_error(f"Error in draft generation attempt {attempt + 1}", e)
                if attempt == max_retries - 1:
                    raise
        
        if best_draft is None:
            raise RuntimeError(f"Failed to generate valid draft for section {section_title} after {max_retries} attempts")
        
        validation_logger.complete_section_validation(best_draft["validation_report"])
        
        return best_draft

    async def _generate_draft_with_enhanced_prompts(
        self,
        section_title: str,
        section_desc: str,
        rfp_context: str,
        company_context: str,
        sow_tasks: List[Dict[str, Any]] = None
    ) -> str:
        """
        Generate draft content using enhanced prompts with critical validation rules
        """
        
        # Prepare SOW tasks context
        sow_context = ""
        if sow_tasks:
            sow_context = "\n".join([
                f"Task {i+1}: {task.get('description', '')}"
                for i, task in enumerate(sow_tasks)
            ])
        
        system_prompt = f"""
        You are an expert government proposal writer with 15+ years of experience. Your task is to generate high-quality, compliant proposal content.

        *CRITICAL VALIDATION RULES:*
        1. ONLY generate content that is explicitly requested in the RFP requirements
        2. NEVER repeat compliance tables or submission requirements from the RFP
        3. NEVER include unrequested content (government penalizes this with FAIL)
        4. NEVER use placeholders like [replace with...], [TBD], or [Client Name]
        5. NEVER duplicate contact information unless specifically requested
        6. Address ALL SOW tasks in proper sequence (1, 2, 3...)
        7. NEVER repeat deliverable schedules unless analysis is requested

        *CONTENT VALIDATION:*
        - Before generating, ask: "Did the RFP specifically request this content?"
        - If answer is NO, do not include it
        - Focus on demonstrating capability, not repeating requirements
        - Use specific company information, not generic statements

        *SOW TASKS TO ADDRESS:*
        {sow_context}

        *QUALITY REQUIREMENTS:*
        - Use specific, measurable examples
        - Demonstrate understanding through capability, not repetition
        - Avoid marketing language - focus on technical competence
        - Ensure content directly addresses evaluation factors
        - Use active voice and clear, professional language
        """

        user_prompt = f"""
        Generate content for the proposal section: {section_title}

        Section Description: {section_desc}

        RFP Context (what was specifically requested):
        {rfp_context[:2500]}

        Company Information:
        {company_context[:1500]}

        INSTRUCTIONS:
        1. Generate ONLY the section content - no title, no extra text
        2. Address the specific requirements mentioned in the RFP context
        3. Use the company information to demonstrate specific capabilities
        4. If SOW tasks are listed above, address them in sequence
        5. Focus on HOW you will meet requirements, not WHAT the requirements are
        6. Use specific examples and measurable outcomes
        7. Avoid generic statements and marketing language

        Generate the section content now:
        """

        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            result = self.llm.invoke(messages)
            content = str(result.content).strip()
            
            # Remove any markdown title if present
            lines = content.split('\n')
            if lines and lines[0].startswith('#'):
                content = '\n'.join(lines[1:]).strip()
            
            return content
            
        except Exception as e:
            logger.error(f"Error generating draft content: {str(e)}")
            raise

    async def _validate_draft_content(
        self,
        content: str,
        section_title: str,
        section_number: str,
        rfp_context: str,
        sow_tasks: List[Dict[str, Any]] = None,
        rfp_requirements: List[RFPRequirement] = None
    ) -> ValidationReport:
        """
        Comprehensive validation of generated draft content
        """
        
        validation_report = self.validator.validate_content_quality(content, section_title, section_number)
        
        if rfp_requirements:
            compliance_issues = self.compliance_checker.check_rfp_compliance(
                content, section_title, rfp_requirements, rfp_context
            )
            validation_report.high_issues.extend(compliance_issues)
        
        if sow_tasks:
            sow_issues = self.validator.validate_sow_task_coverage(content, sow_tasks, section_title)
            validation_report.high_issues.extend(sow_issues)
        
        # Content necessity validation
        evaluation_factors = self._extract_evaluation_factors(rfp_context)
        necessity_issues = self.validator.validate_content_necessity(content, evaluation_factors, section_title)
        validation_report.medium_issues.extend(necessity_issues)
        
        validation_report.is_valid = len(validation_report.critical_issues) == 0
        
        return validation_report

    def _calculate_validation_score(self, validation_report: ValidationReport) -> float:
        """
        Calculate a validation score from 0-100 based on issues found
        """
        if validation_report.total_issues == 0:
            return 100.0
        
        # Weight issues by severity
        penalty = (
            len(validation_report.critical_issues) * 25 +
            len(validation_report.high_issues) * 15 +
            len(validation_report.medium_issues) * 8 +
            len(validation_report.low_issues) * 3
        )
        
        score = max(0, 100 - penalty)
        return score

    def _extract_evaluation_factors(self, rfp_context: str) -> List[str]:
        """
        Extract evaluation factors from RFP context
        """
        # This could be enhanced with NLP
        factors = []
        
        lines = rfp_context.lower().split('\n')
        for line in lines:
            if any(keyword in line for keyword in ['factor', 'criteria', 'evaluate', 'weight']):
                if len(line.strip()) > 10 and len(line.strip()) < 200:
                    factors.append(line.strip())
        
        return factors[:10]
