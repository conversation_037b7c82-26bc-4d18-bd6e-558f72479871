import json
import logging
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

from langchain_ollama import ChatOllama

logger = logging.getLogger(__name__)

@dataclass
class ValidationIssue:
    issue_type: str
    severity: str
    message: str
    suggested_fix: str
    problematic_content: str

class AutoFixingValidator:
    """
    Enhanced validator that detects issues and automatically re-prompts for better content
    """
    
    def __init__(self, llm_api_url: str = "http://ai.kontratar.com:11434"):
        self.llm = ChatOllama(
            model="gemma3:27b", 
            num_ctx=6300, 
            temperature=0, 
            base_url=llm_api_url
        )
        logger.info(f"AUTO-FIXING VALIDATOR: Initialized with {llm_api_url}")
    
    async def validate_and_fix_content(
        self, 
        content: str, 
        section_title: str, 
        section_description: str,
        max_retries: int = 2
    ) -> Tuple[str, List[ValidationIssue]]:
        """
        Validate content and automatically fix issues by re-prompting
        
        Returns:
            Tuple[improved_content, remaining_issues]
        """
        logger.info(f"AUTO-FIX: Validating and fixing content for '{section_title}'")
        
        current_content = content
        all_issues = []
        
        for attempt in range(max_retries + 1):

            
            issues = await self._detect_all_issues(current_content, section_title)
            
            if not issues:
                logger.info(f"AUTO-FIX: No issues found - content is clean!")
                return current_content, []
            
            logger.warning(f"AUTO-FIX: Found {len(issues)} issues in attempt {attempt + 1}")
            for issue in issues:
                logger.warning(f"   - {issue.issue_type}: {issue.message}")
            
            if attempt == max_retries:
                logger.error(f"AUTO-FIX: Max retries reached - returning with {len(issues)} remaining issues")
                return current_content, issues
            
            try:
                fixed_content = await self._fix_content_government_strict(
                    current_content,
                    section_title,
                    section_description,
                    issues
                )
                
                if fixed_content and fixed_content != current_content:
                    logger.info(f"AUTO-FIX: Content improved in attempt {attempt + 1}")
                    current_content = fixed_content
                    all_issues.extend(issues)
                else:
                    logger.warning(f"AUTO-FIX: No improvement in attempt {attempt + 1}")
                    return current_content, issues
                    
            except Exception as e:
                logger.error(f"AUTO-FIX: Error fixing content - {str(e)}")
                return current_content, issues
        
        return current_content, []
    
    async def _detect_all_issues(self, content: str, section_title: str) -> List[ValidationIssue]:
        """Detect all quality issues in content"""
        
        try:
            system_prompt = """You are an expert GOVERNMENT PROPOSAL validator. Find ALL quality issues with 100% precision.

CRITICAL ISSUES TO DETECT:

1. PLACEHOLDERS (CRITICAL - IMMEDIATE REJECTION):
   - ANY text in brackets: [Project Manager Name], [PM Email], [Phone], [Address]
   - Incomplete information: TBD, TODO, "To Be Determined", "Insert here"
   - Template instructions: "Replace with", "Fill in", "Add details"
   - Generic references: [Company Name], [Client], [Contact]

2. CONTACT INFO VIOLATIONS (CRITICAL):
   - Contact details in cover letters/transmittal letters
   - Same contact info repeated multiple times in proposal
   - Email addresses appearing in technical sections
   - Phone numbers in inappropriate sections
   - Full addresses repeated throughout document

3. COMPLIANCE REPETITION (CRITICAL - SHOWS LACK OF UNDERSTANDING):
   - Copying RFP submission requirements into proposal
   - Repeating formatting instructions (font size, margins, page limits)
   - Restating evaluation criteria or selection processes
   - Including submission deadlines in proposal content
   - Copying deliverable schedules without analysis
   - Repeating administrative procedures from RFP

4. GENERIC MARKETING CONTENT (HIGH - SHOWS NO DIFFERENTIATION):
   - "Committed to excellence", "extensive experience", "proven track record"
   - "World-class service", "industry-leading solutions", "cutting-edge"
   - "We are proud to", "comprehensive solutions", "state-of-the-art"
   - Claims without specific evidence or metrics
   - Boilerplate language that could apply to any company

5. INAPPROPRIATE CONTENT PLACEMENT (HIGH):
   - Contact information in technical approach sections
   - Administrative details in capability demonstrations
   - Company history in methodology sections
   - Marketing language in compliance sections
   - Biographical info in technical discussions

6. INSUFFICIENT SPECIFICITY (MEDIUM):
   - Vague statements without concrete examples
   - Claims without supporting evidence
   - Generic processes without customization
   - Lack of specific methodologies or tools

Return JSON array with EXACT issues found:
[{
  "issue_type": "PLACEHOLDER|CONTACT_VIOLATION|COMPLIANCE_REPETITION|GENERIC_MARKETING|INAPPROPRIATE_PLACEMENT|INSUFFICIENT_SPECIFICITY",
  "severity": "CRITICAL|HIGH|MEDIUM",
  "message": "Specific description of the exact issue found",
  "suggested_fix": "Precise instruction on how to fix",
  "problematic_content": "Exact problematic text (max 150 chars)"
}]

CRITICAL: Government proposals must be 100% compliant. Any CRITICAL issue = proposal rejection.
If no issues found, return []."""

            user_prompt = f"""Section: {section_title}

Content to validate:
{content[:3000]}

Find ALL quality issues in this content."""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            result = self.llm.invoke(messages)
            response_text = result.content.strip()
            
            # Parse JSON response
            if response_text.startswith('```json'):
                response_text = response_text[7:-3]
            elif response_text.startswith('```'):
                response_text = response_text[3:-3]
            
            issues_data = json.loads(response_text)
            
            issues = []
            for issue_data in issues_data:
                issues.append(ValidationIssue(
                    issue_type=issue_data.get("issue_type", "UNKNOWN"),
                    severity=issue_data.get("severity", "MEDIUM"),
                    message=issue_data.get("message", "Unknown issue"),
                    suggested_fix=issue_data.get("suggested_fix", "Fix manually"),
                    problematic_content=issue_data.get("problematic_content", "")
                ))
            
            return issues
            
        except Exception as e:
            logger.error(f"AUTO-FIX: Error detecting issues - {str(e)}")
            return []
    
    async def _fix_content(
        self, 
        content: str, 
        section_title: str, 
        section_description: str, 
        issues: List[ValidationIssue]
    ) -> str:
        """Fix content by re-prompting with issue-specific instructions"""
        
        try:
            # Create specific fixing instructions based on issues
            fixing_instructions = self._create_fixing_instructions(issues)
            
            system_prompt = f"""You are an expert GOVERNMENT PROPOSAL writer. Rewrite content to meet 100% government compliance standards.

SECTION: {section_title}
DESCRIPTION: {section_description}

CRITICAL GOVERNMENT PROPOSAL FIXING RULES:
{fixing_instructions}

MANDATORY GOVERNMENT PROPOSAL STANDARDS:
- ZERO placeholders, brackets, TBD, TODO, or incomplete information
- NO contact information in technical sections (only in designated contact sections)
- NO repetition of RFP administrative requirements, deadlines, or formatting rules
- NO generic marketing language - be specific and evidence-based
- NO boilerplate content that could apply to any contractor
- FOCUS ONLY on demonstrating specific capability for THIS requirement
- USE concrete examples, specific methodologies, and measurable outcomes
- DEMONSTRATE understanding of the actual work to be performed
- SHOW how you will meet the specific government requirement
- PROVIDE substantive technical content that adds value

GOVERNMENT PROPOSAL CONTENT REQUIREMENTS:
- Address ONLY what is asked for in the section description
- Demonstrate specific understanding of government requirements
- Show concrete capability with relevant examples
- Use professional, clear, direct language
- Focus on HOW you will perform the work, not WHY you're qualified
- Provide specific processes, tools, and methodologies
- Include measurable outcomes and success criteria

Return ONLY the improved, government-compliant content with no explanations or formatting."""

            user_prompt = f"""Fix this content by addressing all quality issues:

ORIGINAL CONTENT:
{content}

ISSUES TO FIX:
{chr(10).join([f"- {issue.issue_type}: {issue.message}" for issue in issues])}

Provide improved, professional content that addresses all these issues."""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            result = self.llm.invoke(messages)
            improved_content = result.content.strip()
            
            # Remove any markdown formatting
            if improved_content.startswith('```'):
                lines = improved_content.split('\n')
                improved_content = '\n'.join(lines[1:-1])
            
            return improved_content
            
        except Exception as e:
            logger.error(f"AUTO-FIX: Error fixing content - {str(e)}")
            return content
    
    def _create_fixing_instructions(self, issues: List[ValidationIssue]) -> str:
        """Create specific government proposal fixing instructions based on detected issues"""

        instructions = []

        issue_types = [issue.issue_type for issue in issues]

        if "PLACEHOLDER" in issue_types:
            instructions.append("- CRITICAL: ELIMINATE ALL placeholders [Name], TBD, TODO, brackets - replace with specific content or omit entirely")
            instructions.append("- CRITICAL: NO incomplete information - every statement must be complete and specific")

        if "CONTACT_VIOLATION" in issue_types or "CONTACT_REPETITION" in issue_types:
            instructions.append("- CRITICAL: REMOVE ALL contact information from technical sections")
            instructions.append("- CRITICAL: Contact details belong ONLY in designated contact/administrative sections")
            instructions.append("- CRITICAL: NO email addresses, phone numbers, or addresses in capability demonstrations")

        if "COMPLIANCE_REPETITION" in issue_types:
            instructions.append("- CRITICAL: ELIMINATE repetition of RFP submission requirements, deadlines, formatting rules")
            instructions.append("- CRITICAL: DO NOT restate evaluation criteria or administrative procedures")
            instructions.append("- CRITICAL: Focus on HOW you will perform work, not restating what RFP already says")

        if "GENERIC_MARKETING" in issue_types:
            instructions.append("- HIGH: ELIMINATE generic marketing language ('committed to excellence', 'proven track record')")
            instructions.append("- HIGH: REPLACE with specific, measurable examples relevant to THIS requirement")
            instructions.append("- HIGH: NO boilerplate content - everything must be specific to this opportunity")

        if "INAPPROPRIATE_PLACEMENT" in issue_types:
            instructions.append("- HIGH: REMOVE company history, contact info, and administrative details from technical sections")
            instructions.append("- HIGH: Focus ONLY on technical capability and methodology for this section")

        if "INSUFFICIENT_SPECIFICITY" in issue_types:
            instructions.append("- MEDIUM: ADD specific methodologies, tools, processes, and measurable outcomes")
            instructions.append("- MEDIUM: DEMONSTRATE concrete understanding of the government requirement")

        instructions.append("- MANDATORY: Address ONLY the specific section requirements")
        instructions.append("- MANDATORY: Demonstrate HOW you will perform the work with specific processes")
        instructions.append("- MANDATORY: Use professional, direct language appropriate for government audience")

        return '\n'.join(instructions) if instructions else "- Ensure 100% government proposal compliance and specificity"

    async def _fix_content_government_strict(
        self,
        content: str,
        section_title: str,
        section_description: str,
        issues: List[ValidationIssue]
    ) -> str:
        """Enhanced government-strict content fixing with zero tolerance for compliance issues"""

        try:
            fixing_instructions = self._create_fixing_instructions(issues)

            system_prompt = f"""You are an expert GOVERNMENT PROPOSAL writer with ZERO TOLERANCE for compliance violations.

SECTION: {section_title}
DESCRIPTION: {section_description}

CRITICAL GOVERNMENT COMPLIANCE VIOLATIONS TO FIX:
{fixing_instructions}

MANDATORY GOVERNMENT PROPOSAL STANDARDS (100% COMPLIANCE REQUIRED):
- ABSOLUTELY NO placeholders, brackets, TBD, TODO, or incomplete information
- ABSOLUTELY NO contact information in technical sections
- ABSOLUTELY NO repetition of RFP requirements, deadlines, or administrative procedures
- ABSOLUTELY NO generic marketing language or boilerplate content
- ABSOLUTELY NO company history or biographical information in technical sections
- WRITE ONLY about the specific technical capability requested
- DEMONSTRATE HOW you will perform the work with concrete processes
- USE specific methodologies, tools, and measurable outcomes
- FOCUS EXCLUSIVELY on capability demonstration for THIS requirement
- ENSURE every sentence directly addresses the section requirements

GOVERNMENT EVALUATOR PERSPECTIVE:
- Government evaluators need to assess your technical capability
- They want to see HOW you will do the work, not WHO you are
- They need specific processes, not marketing claims
- They require evidence-based capability demonstration
- They will reject proposals with placeholders or compliance violations

REWRITE INSTRUCTIONS:
1. ELIMINATE every placeholder, bracket, TBD, TODO completely
2. REMOVE all contact information from technical content
3. DELETE all repetition of RFP administrative requirements
4. REPLACE generic marketing with specific capability demonstration
5. FOCUS ONLY on the technical work requested in this section
6. PROVIDE concrete methodologies and processes
7. DEMONSTRATE understanding of the government requirement
8. WRITE for government evaluators who assess technical capability

Return ONLY the rewritten, 100% government-compliant content with no explanations."""

            user_prompt = f"""REWRITE this content to achieve 100% government proposal compliance:

ORIGINAL CONTENT (WITH VIOLATIONS):
{content}

SPECIFIC VIOLATIONS TO FIX:
{chr(10).join([f"- {issue.severity} {issue.issue_type}: {issue.message}" for issue in issues])}

REWRITE to eliminate ALL violations and create government-compliant content that demonstrates specific technical capability for: {section_description}"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            result = self.llm.invoke(messages)
            improved_content = result.content.strip()

            # Remove any markdown formatting
            if improved_content.startswith('```'):
                lines = improved_content.split('\n')
                improved_content = '\n'.join(lines[1:-1])

            return improved_content

        except Exception as e:
            logger.error(f"GOVERNMENT AUTO-FIX: Error in strict fixing - {str(e)}")
            return content
