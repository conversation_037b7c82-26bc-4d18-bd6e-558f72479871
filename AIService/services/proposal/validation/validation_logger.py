import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
from loguru import logger
from .proposal_validator import ValidationReport, ValidationResult

@dataclass
class ValidationSession:
    session_id: str
    opportunity_id: str
    tenant_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    total_sections: int = 0
    sections_processed: int = 0
    total_issues: int = 0
    critical_issues: int = 0
    high_issues: int = 0
    validation_score: float = 0.0
    status: str = "IN_PROGRESS"

@dataclass
class SectionValidationLog:
    section_title: str
    section_number: str
    start_time: datetime
    end_time: Optional[datetime] = None
    validation_steps: List[str] = None
    issues_found: List[Dict[str, Any]] = None
    is_valid: bool = False
    processing_time_ms: float = 0.0

class ValidationLogger:
    """
    Comprehensive logging system for proposal validation processes
    """
    
    def __init__(self, log_directory: str = "logs/validation"):
        self.log_directory = Path(log_directory)
        self.log_directory.mkdir(parents=True, exist_ok=True)
        
        self.current_session: Optional[ValidationSession] = None
        self.section_logs: List[SectionValidationLog] = []
        
        self._setup_logging()

    def _setup_logging(self):
        """Setup enhanced logging configuration"""
        logger.remove()
        
        logger.add(
            sink=lambda msg: print(msg, end=""),
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>VALIDATION</cyan> | {message}",
            level="INFO"
        )
        
        validation_log_file = self.log_directory / "validation_detailed.log"
        logger.add(
            sink=str(validation_log_file),
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | VALIDATION | {message}",
            level="DEBUG",
            rotation="10 MB",
            retention="30 days"
        )
        
        json_log_file = self.log_directory / "validation_structured.jsonl"
        logger.add(
            sink=str(json_log_file),
            format="{message}",
            level="INFO",
            filter=lambda record: "STRUCTURED" in record["message"],
            rotation="10 MB",
            retention="30 days"
        )

    def start_validation_session(
        self, 
        opportunity_id: str, 
        tenant_id: str, 
        total_sections: int
    ) -> str:
        """Start a new validation session"""
        session_id = f"val_{opportunity_id}_{int(time.time())}"
        
        self.current_session = ValidationSession(
            session_id=session_id,
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            start_time=datetime.now(),
            total_sections=total_sections
        )
        
        logger.info(f"   VALIDATION SESSION STARTED")
        logger.info(f"   Session ID: {session_id}")
        logger.info(f"   Opportunity: {opportunity_id}")
        logger.info(f"   Tenant: {tenant_id}")
        logger.info(f"   Total Sections: {total_sections}")
        logger.info(f"   Started: {self.current_session.start_time}")
        
        session_data = {
            "event": "validation_session_started",
            "session_id": session_id,
            "opportunity_id": opportunity_id,
            "tenant_id": tenant_id,
            "total_sections": total_sections,
            "timestamp": self.current_session.start_time.isoformat()
        }
        logger.info(f"STRUCTURED: {json.dumps(session_data)}")
        
        return session_id

    def start_section_validation(self, section_title: str, section_number: str) -> None:
        """Start validation for a specific section"""
        section_log = SectionValidationLog(
            section_title=section_title,
            section_number=section_number,
            start_time=datetime.now(),
            validation_steps=[],
            issues_found=[]
        )
        
        self.section_logs.append(section_log)
        
        logger.info(f"SECTION VALIDATION STARTED")
        logger.info(f"   Section: {section_number} - {section_title}")
        logger.info(f"   Started: {section_log.start_time}")
        
        section_data = {
            "event": "section_validation_started",
            "session_id": self.current_session.session_id if self.current_session else None,
            "section_title": section_title,
            "section_number": section_number,
            "timestamp": section_log.start_time.isoformat()
        }
        logger.info(f"STRUCTURED: {json.dumps(section_data)}")

    def log_validation_step(self, step_name: str, details: Optional[Dict[str, Any]] = None) -> None:
        """Log a validation step"""
        if not self.section_logs:
            logger.warning("No active section validation to log step for")
            return
        
        current_section = self.section_logs[-1]
        current_section.validation_steps.append(step_name)
        
        logger.info(f"🔍 VALIDATION STEP: {step_name}")
        if details:
            for key, value in details.items():
                logger.info(f"   {key}: {value}")
        
        step_data = {
            "event": "validation_step",
            "session_id": self.current_session.session_id if self.current_session else None,
            "section_title": current_section.section_title,
            "step_name": step_name,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        logger.info(f"STRUCTURED: {json.dumps(step_data)}")

    def log_validation_issue(self, issue: ValidationResult) -> None:
        """Log a validation issue"""
        if not self.section_logs:
            logger.warning("No active section validation to log issue for")
            return
        
        current_section = self.section_logs[-1]
        issue_dict = {
            "severity": issue.severity.value,
            "issue_type": issue.issue_type,
            "message": issue.message,
            "suggested_fix": issue.suggested_fix,
            "line_number": issue.line_number,
            "timestamp": datetime.now().isoformat()
        }
        current_section.issues_found.append(issue_dict)
        
        severity_emoji = {
            "CRITICAL": "🚨",
            "HIGH": "⚠️",
            "MEDIUM": "⚡",
            "LOW": "ℹ️"
        }
        
        emoji = severity_emoji.get(issue.severity.value, "❓")
        logger.warning(f"{emoji} VALIDATION ISSUE [{issue.severity.value}]: {issue.message}")
        if issue.suggested_fix:
            logger.info(f"   💡 Suggested Fix: {issue.suggested_fix}")
        
        issue_data = {
            "event": "validation_issue",
            "session_id": self.current_session.session_id if self.current_session else None,
            "section_title": current_section.section_title,
            "issue": issue_dict
        }
        logger.info(f"STRUCTURED: {json.dumps(issue_data)}")

    def complete_section_validation(self, validation_report: ValidationReport) -> None:
        """Complete validation for a section"""
        if not self.section_logs:
            logger.warning("No active section validation to complete")
            return
        
        current_section = self.section_logs[-1]
        current_section.end_time = datetime.now()
        current_section.is_valid = validation_report.is_valid
        current_section.processing_time_ms = (
            current_section.end_time - current_section.start_time
        ).total_seconds() * 1000
        
        if self.current_session:
            self.current_session.sections_processed += 1
            self.current_session.total_issues += validation_report.total_issues
            self.current_session.critical_issues += len(validation_report.critical_issues)
            self.current_session.high_issues += len(validation_report.high_issues)
        
        logger.info(f" SECTION VALIDATION COMPLETED")
        logger.info(f"   Section: {current_section.section_number} - {current_section.section_title}")
        logger.info(f"   Valid: {validation_report.is_valid}")
        logger.info(f"   Issues: {validation_report.total_issues}")
        logger.info(f"   Processing Time: {current_section.processing_time_ms:.2f}ms")
        logger.info(f"   Steps Completed: {len(current_section.validation_steps)}")
        
        completion_data = {
            "event": "section_validation_completed",
            "session_id": self.current_session.session_id if self.current_session else None,
            "section_title": current_section.section_title,
            "section_number": current_section.section_number,
            "is_valid": validation_report.is_valid,
            "total_issues": validation_report.total_issues,
            "critical_issues": len(validation_report.critical_issues),
            "high_issues": len(validation_report.high_issues),
            "processing_time_ms": current_section.processing_time_ms,
            "validation_steps": current_section.validation_steps,
            "timestamp": current_section.end_time.isoformat()
        }
        logger.info(f"STRUCTURED: {json.dumps(completion_data)}")

    def complete_validation_session(self, validation_summary: Dict[str, Any]) -> None:
        """Complete the validation session"""
        if not self.current_session:
            logger.warning("No active validation session to complete")
            return
        
        self.current_session.end_time = datetime.now()
        self.current_session.validation_score = validation_summary.get("validation_score", 0.0)
        self.current_session.status = "COMPLETED" if validation_summary.get("overall_valid", False) else "FAILED"
        
        session_duration = (
            self.current_session.end_time - self.current_session.start_time
        ).total_seconds()
        
        logger.info(f" VALIDATION SESSION COMPLETED")
        logger.info(f"   Session ID: {self.current_session.session_id}")
        logger.info(f"   Status: {self.current_session.status}")
        logger.info(f"   Duration: {session_duration:.2f} seconds")
        logger.info(f"   Sections Processed: {self.current_session.sections_processed}/{self.current_session.total_sections}")
        logger.info(f"   Validation Score: {self.current_session.validation_score:.1f}%")
        logger.info(f"   Total Issues: {self.current_session.total_issues}")
        logger.info(f"   Critical Issues: {self.current_session.critical_issues}")
        logger.info(f"   High Issues: {self.current_session.high_issues}")
        
        self._save_session_summary(validation_summary)
        
        session_completion_data = {
            "event": "validation_session_completed",
            "session": asdict(self.current_session),
            "validation_summary": validation_summary,
            "session_duration_seconds": session_duration
        }
        logger.info(f"STRUCTURED: {json.dumps(session_completion_data, default=str)}")

    def _save_session_summary(self, validation_summary: Dict[str, Any]) -> None:
        """Save detailed session summary to file"""
        if not self.current_session:
            return
        
        summary_file = self.log_directory / f"session_{self.current_session.session_id}_summary.json"
        
        summary_data = {
            "session": asdict(self.current_session),
            "validation_summary": validation_summary,
            "section_logs": [asdict(log) for log in self.section_logs],
            "generated_at": datetime.now().isoformat()
        }
        
        with open(summary_file, 'w') as f:
            json.dump(summary_data, f, indent=2, default=str)
        
        logger.info(f"Session summary saved to: {summary_file}")

    def log_critical_error(self, error_message: str, exception: Optional[Exception] = None) -> None:
        """Log critical errors during validation"""
        logger.error(f"CRITICAL VALIDATION ERROR: {error_message}")
        if exception:
            logger.error(f"   Exception: {str(exception)}")
        
        if self.current_session:
            self.current_session.status = "FAILED"
        
        error_data = {
            "event": "validation_critical_error",
            "session_id": self.current_session.session_id if self.current_session else None,
            "error_message": error_message,
            "exception": str(exception) if exception else None,
            "timestamp": datetime.now().isoformat()
        }
        logger.info(f"STRUCTURED: {json.dumps(error_data)}")

validation_logger = ValidationLogger()
