import re
import json
import logging
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import logging
logger = logging.getLogger(__name__)

class ValidationSeverity(Enum):
    CRITICAL = "CRITICAL"  # Must fix - will cause proposal failure
    HIGH = "HIGH"         # Should fix - significant quality impact
    MEDIUM = "MEDIUM"     # Should review - moderate quality impact
    LOW = "LOW"          # Optional - minor quality improvement

@dataclass
class ValidationResult:
    is_valid: bool
    severity: ValidationSeverity
    issue_type: str
    message: str
    suggested_fix: Optional[str] = None
    line_number: Optional[int] = None

@dataclass
class ValidationReport:
    section_title: str
    section_number: str
    is_valid: bool
    critical_issues: List[ValidationResult]
    high_issues: List[ValidationResult]
    medium_issues: List[ValidationResult]
    low_issues: List[ValidationResult]
    
    @property
    def total_issues(self) -> int:
        return len(self.critical_issues) + len(self.high_issues) + len(self.medium_issues) + len(self.low_issues)
    
    @property
    def has_blocking_issues(self) -> bool:
        return len(self.critical_issues) > 0

class ProposalValidator:
    """
    Comprehensive proposal validation system that ensures quality and compliance
    """
    
    def __init__(self, llm_api_url: str = "http://ai.kontratar.com:11434"):
        from langchain_ollama import ChatOllama
        self.llm = ChatOllama(
            model="gemma3:27b",
            num_ctx=6300,
            temperature=0,
            base_url=llm_api_url
        )
        logger.info(f"VALIDATION: LLM initialized with {llm_api_url}")

    def validate_content_quality(self, content: str, section_title: str, section_number: str) -> ValidationReport:
        """
        Comprehensive content quality validation
        """
        logger.info(f"VALIDATION: Starting quality validation for section {section_number} - {section_title}")
        
        issues = []
        
        # Check for placeholders
        placeholder_issues = self._check_placeholders(content)
        issues.extend(placeholder_issues)
        
        # Check for generic content
        generic_issues = self._check_generic_content(content)
        issues.extend(generic_issues)
        
        # Check for compliance repetition
        compliance_issues = self._check_compliance_repetition(content)
        issues.extend(compliance_issues)
        
        # Check content length and substance
        substance_issues = self._check_content_substance(content, section_title)
        issues.extend(substance_issues)
        
        # Check for contact info duplication
        contact_issues = self._check_contact_duplication(content)
        issues.extend(contact_issues)
        
        # Categorize issues by severity
        critical_issues = [i for i in issues if i.severity == ValidationSeverity.CRITICAL]
        high_issues = [i for i in issues if i.severity == ValidationSeverity.HIGH]
        medium_issues = [i for i in issues if i.severity == ValidationSeverity.MEDIUM]
        low_issues = [i for i in issues if i.severity == ValidationSeverity.LOW]
        
        is_valid = len(critical_issues) == 0
        
        report = ValidationReport(
            section_title=section_title,
            section_number=section_number,
            is_valid=is_valid,
            critical_issues=critical_issues,
            high_issues=high_issues,
            medium_issues=medium_issues,
            low_issues=low_issues
        )
        
        logger.info(f"VALIDATION: Section {section_number} validation complete - Valid: {is_valid}, Issues: {report.total_issues}")
        
        return report

    def _check_placeholders(self, content: str) -> List[ValidationResult]:
        """LLM-based intelligent placeholder detection"""
        logger.info(f"VALIDATION: LLM checking for placeholder content")

        try:
            # Simple, focused prompt for placeholder detection
            system_prompt = """Find placeholders in text. Look for:
- Text in brackets: [Project Manager Name], [PM Email]
- TBD, TODO text
- Instructions to insert/replace content

Return JSON array: [{"type": "placeholder", "text": "found text", "fix": "how to fix"}]
If no placeholders, return []."""

            user_prompt = f"Find placeholders in: {content[:2000]}"

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            result = self.llm.invoke(messages)
            response_text = result.content.strip()

            if response_text.startswith('```json'):
                response_text = response_text[7:-3]
            elif response_text.startswith('```'):
                response_text = response_text[3:-3]

            placeholders = json.loads(response_text)

            issues = []
            for placeholder in placeholders:
                issues.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.CRITICAL,
                    issue_type="PLACEHOLDER_CONTENT",
                    message=f"Placeholder found: '{placeholder.get('text', 'Unknown')}'",
                    suggested_fix=placeholder.get('fix', 'Replace with specific content')
                ))
                logger.warning(f"LLM DETECTED PLACEHOLDER: {placeholder.get('text', 'Unknown')}")

            logger.info(f"VALIDATION: LLM placeholder check complete - {len(issues)} issues found")
            return issues

        except Exception as e:
            logger.error(f"VALIDATION: LLM placeholder check failed - {str(e)}")
            # Fallback to basic pattern matching
            return self._fallback_placeholder_check(content)

    def _fallback_placeholder_check(self, content: str) -> List[ValidationResult]:
        """Fallback pattern-based placeholder detection"""
        logger.info(f"VALIDATION: Using fallback placeholder detection")
        issues = []

        patterns = [r'\[.*?\]', r'\bTBD\b', r'\bTODO\b']
        for pattern in patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                issues.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.CRITICAL,
                    issue_type="PLACEHOLDER_CONTENT",
                    message=f"Placeholder found: '{match.group()}'",
                    suggested_fix="Replace with specific content"
                ))

        return issues

    def _check_generic_content(self, content: str) -> List[ValidationResult]:
        """LLM-based intelligent generic content detection"""
        logger.info(f"VALIDATION: LLM checking for generic content")

        try:
            system_prompt = """Find generic, non-specific content that doesn't add value. Look for:
- "Our company is committed to excellence"
- "We have extensive experience"
- "Proven track record"
- "World class service"
- Other marketing fluff without substance

Return JSON array: [{"type": "generic", "text": "found text", "fix": "make it specific"}]
If no generic content, return []."""

            user_prompt = f"Find generic content in: {content[:2000]}"

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            result = self.llm.invoke(messages)
            response_text = result.content.strip()
            
            if response_text.startswith('```json'):
                response_text = response_text[7:-3]
            elif response_text.startswith('```'):
                response_text = response_text[3:-3]

            generic_items = json.loads(response_text)

            issues = []
            for item in generic_items:
                issues.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.HIGH,
                    issue_type="GENERIC_CONTENT",
                    message=f"Generic content: '{item.get('text', 'Unknown')}'",
                    suggested_fix=item.get('fix', 'Replace with specific, measurable content')
                ))
                logger.warning(f"LLM DETECTED GENERIC: {item.get('text', 'Unknown')}")

            logger.info(f"VALIDATION: LLM generic content check complete - {len(issues)} issues found")
            return issues

        except Exception as e:
            logger.error(f"VALIDATION: LLM generic content check failed - {str(e)}")
            return []

    def _check_compliance_repetition(self, content: str) -> List[ValidationResult]:
        """LLM-based intelligent compliance repetition detection"""
        logger.info(f"VALIDATION: LLM checking for compliance repetition")

        try:
            system_prompt = """Find inappropriate repetition of RFP requirements. Look for:
- Submission deadlines and requirements
- Font/formatting specifications
- Evaluation criteria descriptions
- Administrative procedures
- Deliverable schedules (unless analysis requested)

Return JSON array: [{"type": "compliance", "text": "found text", "fix": "how to fix"}]
If no repetition, return []."""

            user_prompt = f"Find compliance repetition in: {content[:2000]}"

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            result = self.llm.invoke(messages)
            response_text = result.content.strip()

            if response_text.startswith('```json'):
                response_text = response_text[7:-3]
            elif response_text.startswith('```'):
                response_text = response_text[3:-3]

            compliance_items = json.loads(response_text)

            issues = []
            for item in compliance_items:
                issues.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.HIGH,
                    issue_type="COMPLIANCE_REPETITION",
                    message=f"Compliance repetition: '{item.get('text', 'Unknown')}'",
                    suggested_fix=item.get('fix', 'Remove repetition, focus on demonstrating capability')
                ))
                logger.warning(f"LLM DETECTED COMPLIANCE REPETITION: {item.get('text', 'Unknown')}")

            logger.info(f"VALIDATION: LLM compliance check complete - {len(issues)} issues found")
            return issues

        except Exception as e:
            logger.error(f"VALIDATION: LLM compliance check failed - {str(e)}")
            return []

    def _check_content_substance(self, content: str, section_title: str) -> List[ValidationResult]:
        """Check if content has sufficient substance and relevance"""
        issues = []
        
        if len(content.strip()) < 100:
            issues.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.CRITICAL,
                issue_type="INSUFFICIENT_CONTENT",
                message="Content is too short to be substantive",
                suggested_fix="Provide detailed, comprehensive content that addresses the section requirements"
            ))
            logger.warning(f"VALIDATION: Insufficient content length - {len(content)} characters")
        
        # Check for section title relevance
        title_words = section_title.lower().split()
        content_lower = content.lower()
        relevant_words = sum(1 for word in title_words if word in content_lower and len(word) > 3)
        
        if relevant_words < len(title_words) * 0.3:
            issues.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.HIGH,
                issue_type="CONTENT_RELEVANCE",
                message="Content may not be sufficiently relevant to section title",
                suggested_fix="Ensure content directly addresses the section requirements and title"
            ))
            logger.warning(f"VALIDATION: Low content relevance - {relevant_words}/{len(title_words)} title words found")
        
        return issues

    def _check_contact_duplication(self, content: str) -> List[ValidationResult]:
        """Check for unnecessary contact information duplication"""
        issues = []
        
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, content)
        
        if len(emails) > 1:
            issues.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.MEDIUM,
                issue_type="CONTACT_DUPLICATION",
                message=f"Multiple email addresses found ({len(emails)})",
                suggested_fix="Remove duplicate contact information unless specifically requested"
            ))
            logger.warning(f"VALIDATION: Contact duplication detected - {len(emails)} emails")
        
        phone_pattern = r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'
        phones = re.findall(phone_pattern, content)
        
        if len(phones) > 1:
            issues.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.MEDIUM,
                issue_type="CONTACT_DUPLICATION",
                message=f"Multiple phone numbers found ({len(phones)})",
                suggested_fix="Remove duplicate contact information unless specifically requested"
            ))
            logger.warning(f"VALIDATION: Contact duplication detected - {len(phones)} phones")
        
        return issues

    def validate_rfp_compliance(self, content: str, rfp_requirements: List[str], section_title: str) -> List[ValidationResult]:
        """
        Validate that content only includes what was specifically requested in RFP
        """
        logger.info(f"VALIDATION: Checking RFP compliance for section - {section_title}")
        
        issues = []
        
        #TO DO: Use actual RFP requirement parsing
        # Check if content addresses requirements without repeating them
        for requirement in rfp_requirements:
            if requirement.lower() in content.lower():
                # Check if it's just repetition vs. addressing the requirement
                requirement_mentions = len(re.findall(re.escape(requirement.lower()), content.lower()))
                if requirement_mentions > 2:
                    issues.append(ValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.HIGH,
                        issue_type="REQUIREMENT_REPETITION",
                        message=f"Requirement '{requirement}' mentioned {requirement_mentions} times",
                        suggested_fix="Address the requirement without repeating it verbatim"
                    ))
                    logger.warning(f"VALIDATION: Requirement repetition - '{requirement}' mentioned {requirement_mentions} times")
        
        logger.info(f"VALIDATION: RFP compliance check complete - {len(issues)} issues found")
        return issues

    def validate_sow_task_coverage(self, content: str, sow_tasks: List[Dict[str, Any]], section_title: str) -> List[ValidationResult]:
        """
        Validate that all Statement of Work tasks are properly addressed
        """
        logger.info(f"VALIDATION: Checking SOW task coverage for section - {section_title}")

        issues = []

        if not sow_tasks:
            logger.info("VALIDATION: No SOW tasks provided for validation")
            return issues

        # Check if tasks are addressed in proper sequence
        task_numbers_in_content = []
        for i, task in enumerate(sow_tasks, 1):
            task_patterns = [
                f"task\\s+{i}",
                f"task\\s+{i}\\.",
                f"{i}\\.",
                f"requirement\\s+{i}",
            ]

            found_task = False
            for pattern in task_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    task_numbers_in_content.append(i)
                    found_task = True
                    break

            if not found_task:
                # Check if task content is addressed without explicit numbering
                task_description = task.get('description', '').lower()
                if task_description and len(task_description) > 10:
                    key_words = [word for word in task_description.split() if len(word) > 4]
                    matches = sum(1 for word in key_words if word in content.lower())

                    if matches < len(key_words) * 0.3:
                        issues.append(ValidationResult(
                            is_valid=False,
                            severity=ValidationSeverity.HIGH,
                            issue_type="SOW_TASK_MISSING",
                            message=f"SOW Task {i} may not be adequately addressed",
                            suggested_fix=f"Ensure Task {i} is explicitly addressed: {task_description[:100]}..."
                        ))
                        logger.warning(f"VALIDATION: SOW Task {i} not adequately addressed")

        # Check for proper sequencing
        if len(task_numbers_in_content) > 1:
            if task_numbers_in_content != sorted(task_numbers_in_content):
                issues.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.MEDIUM,
                    issue_type="SOW_TASK_SEQUENCE",
                    message="SOW tasks not addressed in proper sequence",
                    suggested_fix="Address SOW tasks in numerical order (1, 2, 3...)"
                ))
                logger.warning(f"VALIDATION: SOW tasks out of sequence - found: {task_numbers_in_content}")

        logger.info(f"VALIDATION: SOW task coverage check complete - {len(issues)} issues found")
        return issues

    def validate_content_necessity(self, content: str, evaluation_factors: List[str], section_title: str) -> List[ValidationResult]:
        """
        Validate that content adds value and addresses evaluation factors
        """
        logger.info(f"VALIDATION: Checking content necessity for section - {section_title}")

        issues = []

        # Check if content addresses evaluation factors
        if evaluation_factors:
            factor_coverage = 0
            for factor in evaluation_factors:
                factor_words = factor.lower().split()
                content_lower = content.lower()
                matches = sum(1 for word in factor_words if word in content_lower and len(word) > 3)

                if matches > 0:
                    factor_coverage += 1

            coverage_percentage = factor_coverage / len(evaluation_factors) if evaluation_factors else 0

            if coverage_percentage < 0.5:
                issues.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.HIGH,
                    issue_type="EVALUATION_FACTOR_COVERAGE",
                    message=f"Only {factor_coverage}/{len(evaluation_factors)} evaluation factors addressed",
                    suggested_fix="Ensure content addresses the key evaluation factors for this section"
                ))
                logger.warning(f"VALIDATION: Low evaluation factor coverage - {factor_coverage}/{len(evaluation_factors)}")

        unnecessary_patterns = [
            r'as\s+required\s+by\s+the\s+rfp',
            r'in\s+accordance\s+with\s+the\s+solicitation',
            r'per\s+the\s+government\s+requirements',
            r'as\s+specified\s+in\s+the\s+rfp',
        ]

        for pattern in unnecessary_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                issues.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.MEDIUM,
                    issue_type="UNNECESSARY_CONTENT",
                    message=f"Unnecessary compliance language: '{match.group()}'",
                    suggested_fix="Focus on demonstrating capability rather than restating requirements"
                ))
                logger.warning(f"VALIDATION: Unnecessary content detected - '{match.group()}'")

        logger.info(f"VALIDATION: Content necessity check complete - {len(issues)} issues found")
        return issues

    def generate_validation_summary(self, reports: List[ValidationReport]) -> Dict[str, Any]:
        """
        Generate a comprehensive validation summary
        """
        total_sections = len(reports)
        valid_sections = sum(1 for report in reports if report.is_valid)
        total_issues = sum(report.total_issues for report in reports)
        blocking_sections = sum(1 for report in reports if report.has_blocking_issues)

        critical_issues = []
        high_issues = []

        for report in reports:
            critical_issues.extend(report.critical_issues)
            high_issues.extend(report.high_issues)

        summary = {
            "overall_valid": blocking_sections == 0,
            "total_sections": total_sections,
            "valid_sections": valid_sections,
            "sections_with_blocking_issues": blocking_sections,
            "total_issues": total_issues,
            "critical_issues_count": len(critical_issues),
            "high_issues_count": len(high_issues),
            "validation_score": (valid_sections / total_sections * 100) if total_sections > 0 else 0,
            "recommendations": self._generate_recommendations(reports)
        }

        logger.info(f"VALIDATION SUMMARY: {valid_sections}/{total_sections} sections valid, {total_issues} total issues")

        return summary

    def _generate_recommendations(self, reports: List[ValidationReport]) -> List[str]:
        """Generate actionable recommendations based on validation results"""
        recommendations = []

        issue_counts = {}
        for report in reports:
            all_issues = report.critical_issues + report.high_issues + report.medium_issues + report.low_issues
            for issue in all_issues:
                issue_counts[issue.issue_type] = issue_counts.get(issue.issue_type, 0) + 1

        if issue_counts.get("PLACEHOLDER_CONTENT", 0) > 0:
            recommendations.append("Remove all placeholder content and replace with specific, relevant information")

        if issue_counts.get("GENERIC_CONTENT", 0) > 0:
            recommendations.append("Replace generic statements with specific, tailored content that demonstrates unique value")

        if issue_counts.get("COMPLIANCE_REPETITION", 0) > 0:
            recommendations.append("Focus on demonstrating capabilities rather than repeating RFP requirements")

        if issue_counts.get("SOW_TASK_MISSING", 0) > 0:
            recommendations.append("Ensure all Statement of Work tasks are explicitly addressed in proper sequence")

        if issue_counts.get("CONTENT_RELEVANCE", 0) > 0:
            recommendations.append("Improve content relevance to section titles and requirements")

        return recommendations
