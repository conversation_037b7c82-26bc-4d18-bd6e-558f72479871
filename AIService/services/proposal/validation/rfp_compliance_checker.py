import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from loguru import logger
from langchain_ollama import Chat<PERSON>lla<PERSON>
from .proposal_validator import ValidationResult, ValidationSeverity

@dataclass
class RFPRequirement:
    section: str
    requirement_text: str
    is_mandatory: bool
    evaluation_weight: Optional[float] = None
    page_limit: Optional[int] = None

class RFPComplianceChecker:
    """
    LLM-powered RFP compliance checker that intelligently validates content against RFP requirements
    """
    
    def __init__(self, llm_api_url: str = "http://ai.kontratar.com:11434"):
        self.llm = ChatOllama(model="gemma3:27b", temperature=0.0, base_url=llm_api_url)

    def check_rfp_compliance(
        self, 
        content: str, 
        section_title: str,
        rfp_requirements: List[RFPRequirement],
        rfp_context: str
    ) -> List[ValidationResult]:
        """
        LLM-powered comprehensive RFP compliance check
        """
        logger.info(f"COMPLIANCE: Starting intelligent RFP compliance check for section - {section_title}")
        
        issues = []
        
        forbidden_issues = self._llm_check_forbidden_content(content, section_title, rfp_context)
        issues.extend(forbidden_issues)
        
        unrequested_issues = self._llm_check_unrequested_content(content, section_title, rfp_context)
        issues.extend(unrequested_issues)
        
        requirement_issues = self._llm_check_requirement_addressing(content, rfp_requirements, section_title)
        issues.extend(requirement_issues)
        
        value_issues = self._llm_check_content_value(content, section_title, rfp_context)
        issues.extend(value_issues)
        
        logger.info(f"COMPLIANCE: RFP compliance check complete - {len(issues)} issues found")
        return issues

    def _llm_check_forbidden_content(self, content: str, section_title: str, rfp_context: str) -> List[ValidationResult]:
        """Use LLM to intelligently check for content that repeats RFP submission/format requirements"""
        logger.info(f"LLM: Checking for forbidden content repetition in section - {section_title}")
        
        system_prompt = """
        You are an expert government proposal compliance validator. Your task is to identify content that inappropriately repeats RFP administrative, submission, or formatting requirements instead of demonstrating capability.

        CRITICAL VALIDATION RULES:
        1. NEVER repeat compliance tables or submission requirements from the RFP
        2. NEVER repeat formatting requirements (font, margins, page limits)
        3. NEVER repeat evaluation criteria or selection processes
        4. NEVER repeat submission deadlines or administrative procedures
        5. Focus should be on demonstrating capability, not restating requirements

        Return a JSON array of issues found. Each issue should have:
        - "severity": "CRITICAL" | "HIGH" | "MEDIUM" | "LOW"
        - "issue_type": "FORBIDDEN_CONTENT_REPETITION"
        - "message": Brief description of the issue
        - "suggested_fix": How to fix the issue
        - "content_snippet": The problematic content (max 100 chars)

        If no issues found, return empty array [].
        """

        user_prompt = f"""
        Section Title: {section_title}

        RFP Context (what was actually requested):
        {rfp_context[:2000]}

        Content to Validate:
        {content[:3000]}

        Analyze the content and identify any inappropriate repetition of RFP administrative, formatting, or submission requirements. Focus on content that restates what the government said rather than demonstrating the contractor's capability.
        """

        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            result = self.llm.invoke(messages)
            
            response_text = result.content.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:-3]
            elif response_text.startswith('```'):
                response_text = response_text[3:-3]
            
            issues_data = json.loads(response_text)
            
            issues = []
            for issue_data in issues_data:
                issues.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity(issue_data["severity"]),
                    issue_type=issue_data["issue_type"],
                    message=issue_data["message"],
                    suggested_fix=issue_data["suggested_fix"]
                ))
                logger.warning(f"LLM DETECTED: {issue_data['message']}")
            
            logger.info(f"LLM: Forbidden content check complete - {len(issues)} issues found")
            return issues
            
        except Exception as e:
            logger.error(f"LLM: Error in forbidden content check - {str(e)}")
            return []

    def _llm_check_unrequested_content(self, content: str, section_title: str, rfp_context: str) -> List[ValidationResult]:
        """Use LLM to intelligently check for content not specifically requested in RFP"""
        logger.info(f"LLM: Checking for unrequested content in section - {section_title}")
        
        system_prompt = """
        You are an expert government proposal compliance validator. Your task is to identify content that was NOT specifically requested in the RFP.

        CRITICAL VALIDATION RULES:
        1. ONLY generate content that is explicitly requested in the RFP requirements
        2. NEVER include unrequested content (government penalizes this with FAIL)
        3. NEVER include company promotional content unless specifically requested
        4. NEVER duplicate contact information unless specifically requested
        5. NEVER include generic capability statements unless specifically requested

        Common unrequested content to flag:
        - Company history/background (unless requested)
        - Mission/vision statements (unless requested)
        - Awards and recognition (unless requested)
        - Generic "we are committed to excellence" statements
        - Unnecessary contact information repetition
        - Marketing language not addressing specific requirements

        Return a JSON array of issues found. Each issue should have:
        - "severity": "CRITICAL" | "HIGH" | "MEDIUM" | "LOW"
        - "issue_type": "UNREQUESTED_CONTENT"
        - "message": Brief description of the issue
        - "suggested_fix": How to fix the issue
        - "content_snippet": The problematic content (max 100 chars)

        If no issues found, return empty array [].
        """

        user_prompt = f"""
        Section Title: {section_title}

        RFP Context (what was actually requested):
        {rfp_context[:2000]}

        Content to Validate:
        {content[:3000]}

        Analyze the content and identify any information that was NOT specifically requested in the RFP context. Focus on content that seems promotional, generic, or unnecessary rather than addressing specific RFP requirements.
        """

        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            result = self.llm.invoke(messages)
            
            
            response_text = result.content.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:-3]
            elif response_text.startswith('```'):
                response_text = response_text[3:-3]
            
            issues_data = json.loads(response_text)
            
            issues = []
            for issue_data in issues_data:
                issues.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity(issue_data["severity"]),
                    issue_type=issue_data["issue_type"],
                    message=issue_data["message"],
                    suggested_fix=issue_data["suggested_fix"]
                ))
                logger.warning(f"LLM DETECTED: {issue_data['message']}")
            
            logger.info(f"LLM: Unrequested content check complete - {len(issues)} issues found")
            return issues
            
        except Exception as e:
            logger.error(f"LLM: Error in unrequested content check - {str(e)}")
            return []

    def _llm_check_requirement_addressing(
        self,
        content: str,
        rfp_requirements: List[RFPRequirement],
        section_title: str
    ) -> List[ValidationResult]:
        """Use LLM to intelligently check that requirements are addressed, not repeated"""
        logger.info(f"LLM: Checking requirement addressing in section - {section_title}")

        if not rfp_requirements:
            return []

        # Prepare requirements for LLM analysis
        requirements_text = "\n".join([
            f"- {req.requirement_text} (Mandatory: {req.is_mandatory})"
            for req in rfp_requirements
        ])

        system_prompt = """
        You are an expert government proposal compliance validator. Your task is to determine if RFP requirements are properly addressed versus inappropriately repeated.

        CRITICAL VALIDATION RULES:
        1. Requirements should be ADDRESSED by demonstrating capability, not REPEATED verbatim
        2. Mandatory requirements MUST be addressed substantively
        3. Content should show HOW the contractor will meet requirements, not restate what the requirements are
        4. Avoid copying requirement text - instead demonstrate understanding and capability

        Analyze for these issues:
        - REQUIREMENT_REPETITION: Requirement text copied/repeated instead of addressed
        - REQUIREMENT_NOT_ADDRESSED: Mandatory requirement not adequately addressed
        - SUPERFICIAL_ADDRESSING: Requirement mentioned but not substantively addressed

        Return a JSON array of issues found. Each issue should have:
        - "severity": "CRITICAL" | "HIGH" | "MEDIUM" | "LOW"
        - "issue_type": "REQUIREMENT_REPETITION" | "REQUIREMENT_NOT_ADDRESSED" | "SUPERFICIAL_ADDRESSING"
        - "message": Brief description of the issue
        - "suggested_fix": How to fix the issue
        - "requirement_text": The requirement that has the issue (max 100 chars)

        If no issues found, return empty array [].
        """

        user_prompt = f"""
        Section Title: {section_title}

        RFP Requirements for this section:
        {requirements_text}

        Content to Validate:
        {content[:3000]}

        Analyze how well the content addresses the requirements. Look for repetition of requirement text versus substantive demonstration of capability to meet the requirements.
        """

        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            result = self.llm.invoke(messages)

            
            response_text = result.content.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:-3]
            elif response_text.startswith('```'):
                response_text = response_text[3:-3]

            issues_data = json.loads(response_text)

            issues = []
            for issue_data in issues_data:
                issues.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity(issue_data["severity"]),
                    issue_type=issue_data["issue_type"],
                    message=issue_data["message"],
                    suggested_fix=issue_data["suggested_fix"]
                ))
                logger.warning(f"LLM DETECTED: {issue_data['message']}")

            logger.info(f"LLM: Requirement addressing check complete - {len(issues)} issues found")
            return issues

        except Exception as e:
            logger.error(f"LLM: Error in requirement addressing check - {str(e)}")
            return []

    def _llm_check_content_value(self, content: str, section_title: str, rfp_context: str) -> List[ValidationResult]:
        """Use LLM to intelligently check if content adds value and is necessary"""
        logger.info(f"LLM: Checking content value and necessity in section - {section_title}")

        system_prompt = """
        You are an expert government proposal compliance validator. Your task is to identify content that adds no value or is unnecessary filler.

        CRITICAL VALIDATION RULES:
        1. Content must add value and address evaluation factors
        2. Avoid filler phrases that add no substance
        3. Avoid excessive marketing language without substance
        4. Focus on specific, measurable capabilities and achievements
        5. Remove content that doesn't directly address RFP requirements

        Common issues to flag:
        - FILLER_CONTENT: Phrases like "needless to say", "obviously", "clearly"
        - EXCESSIVE_MARKETING: Too many superlatives without substance
        - IRRELEVANT_CONTENT: Content not related to section requirements
        - VAGUE_STATEMENTS: Generic statements without specific details

        Return a JSON array of issues found. Each issue should have:
        - "severity": "CRITICAL" | "HIGH" | "MEDIUM" | "LOW"
        - "issue_type": "FILLER_CONTENT" | "EXCESSIVE_MARKETING" | "IRRELEVANT_CONTENT" | "VAGUE_STATEMENTS"
        - "message": Brief description of the issue
        - "suggested_fix": How to fix the issue
        - "content_snippet": The problematic content (max 100 chars)

        If no issues found, return empty array [].
        """

        user_prompt = f"""
        Section Title: {section_title}

        RFP Context (what was requested):
        {rfp_context[:2000]}

        Content to Validate:
        {content[:3000]}

        Analyze the content for value and necessity. Identify any filler content, excessive marketing language, or content that doesn't add value to addressing the RFP requirements.
        """

        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            result = self.llm.invoke(messages)

            
            response_text = result.content.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:-3]
            elif response_text.startswith('```'):
                response_text = response_text[3:-3]

            issues_data = json.loads(response_text)

            issues = []
            for issue_data in issues_data:
                issues.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity(issue_data["severity"]),
                    issue_type=issue_data["issue_type"],
                    message=issue_data["message"],
                    suggested_fix=issue_data["suggested_fix"]
                ))
                logger.warning(f"LLM DETECTED: {issue_data['message']}")

            logger.info(f"LLM: Content value check complete - {len(issues)} issues found")
            return issues

        except Exception as e:
            logger.error(f"LLM: Error in content value check - {str(e)}")
            return []

    def validate_section_necessity(
        self,
        section_title: str,
        section_content: str,
        rfp_requirements: List[RFPRequirement]
    ) -> ValidationResult:
        """
        Use LLM to validate if an entire section is necessary based on RFP requirements
        """
        logger.info(f"LLM: Validating section necessity - {section_title}")

        requirements_text = "\n".join([req.requirement_text for req in rfp_requirements])

        system_prompt = """
        You are an expert government proposal compliance validator. Your task is to determine if a proposal section is necessary based on RFP requirements.

        CRITICAL VALIDATION RULES:
        1. Sections must be explicitly or implicitly required by the RFP
        2. Government penalizes unrequested sections with FAIL
        3. Every section must address specific RFP requirements or evaluation factors
        4. Avoid sections that are just promotional or not requested

        Analyze if the section is:
        - REQUIRED: Explicitly requested in RFP requirements
        - IMPLIED: Implicitly needed to address RFP requirements
        - UNNECESSARY: Not required and potentially harmful

        Return a JSON object with:
        - "is_necessary": true/false
        - "confidence": "HIGH" | "MEDIUM" | "LOW"
        - "reasoning": Brief explanation
        - "recommendation": What to do with this section
        """

        user_prompt = f"""
        Section Title: {section_title}

        RFP Requirements:
        {requirements_text}

        Section Content Preview:
        {section_content[:1000]}

        Determine if this section is necessary based on the RFP requirements. Consider if it's explicitly requested, implicitly needed, or unnecessary.
        """

        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            result = self.llm.invoke(messages)

            
            response_text = result.content.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:-3]
            elif response_text.startswith('```'):
                response_text = response_text[3:-3]

            analysis = json.loads(response_text)

            if analysis["is_necessary"]:
                logger.info(f"LLM: Section '{section_title}' is necessary - {analysis['reasoning']}")
                return ValidationResult(
                    is_valid=True,
                    severity=ValidationSeverity.LOW,
                    issue_type="SECTION_NECESSITY",
                    message=f"Section is necessary: {analysis['reasoning']}"
                )
            else:
                logger.warning(f"LLM: Section '{section_title}' may not be necessary - {analysis['reasoning']}")
                severity = ValidationSeverity.HIGH if analysis["confidence"] == "HIGH" else ValidationSeverity.MEDIUM
                return ValidationResult(
                    is_valid=False,
                    severity=severity,
                    issue_type="SECTION_NOT_REQUIRED",
                    message=f"Section may not be required: {analysis['reasoning']}",
                    suggested_fix=analysis["recommendation"]
                )

        except Exception as e:
            logger.error(f"LLM: Error in section necessity validation - {str(e)}")
            return ValidationResult(
                is_valid=True,
                severity=ValidationSeverity.LOW,
                issue_type="SECTION_NECESSITY",
                message="Could not validate section necessity due to LLM error"
            )
