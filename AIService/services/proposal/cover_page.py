import asyncio
from typing import Any, Dict, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from services.chroma.chroma_service import ChromaService
from utils.llm import KontratarLLM
from database import get_kontratar_db
from langchain_ollama import ChatOllama

## I have a feeling content compliance already covers this
## But feel free to make use of it if you think it is necessary
class CoverPageService:
    """
    Service for generating structure compliance context and LLM output using ChromaDB and an LLM.
    """
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.chroma_service = ChromaService(embedding_api_url, None)
        #self.llm = KontratarLLM(api_url=llm_api_url, api_key=None)
        self.llm = ChatOllama(model="gemma3:27b", temperature=0.0, base_url=llm_api_url)

    async def get_cover_page_context(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        max_chunks: int = 3,
    ) -> List[str]:
        """
        Query ChromaDB for relevant structure compliance context chunks.
        Cleans up newlines and tabs in the returned chunks.
        """

        chroma_query = '''
            Return the specific information that should be seen on the cover page.
        '''
        
        async for db in get_kontratar_db():
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, chroma_query, n_results=max_chunks
            )
            # Clean up newlines and tabs
            requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            break
        
        return requirements_context

    async def generate_cover_page(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
    ) -> Dict[str, Any]:
        """
        Generate structure compliance output using ChromaDB and LLM.
        Returns a dict with 'content' (LLM output).
        """
        # Get context
        cover_page_context = await self.get_cover_page_context(opportunity_id, tenant_id, source)

        print(f"Cover Page: {cover_page_context}")
        

        system_prompt = '''
            **Task:**
            Your task is to extract and highlight the neccessary fields that should be seen in teh cover page for an RFI or RFP.


            **Important:** 
            1. The response SHOULD be returned as JSON so ensure to generate accurate, parsebale and valid JSON.
            2. For EACH field specified in the JSON schema, RETUEN either true or false.
            3. IF ANY OF the fields were not explicitly mentioned, return false for ALL fields.
            4. You will be given a JSON schema to comply to, ensure to follow it strictly.
        '''
        user_prompt = f'''
            <context>
                {cover_page_context}
            </context>

            USE ONLY information found on context to build the response.

            Use the JSON schema below:
            {{ 
                "cover_page_fields": {{ 
                    "duns_number": boolean, 
                    "cage_code": boolean,
                    "sam_entity_id": boolean,
                    "uei_number": boolean
                }} 
            }}
        '''
        messages = [
            ("system", system_prompt),
            ("human", user_prompt)
        ]
        result = self.llm.invoke(messages)
        
        return str(result.content)
