from langchain_ollama import ChatOllama
from database import get_kontratar_db
from services.chroma.chroma_service import ChromaService
import logging

logger = logging.getLogger(__name__)

class CoverLetterService:
    def __init__(self, tenant_id: str, source: str, opportunity_id: str, tenant_metadata: str, opportunity_metadata: str):
        self.tenant_id = tenant_id
        self.source = source
        self.opportunity_id = opportunity_id
        self.tenant_metadata = tenant_metadata
        self.opportunity_metadata = opportunity_metadata

        self.model = "gemma3:27b"
        self.base_url = "http://ai.kontratar.com:11434"
        self.embedding_url = "http://ai.kontratar.com:5000"
        self.max_tokens = 2048
        self.chroma_service = ChromaService(self.embedding_url)
        self.llm = ChatOllama(
            model = self.model, 
            base_url=self.base_url,
        )

    '''
    async def get_cover_letter_context(self) -> str:
        max_chunks = 1
        opportunity_collection = f"{self.tenant_id}_{self.opportunity_id}" \
        if self.source == "custom" else self.opportunity_id

        query = """
        What is the information expected to be seen in the cover letter
        """

        async for db in get_kontratar_db():
            context = await self.chroma_service.get_relevant_chunks(db, opportunity_collection, query, max_chunks)
            context_str = "\n".join(context)
            break

        return context_str
    '''
    
    async def generate_cover_letter(self, volume_name: str):
        #context = await self.get_cover_letter_context()

        print(f"Tenant Metadata: {self.tenant_metadata}")
        print(f"Opportunity metadata: {self.opportunity_metadata}")

        system_prompt = """
            You are an expert proposal writer. 
            Your task is to generate a compelling, professional, and concise cover letter for an RFP (Request for Proposal) response. 
            Return only the cover letter text, with no explanations, headers, or additional commentary before or after the letter.

            DO NOT add placeholders in your generation. 
            IF the RFP requirements are not relevant to cover letter, DO NOT use it.
        """

        user_prompt = f"""
        Write a strong cover letter for {volume_name} of an RFP.
        Return ONLY the cover letter, do not generate anything before or after it.
        The company information and RFP information have been given below

        <company-information>
            {self.tenant_metadata}
        </company-information>

        <rfp-information>
            {self.opportunity_metadata}
        </rfp-information>
        """

        messages = [
            ("system", system_prompt),
            ("human", user_prompt),
        ]
        response = self.llm.invoke(messages)
        cover_letter = response.content if hasattr(response, "content") else response
        return cover_letter


