import asyncio
import j<PERSON>
from typing import Any, Dict, List, Optional
from services.chroma.chroma_service import ChromaService
from utils.llm import KontratarLLM
from database import get_kontratar_db
from langchain_ollama import ChatOllama

class FormattingRequirementsService:
    """
    Service for generating formatting requirements context and LLM output using ChromaDB and an LLM.
    """
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.chroma_service = ChromaService(embedding_api_url, None)
        #self.llm = KontratarLLM(api_url=llm_api_url, api_key=None)
        self.llm = ChatOllama(model="gemma3:27b", temperature=0.0, base_url=llm_api_url)

    async def generate_formatting_requirements(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        max_tokens: int = 1024,
    ) -> Dict[str, Any]:
        """
        Generate formatting requirements output using ChromaDB and LLM.
        Returns a dict with 'content' (parsed JSON) and 'context' (list of cleaned chunks).
        Raises an exception if a valid JSON is not produced after 3 attempts.
        """

        chroma_query = '''
            Return excerpts from government solicitation documents that specify formatting requirements including:
            - Font size (header, footer, body)
            - Font type (e.g., Arial, Times New Roman)
            - Page limits (total or per section)
            - Margin sizes (top, bottom, left, right)
            - Line spacing (single, 1.5, double)
        '''
        
        async for db in get_kontratar_db():
            max_chunks = 5
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, chroma_query, n_results=max_chunks
            )
            requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            context_str = "\n".join(requirements_context)
            break
        
        system_prompt = '''
        Role: Government Solicitation Formatting Expert
        Task: Generate a JSON response with formatting requirements.

        Rules:
        1. **Output Format** :
            {{
                "font_type": string,
                "font_size_body": number,
                "font_size_header": number,
                "font_size_footer": number,
                "margin": number,
                "line_spacing": number,
                "page_limit": number
            }}

            - Use the information found in <context> to generate the formatting requirements in this structure

            Example Response:
            {{"document_guidelines":
                {{
                    "font_type": "Times-Roman",
                    "font_size_body": 12,
                    "font_size_header": 14,
                    "font_size_footer": 10,
                    "margin": 15,
                    "line_spacing": 1.5,
                    "page_limit": 20
                }}
            }}
        '''
        user_prompt = '''
        Generate formatting requirements for this solicitation in JSON format using the following context:

        <context>
            {context_str}
        </context>

        **Contraints**:

        - DO NOT add any text before or after the JSON requirements, just generate the requirement in the correct structure
        - For the "font_type" field, limit it to either "Times-Roman" for Times New Roman font or "Helvetica" for Arial font
        - Return only the document guidelines

        **Defaults**:
        - "font_type": "Times-Roman"
        - "font_size_body": 12
        - "font_size_header": 14
        - "font_size_footer": 10
        - "line_spacing": 1.5
        - "margin": 50
        - "page_limit": 20
        '''.format(context_str=context_str)
        n_llm_attempts = 3

        for attempt in range(n_llm_attempts):
            try:
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
                content = self.llm.invoke(messages)
                break
            except Exception as e:
                if attempt == n_llm_attempts - 1:
                    raise RuntimeError(f"Failed to generate valid formatting requirements after {n_llm_attempts} attempts: {e}")
                await asyncio.sleep(1)
        
        return {"content": content, "context": requirements_context}

    @staticmethod
    def is_valid_formatting_requirements(data: dict) -> bool:
        try:
            # Check if all required keys are present
            required_keys = [
                "font_type",
                "font_size_body",
                "font_size_header",
                "font_size_footer",
                "margin",
                "line_spacing",
                "page_limit",
            ]
            for key in required_keys:
                if key not in data:
                    return False
            # Validate font_type value
            if data["font_type"] not in ("Times-Roman", "Helvetica"):
                return False
            # Validate numeric values are within reasonable ranges
            if not (isinstance(data["font_size_body"], (int, float)) and data["font_size_body"] > 0):
                return False
            if not (isinstance(data["font_size_header"], (int, float)) and data["font_size_header"] > 0):
                return False
            if not (isinstance(data["font_size_footer"], (int, float)) and data["font_size_footer"] > 0):
                return False
            if not (isinstance(data["margin"], (int, float)) and data["margin"] > 0):
                return False
            if not (isinstance(data["line_spacing"], (int, float)) and data["line_spacing"] > 0):
                return False
            if not (isinstance(data["page_limit"], (int, float)) and data["page_limit"] > 0):
                return False
            return True
        except Exception:
            return False 