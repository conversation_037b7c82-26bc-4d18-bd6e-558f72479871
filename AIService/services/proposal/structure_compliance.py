import asyncio
from typing import Any, Dict, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from services.chroma.chroma_service import ChromaService
from utils.llm import KontratarLLM
from database import get_kontratar_db
from services.llm.llm_factory import get_llm

## I have a feeling content compliance already covers this
## But feel free to make use of it if you think it is necessary
class StructureComplianceService:
    """
    Service for generating structure compliance context and LLM output using ChromaDB and an LLM.
    """
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.chroma_service = ChromaService(embedding_api_url, None)
        # Use the LLM factory to get the configured LLM
        self.llm = get_llm(temperature=0.0, base_url=llm_api_url)

    async def get_structure_compliance_context(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        max_chunks: int = 10,
    ) -> List[str]:
        """
        Query ChromaDB for relevant structure compliance context chunks.
        Cleans up newlines and tabs in the returned chunks.
        """

        chroma_query = '''
            Show the structure of the content expected to be seen in each volume or in the RFI, the approaches
            to be shown and page limits.
        '''
        
        async for db in get_kontratar_db():
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, chroma_query, n_results=max_chunks
            )
            # Clean up newlines and tabs
            requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            break
        
        return requirements_context

    async def generate_structure_compliance(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        max_tokens: int = 2048,
    ) -> Dict[str, Any]:
        """
        Generate structure compliance output using ChromaDB and LLM.
        Returns a dict with 'content' (LLM output).
        """
        # Get context
        chroma_query = '''
            Show the structure of the content expected to be seen in each volume or in the RFI, the approaches
            to be shown and page limits.
        '''

        async for db in get_kontratar_db():
            max_chunks = 5
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, chroma_query, n_results=max_chunks
            )
            # Clean up newlines and tabs
            requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            compliance_context = "\n".join(requirements_context)
            break
        

        system_prompt = '''
            **Task:**
            Your task is give me the structure of the content expected to be seen in RFI/RFP response.
            Related information to this has been fetched for you and supplied in <context>.
            For an RFP, there are different volumes (Volume I - V). The RFP MAY NOT ASK for all volumes,
            so FOR EVERY volume it asks for show how content should be structured for it
            For an RFI, there are not different volumes so show just the structure of the content.

            **Important:**
            1. IF information in <context> makes mention of different Volumes, ensure to list out each Volume with the structure
            of how content should be, otherwise just show the structure without seprating it into different Volumes
            2. The response SHOULD be returned as JSON so ensure to generate accurate, parsebale and valid JSON.
            3. You will be given a JSON schema to comply to, ensure to follow it strictly.
        '''
        user_prompt = '''
            <context>
                {compliance_context}
            </context>

            USE ONLY information found on context to build the response.

            Use the JSON schema below:
            {{ "structure": [{{ "volume_title": "string", "content": [{{ "section_name": "string", "page_limit": number }}] }}] }}

            - "structure" IS AN ARRAY for the list of volumes to be shown, IF it is an RFP,
            there will be multiple items in this array representing each volume, if it is an RFI there should be ONLY 1
            item in this array.
            - "volume_title" WILL be the name of the volume for this item. For an RFP it can either be Volume I,
            Volume II, Volume III, Volume IV or Volume V. For an RFI it MUST BE "RFI"
            - "content" IS AN ARRAY for the items EXPECTED to BE seen in EACH volume
            - "section_name" is the title of the content expected to be seen in the associated volume, to also include any naming conventions like Tabs, Sections, etc.
            - "page_limit" the number of pages expected to be seen for the item with name "section_name", if not specified, use 2 as default
        '''.format(compliance_context=compliance_context)
        # Try LLM up to n_llm_attempts
        content = None
        n_llm_attempts = 3
        for attempt in range(n_llm_attempts):
            try:
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
                content = self.llm.invoke(messages)
                break
            except Exception as e:
                if attempt == n_llm_attempts - 1:
                    raise RuntimeError(f"LLM failed after {n_llm_attempts} attempts: {e}")
                await asyncio.sleep(1)
        return {"content": content.content}
