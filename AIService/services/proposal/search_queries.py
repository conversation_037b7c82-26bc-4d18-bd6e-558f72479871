import re
import json
import logging
from typing import List, Dict, Optional
import asyncio

from langchain_ollama import Chat<PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from controllers.kontratar.ebuy_opps_controller import EB<PERSON>YOppsController
from controllers.kontratar.opps_table_controller import OppsTableController

logger = logging.getLogger("ExtractKeywords")

class ExtractKeywords:
    def __init__(self, llm_service=None):
        self.llm_service = llm_service or ChatOllama(model="gemma3:27b", base_url="http://ai.kontratar.com:11434")

    async def get_queries(self, opportunity_text: str, get_related_queries: bool) -> List[str]:
        system_prompt = """
            Role: Search Query Generation Expert
            Task: Generate search queries using related topics from the given information.

            **Rules:**
            1. You will be given information about an opportunity to gather more information from the internet to generate an RFI/RFP.
            2. You will be given information found in <information>
            3. The search queries WILL be used to gather related information similar to the opportunity so ENSURE your search queries will help to gather meaningful information
            4. EACH generated query should be long and VERY specific in order to get meaningful search results
            5. You are free to include NICHE KEYWORDS from the opportunity in order to get more specific queries
            6. You MUST generate AT LEAST 5 search queries

            **Important**:
            The search queries SHOULD be returned as JSON so ensure to generate accurate, parsebale and valid JSON.
            You will be given a JSON schema to comply to, ensure to follow it strictly.
        """

        user_prompt = f"""
            <information>
            {opportunity_text}
            </information>

            Use the JSON schema below:
            {{ "searchQueries": [] }}

            - "searchQueries" is an ARRAY of AT LEAST 5 search queries
            - EACH search query MUST be a STRING

            Important:
            ONLY return A COMPLIANT JSON, DO NOT return any other thing.
        """
        generated_queries = await self.generate_json_queries(system_prompt, user_prompt, 250)
        if not get_related_queries:
            return generated_queries
        related_queries = await self.get_related_queries(generated_queries)
        return generated_queries + related_queries

    async def get_queries_for_large_document(self, opportunity_text: str, get_related_queries: bool) -> List[str]:
        system_prompt = """
        Role: Search Query Generation Expert
        Task: Generate search queries using related topics from the given information.

        **Rules:**
        1. You will be given a portion of information about an opportunity to gather more information from the internet to generate an RFI/RFP.
        2. You will be given information found in <information>
        3. The search queries WILL be used to gather related information similar to the opportunity so ENSURE your search queries will help to gather meaningful information
        4. EACH generated query should be long and VERY specific in order to get meaningful search results
        5. You are free to include NICHE KEYWORDS from the opportunity in order to get more specific queries
        6. You MUST generate AT LEAST 3 search queries for this portion

        **Important**:
        The search queries SHOULD be returned as JSON so ensure to generate accurate, parsebale and valid JSON.
        You will be given a JSON schema to comply to, ensure to follow it strictly.
        """
        user_prompt = f"""
        <information>
        {opportunity_text}
        </information>

        Use the JSON schema below:
        {{ "searchQueries": [] }}

        - "searchQueries" is an ARRAY of AT LEAST 3 search queries
        - EACH search query MUST be a STRING

        Important:
        ONLY return A COMPLIANT JSON, DO NOT return any other thing.
        """
        combined_result = await self.llm_invoke(system_prompt, user_prompt)
        all_queries = []
        try:
            json_result = self.extract_json_content(combined_result)
            data = json.loads(json_result)
            all_queries = data.get("searchQueries", [])
        except Exception as e:
            logger.warning(f"Error parsing combined result: {e}")
            for line in combined_result.split("\n"):
                line = line.strip()
                if line and not line.startswith("{") and not line.startswith("}") and not line.startswith("["):
                    all_queries.append(line)
        unique_queries = list(all_queries)
        logger.info(f"Generated {len(unique_queries)} unique queries from large document processing")
        if not get_related_queries:
            return unique_queries
        related_queries = await self.get_related_queries(unique_queries)
        return unique_queries + related_queries

    async def get_related_queries(self, initial_search_queries: List[str]) -> List[str]:
        listed_search_queries = "\n".join(f"{i+1}. {q}" for i, q in enumerate(initial_search_queries))
        system_prompt = """
            Role: Search Query Generation Expert
            Task: Generate related search queries using related topics from a list of initial search queries given.

            **Rules:**
            1. You will be given a list of initial search queries for gather more information from the internet to generate an RFI/RFP.
            2. You will be given a list of initial search queries found in <initial-queries>
            3. The related search queries WILL be used to gather related information similar to the 
            initial queries so ENSURE your search queries will help to gather meaningful information
            4. EACH generated query should be long and VERY specific in order to get meaningful search results
            5. You are free to include NICHE KEYWORDS from the initial queries in order to get more specific 
            related queries
            6. You MUST generate AT LEAST 10 related queries

            **Important**:
            The search queries SHOULD be returned as JSON so ensure to generate accurate, parsebale and valid JSON.
            You will be given a JSON schema to comply to, ensure to follow it strictly.
        """
        user_prompt = f"""
            <initial-queries>
            {listed_search_queries}
            </initial-queries>

            Use the JSON schema below:
            {{ "searchQueries": [] }}

            - "searchQueries" is an ARRAY of AT LEAST 5 search queries
            - EACH search query MUST be a STRING

            Important:
            ONLY return A COMPLIANT JSON, DO NOT return any other thing.
        """
        return await self.generate_json_queries(system_prompt, user_prompt, 250)

    async def generate_json_queries(self, system_prompt: str, user_prompt: str, tokens: int) -> List[str]:
        max_attempts = 3
        attempt = 0
        search_queries = []
        while attempt < max_attempts:
            raw_keywords = await self.llm_invoke(system_prompt, user_prompt)
            json_result = self.extract_json_content(raw_keywords)
            try:
                data = json.loads(json_result)
                search_queries = data.get("searchQueries", [])
                if search_queries:
                    return search_queries
            except Exception as e:
                logger.info(f"Error parsing search queries JSON (attempt {attempt+1}): {e}")
            attempt += 1
        return search_queries

    def extract_json_content(self, text: str) -> str:
        if not text:
            return ""
        start = text.find('{')
        end = text.rfind('}')
        if start != -1 and end != -1 and end > start:
            return text[start:end+1].strip()
        return text.strip()

    async def get_existing_keywords(self, db: AsyncSession, tenant_id: str, opportunity_id: str, source: str) -> Optional[str]:
        source = source.lower()
        if source == "sam":
            return await OppsTableController.get_keywords_by_notice_id(db, opportunity_id)
        elif source == "ebuy":
            return await EBUYOppsController.get_keywords_by_rfq_id(db, opportunity_id)
        elif source == "custom":
            return await CustomOpportunitiesController.get_keywords_by_opportunity_id(db, opportunity_id)
        else:
            raise ValueError("Invalid Opportunity Source")

    async def set_keywords(self, db: AsyncSession, tenant_id: str, opportunity_id: str, source: str, keywords: str):
        source = source.lower()
        if source == "sam":
            await OppsTableController.update_by_notice_id(db, opportunity_id, {"keywords": keywords})
        elif source == "ebuy":
            await EBUYOppsController.update_by_rfq_id(db, opportunity_id, {"keywords": keywords})
        elif source == "custom":
            await CustomOpportunitiesController.update_by_opportunity_id(db, opportunity_id, {"keywords": keywords})
        else:
            raise ValueError("Invalid Opportunity Source")

    async def add_keywords_to_opportunity_table(self, db: AsyncSession, tenant_id: str, opp_id: str, opp_source: str, keywords_text: str) -> bool:
        try:
            opp_source = opp_source.lower()
            if opp_source == "sam":
                await OppsTableController.update_by_notice_id(db, opp_id, {"keywords": keywords_text})
            elif opp_source == "ebuy":
                await EBUYOppsController.update_by_rfq_id(db, opp_id, {"keywords": keywords_text})
            elif opp_source == "custom":
                await CustomOpportunitiesController.update_by_opportunity_id(db, opp_id, {"keywords": keywords_text})
            else:
                raise ValueError("Invalid Opportunity Source")
        except Exception as e:
            logger.error(str(e))
            return False
        return True

    async def llm_invoke(self, system_prompt: str, user_prompt: str) -> str:
        messages = [
            ("system", system_prompt.strip()),
            ("human", user_prompt.strip()),
        ]
        response = await asyncio.get_event_loop().run_in_executor(
            None, lambda: self.llm_service.invoke(messages)
        )
        return getattr(response, "content", str(response))