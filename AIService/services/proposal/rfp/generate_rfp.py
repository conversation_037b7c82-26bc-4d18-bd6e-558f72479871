import json
from typing import Any, Dict, List

from controllers.customer.system_prompt_parameters_controller import SystemPromptParametersController
from controllers.customer.proposals_format_queue_controller import ProposalsFormatQueueController
from controllers.customer.proposals_in_review_controller import ProposalsInReviewController
from models.customer_models import ProposalsFormatQueue
from services.proposal.proposal_decoding_service import ProposalDecodingService
from services.queue_service.proposal_criticism_queue_service import ProposalCriticismQueueService
from database import get_customer_db, get_kontratar_db

from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from controllers.customer.tenant_controller import TenantController
from controllers.kontratar.ebuy_opps_controller import EBUYOppsController
from controllers.kontratar.opps_table_controller import OppsTableController

from services.proposal.content_compliance import ContentComplianceService
from services.proposal.utilities import ProposalUtilities
from services.proposal.outline import ProposalOutlineService
from models.customer_models import ProposalOutlineQueue
from services.proposal.technical_requirements import TechnicalRequirementsService

from services.proposal.rfp.rfp_section import RFPSection
from loguru import logger

NUMBER_KEY = "number"
CONTENT_KEY = "content"


class RFPGenerationService:
    def __init__(self):
        self.technical_requirememnts = TechnicalRequirementsService()
        self.content_compliance_service = ContentComplianceService()
        self.outline_service = ProposalOutlineService()

        self.sam_service = OppsTableController()
        self.ebuy_service = EBUYOppsController()
        self.custom_service = CustomOpportunitiesController()

        self.proposal_decoding_service = ProposalDecodingService()
        self.proposal_in_review = ProposalsInReviewController()
        self.proposal_format_queue = ProposalsFormatQueueController()

        self.personality_controller = SystemPromptParametersController()

    async def get_personality(self, personality_id: int)-> str:
        async for db in get_customer_db():
            record = await self.personality_controller.get_by_id(db, personality_id)

        if record is None:
            raise ValueError("Personality is None or was passed an invalid value")

        personality = record.as_dict()
        

        return f"""
        **Personality:**
        Name: {personality.get("name")}

        Description: {personality.get("description")}

        Personality Writing style: {personality.get("style_guidance")}

        Tone: {personality.get("tone_settings")}

        """

    async def get_opportunity(self, opportunity_id: str, tenant_id: str, source: str):
        logger.info(f"get_opportunity called with opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")
        record = None
        if source == "sam":
            logger.info(f"Searching SAM for notice_id={opportunity_id}")
            async for db in get_kontratar_db():
                record = await self.sam_service.get_by_notice_id(db, opportunity_id)
                logger.info(f"SAM search result: {record}")
                break
        elif source == "ebuy":
            logger.info(f"Searching EBUY for rfq_id={opportunity_id}")
            async for db in get_kontratar_db():
                record = await self.ebuy_service.get_by_rfq_id(db, opportunity_id)
                logger.info(f"EBUY search result: {record}")
                break
        elif source == "custom":
            logger.info(f"Searching CUSTOM for opportunity_id={opportunity_id}")
            async for db in get_customer_db():
                record = await self.custom_service.get_by_opportunity_id(db, opportunity_id)
                logger.info(f"CUSTOM search result: {record}")
                break
        else:
            logger.error(f"Invalid source type: {source}")
            raise ValueError("Invalid source type")

        if record is None:
            logger.error(f"Error getting opportunity metadata for id={opportunity_id}, source={source}")
            raise ValueError("Error getting opportunity metadata")

        logger.info(f"Returning opportunity record: {record}")
        return record

    async def update_generated_field(self, source: str, opportunity_id: str, updated_fields: dict):
        logger.info(f"update_generated_field called with source={source}, opportunity_id={opportunity_id}, updated_fields={updated_fields}")
        """
        Update the record for the given source and opportunity_id with updated_fields.
        Returns the updated record.
        """
        record = None
        if source == "sam":
            logger.info(f"Updating SAM record for notice_id={opportunity_id} with fields={updated_fields}")
            async for db in get_kontratar_db():
                record = await self.sam_service.update_by_notice_id(db, opportunity_id, updated_fields)
                logger.info(f"SAM update result: {record}")
                break
        elif source == "ebuy":
            logger.info(f"Updating EBUY record for rfq_id={opportunity_id} with fields={updated_fields}")
            async for db in get_kontratar_db():
                record = await self.ebuy_service.update_by_rfq_id(db, opportunity_id, updated_fields)
                logger.info(f"EBUY update result: {record}")
                break
        elif source == "custom":
            logger.info(f"Updating CUSTOM record for opportunity_id={opportunity_id} with fields={updated_fields}")
            async for db in get_customer_db():
                record = await self.custom_service.update_by_opportunity_id(db, opportunity_id, updated_fields)
                logger.info(f"CUSTOM update result: {record}")
                break
        else:
            logger.error(f"Invalid source type: {source}")
            raise ValueError("Invalid source type")
        return record

    async def get_technical_requirements(self, opportunity_id: str, tenant_id: str, source: str):
        logger.info(f"get_technical_requirements called with opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")
        ## Get Record
        record = await self.get_opportunity(opportunity_id, tenant_id, source)

        if record and getattr(record, "requirement_text", None) not in (None, ""):
            logger.info(f"Found existing technical requirements: {record.requirement_text}")
            return str(record.requirement_text)
        else:
            logger.info("No existing technical requirements found, generating new requirements.")
            generated_req = await self.technical_requirememnts.generate_technical_requirements(
                opportunity_id, tenant_id, source
            )
            logger.info(f"Generated technical requirements: {generated_req}")

            updated_fields = {
                "requirement_text": json.dumps(generated_req),
            }
            await self.update_generated_field(source, opportunity_id, updated_fields)
            return str(generated_req)

    async def get_content_compliance(self, opportunity_id: str, tenant_id: str, source: str):
        logger.info(f"get_content_compliance called with opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")
        ## Get Record
        record = await self.get_opportunity(opportunity_id, tenant_id, source)

        is_rfp = True

        if record and getattr(record, "content_compliance", None) not in (None, ""):
            logger.info(f"Found existing content compliance: {record.content_compliance}")
            return str(record.content_compliance)
        else:
            logger.info("No existing content compliance found, generating new compliance.")
            generated_compliance = await self.content_compliance_service.generate_content_compliance(
                opportunity_id, tenant_id, source, is_rfp
            )
            #logger.info(f"Generated content compliance: {generated_compliance}")
            # Save generated_compliance to the record here if needed
            updated_fields = {
                "content_compliance": generated_compliance["content"],
            }
            await self.update_generated_field(source, opportunity_id, updated_fields)
            return str(generated_compliance["content"])

    async def get_tenant(self, tenant_id: str):
        logger.info(f"get_tenant called with tenant_id={tenant_id}")
        tenant_service = TenantController()

        async for db in get_customer_db():
            tenant = await tenant_service.get_by_tenant_id(db, tenant_id)
            logger.info(f"Tenant search result: {tenant}")
            break

        if tenant is None:
            logger.warning(f"No tenant found for tenant_id={tenant_id}")
            return None
        
        logger.info(f"Returning tenant as dict: {tenant}")
        return tenant

    async def _check_outline_queue_status(self, opps_id: str, tenant_id: str) -> str:
        """Check the status of outline in proposal_outline_queue"""
        try:
            from database import get_kontratar_db
            from sqlalchemy import select
            async for db in get_kontratar_db():
                query = select(ProposalOutlineQueue).where(
                    ProposalOutlineQueue.opps_id == opps_id,
                    ProposalOutlineQueue.first_request == True
                ).order_by(ProposalOutlineQueue.created_date.desc())
                result = await db.execute(query)
                queue_item = result.scalar_one_or_none()
                if queue_item:
                    return queue_item.status
                break
            return "NOT_FOUND"
        except Exception as e:
            logger.error(f"Error checking outline queue status for {opps_id}: {e}")
            return "ERROR"

    async def _add_to_outline_queue(self, opps_id: str, tenant_id: str, source: str):
        """Add opportunity to proposal_outline_queue"""
        try:
            from database import get_kontratar_db
            from sqlalchemy import select
            async for db in get_kontratar_db():
                # Check if already exists to avoid duplicates
                existing_query = select(ProposalOutlineQueue).where(
                    ProposalOutlineQueue.opps_id == opps_id,
                    ProposalOutlineQueue.tenant_id == tenant_id,
                    ProposalOutlineQueue.status.in_(["NEW", "PROCESSING"])
                )
                existing_result = await db.execute(existing_query)
                existing_item = existing_result.scalar_one_or_none()

                if not existing_item:
                    new_queue_item = ProposalOutlineQueue(
                        opps_id=opps_id,
                        tenant_id='SYSTEM',
                        status="NEW",
                        outline_type=source.upper(),
                        first_request=True
                    )
                    db.add(new_queue_item)
                    await db.commit()
                    logger.info(f"Added {opps_id} to proposal_outline_queue")
                else:
                    logger.info(f"Outline queue item already exists for {opps_id}")
                break
        except Exception as e:
            logger.error(f"Error adding {opps_id} to outline queue: {e}")

    async def _update_outline_queue_status(self, opps_id: str, tenant_id: str, new_status: str):
        """Update the status of an outline queue item"""
        try:
            from database import get_kontratar_db
            from sqlalchemy import update
            from datetime import datetime
            async for db in get_kontratar_db():
                update_stmt = update(ProposalOutlineQueue).where(
                    ProposalOutlineQueue.opps_id == opps_id
                ).values(
                    status=new_status,
                    created_date=datetime.utcnow(),
                    error_message=None if new_status != "FAILED" else ProposalOutlineQueue.error_message
                )
                await db.execute(update_stmt)
                await db.commit()
                logger.info(f"Updated outline queue status for {opps_id} to {new_status}")
                break
        except Exception as e:
            logger.error(f"Error updating outline queue status for {opps_id}: {e}")

    async def _update_outline_queue_timestamp(self, opps_id: str, tenant_id: str):
        """Update the timestamp of an outline queue item to prioritize it"""
        try:
            from database import get_kontratar_db
            from sqlalchemy import update
            from datetime import datetime
            async for db in get_kontratar_db():
                update_stmt = update(ProposalOutlineQueue).where(
                    ProposalOutlineQueue.opps_id == opps_id
                ).values(created_date=datetime.utcnow())
                await db.execute(update_stmt)
                await db.commit()
                logger.info(f"Updated outline queue timestamp for {opps_id} to prioritize processing")
                break
        except Exception as e:
            logger.error(f"Error updating outline queue timestamp for {opps_id}: {e}")

    async def _wait_for_outline_or_queue(self, opps_id: str, tenant_id: str, source: str, volume_number: int) -> tuple[bool, str]:
        """
        Check if outline is ready or needs to be queued for a specific volume.

        Returns:
            (is_ready: bool, message: str)
        """
        try:
            # Check outline queue status
            queue_status = await self._check_outline_queue_status(opps_id, tenant_id)

            if queue_status == "COMPLETED":
                logger.info(f"Outline generation completed for {opps_id} volume {volume_number}")
                return True, "Outline ready"
            elif queue_status == "PROCESSING" or "CLAIMED":
                logger.info(f"Outline generation in progress for {opps_id} volume {volume_number}")
                return False, "Outline generation in progress, please try again later"
            elif queue_status == "FAILED":
                logger.info(f"Outline generation failed for {opps_id} volume {volume_number}, updating to NEW status")
                await self._update_outline_queue_status(opps_id, tenant_id, "NEW")
                return False, "Outline generation failed, requeued for retry"
            elif queue_status == "NEW":
                logger.info(f"Outline generation queued but not started for {opps_id} volume {volume_number}, updating timestamp")
                await self._update_outline_queue_timestamp(opps_id, tenant_id)
                return False, "Outline generation queued, please try again later"
            else:
                # Not in queue - add to queue
                logger.info(f"Adding {opps_id} to outline queue for volume {volume_number}")
                await self._add_to_outline_queue(opps_id, tenant_id, source)
                return False, "Outline generation queued, please try again later"

        except Exception as e:
            logger.error(f"Error checking outline availability for {opps_id} volume {volume_number}: {e}")
            return False, f"Error checking outline availability: {str(e)}"

    async def get_all_table_of_contents(self, opportunity_id: str, tenant_id: str, source: str) -> List[List[Dict[str, Any]] | None]:
        logger.info(f"get_all_table_of_contents called with opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")
        record = await self.get_opportunity(opportunity_id, tenant_id, source)
        table_of_contents = []

        toc_1 = record.toc_text
        logger.info(f"TOC 1: {toc_1}")
        if toc_1 is not None and str(toc_1).strip():
            try:
                table_of_contents.append(json.loads(str(toc_1)))
            except (json.JSONDecodeError, ValueError):
                logger.warning(f"Invalid JSON in toc_1: {toc_1}")
                table_of_contents.append(None)
        else:
            table_of_contents.append(None)

        for i in range(2, 6):
            toc_attr = getattr(record, f"toc_text_{i}", None)
            logger.info(f"TOC {i}: {toc_attr}")
            if toc_attr is not None and str(toc_attr).strip():
                try:
                    table_of_contents.append(json.loads(str(toc_attr)))
                except (json.JSONDecodeError, ValueError):
                    logger.warning(f"Invalid JSON in toc_text_{i}: {toc_attr}")
                    table_of_contents.append(None)
            else:
                table_of_contents.append(None)

        #logger.info(f"All table_of_contents: {table_of_contents}")
        return table_of_contents

    async def get_all_outlines(self, opportunity_id: str, tenant_id: str, source: str) -> List[List[Dict[str, Any]] | None]:
        logger.info(f"get_all_outlines called with opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")
        record = await self.get_opportunity(opportunity_id, tenant_id, source)
        outlines = []

        for i in range(1, 6):
            outline_attr = getattr(record, f"proposal_outline_{i}", None)
            logger.info(f"Outline {i}: {outline_attr}")
            is_valid = outline_attr is not None and outline_attr != ""
            outlines.append(json.loads(str(outline_attr)).get('outlines', []) if is_valid else None)
        
        #logger.info(f"All outlines: {outlines}")
        return outlines

    async def generate_table_of_contents(self, is_rfp: bool, volume_number: int, content_compliance: str, opportunity_id: str, tenant_id: str, source: str) -> List[Dict[str, Any]]:
        logger.info(f"generate_table_of_contents called with is_rfp={is_rfp}, volume_number={volume_number}, content_compliance={content_compliance}, opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")
        volume_information = ProposalUtilities.get_volume_information(volume_number)

        logger.info(f"Volume Information: {volume_information}")

        table_of_contents = await self.outline_service.generate_table_of_contents(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            content_compliance=content_compliance,
            is_rfp=is_rfp,
            volume_information=volume_information
        )
        
        logger.info(f"Generated table_of_contents raw: {table_of_contents}")
        result = str(table_of_contents.get("content"))

        if not result or not result.strip():
            logger.warning("No table_of_contents content generated or content is empty.")
            return []

        result_json = ProposalUtilities.extract_json_from_brackets(result)
        if result_json is None:
            logger.warning(f"Failed to parse table_of_contents content: {result}")
            return []

        logger.info(f"Parsed table_of_contents: {result_json.get('table_of_contents')}")
        toc = result_json.get("table_of_contents")

        if toc is None:
            return []

        return toc

    async def generate_outline(self, volume_number: int, table_of_contents: List[Dict[str, Any]], opportunity_id: str, tenant_id: str, source: str) -> List[Dict[str, Any]]:
        logger.info(f"generate_outline called with volume_number={volume_number}, table_of_contents={table_of_contents}, opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")

        # Check if outline is ready using queue system
        outline_ready, message = await self._wait_for_outline_or_queue(
            opportunity_id, tenant_id, source, volume_number
        )
        if not outline_ready:
            logger.warning(f"Cannot proceed with outline generation for {opportunity_id} volume {volume_number}: {message}")
            raise ValueError(f"Outline not ready: {message}")

        # If we reach here, outline should be ready, try to get it from DB
        record = await self.get_opportunity(opportunity_id, tenant_id, source)
        existing_outline = getattr(record, f"proposal_outline_{volume_number}", None)

        if existing_outline and existing_outline != "":
            # Outline exists, parse and return it
            try:
                parsed_outline = json.loads(str(existing_outline))
                outline: List[Dict[str, Any]] = parsed_outline.get("outlines", [])
                logger.info(f"Using existing outline for volume {volume_number}: {len(outline)} sections")
                return outline
            except json.JSONDecodeError:
                logger.error(f"Failed to parse existing outline for volume {volume_number}")
                raise ValueError(f"Outline data corrupted for volume {volume_number}")

        logger.error(f"Outline queue indicates ready but outline not found in database for {opportunity_id} volume {volume_number}")
        raise ValueError(f"Outline not found in database despite queue status being COMPLETED. This indicates a system error.")

    async def move_to_review(self, volume_number: int, version: int, volumes_data: List[Dict[str, Any]], tenant_id: str, opportunity_id: str, source: str, client_short_name: str, job_instruction: str):
        logger.info(f"move_to_review called with volume_number={volume_number}, tenant_id={tenant_id}, opportunity_id={opportunity_id}, source={source}, client_short_name={client_short_name}")
        """
        Loop over volumes data, convert to bytes, encrypt, and save to proposals in review table
        """
        
        # Get the proposal decoding service
        results = []
        
        async for db in get_customer_db():
            try:
                for section_number, volume_data in enumerate(volumes_data, 1):
                    if not volume_data:  # Only process non-empty volumes
                        logger.info(f"Skipping empty volume_data for section_number={section_number}")
                        continue

                    logger.info(f"Processing volume_data for section_number={section_number}: {volume_data}")

                    # Convert volume data to bytes
                    volume_content = json.dumps(volume_data).encode('utf-8')
                    
                    # Encrypt the volume data
                    encrypted_data = await self.proposal_decoding_service.encrypt_with_public_key(
                        db, tenant_id, volume_content
                    )

                    # Save to proposals in review table
                    review_record = await ProposalsInReviewController.add(
                        db=db,
                        client_short_name=client_short_name,
                        tenant_id=tenant_id,
                        section_number=str(section_number),
                        opps_id=opportunity_id,
                        volume_number=volume_number,
                        version=version,
                        opps_type=source.upper(),
                        proposal_data=encrypted_data,
                        job_instruction=job_instruction
                    )
                    
                    if review_record:
                        logger.info(f"Successfully moved volume {volume_number} section {section_number} to review for opportunity {opportunity_id}")
                        results.append(review_record)
                    else:
                        logger.error(f"Failed to save volume {volume_number} section {section_number} to review table")
                            
            except Exception as e:
                logger.error(f"Error moving volumes to review: {e}")
            finally:
                break
        
        logger.info(f"move_to_review results: {results}")
        return results

    async def move_all_volumes_to_review(self, all_volumes: List[List[Dict[str, Any]] | None], tenant_id: str, opportunity_id: str, source: str, client_short_name: str, job_instruction: str):
        logger.info(f"move_all_volumes_to_review called with tenant_id={tenant_id}, opportunity_id={opportunity_id}, source={source}, client_short_name={client_short_name}")
        """
        Move all proposal volumes to review
        """
        results = []

        async for db in get_customer_db():
            current_version = await ProposalsInReviewController.get_current_version(
                db, tenant_id, opportunity_id
            )

        next_version = current_version + 1 if current_version is not None else 1
        
        for volume_number, volume_data in enumerate(all_volumes, 1):
            if volume_data is None:
                logger.info(f"Skipping empty volume_data for volume_number={volume_number}")
                continue  # Only process non-empty volumes

            logger.info(f"Moving volume_number={volume_number} to review with data: {volume_data}")
            result = await self.move_to_review(
                volumes_data=volume_data, # Pass a single list for each volume
                tenant_id=tenant_id,
                opportunity_id=opportunity_id,
                source=source,
                client_short_name=client_short_name,
                job_instruction=job_instruction,
                volume_number=volume_number,
                version=next_version,
            )
            results.extend(result) # Extend results with the list of review records for this volume
        
        logger.info(f"move_all_volumes_to_review results: {results}")
        return results

    async def move_all_volumes_to_format(self, all_volumes: List[List[Dict[str, Any]] | None], tenant_id: str, opportunity_id: str, source: str, client_short_name: str, cover_page: int, format_type: int, job_submitted_by: str):
        logger.info(f"move_all_volumes_to_format called with tenant_id={tenant_id}, opportunity_id={opportunity_id}, source={source}, client_short_name={client_short_name}, cover_page={cover_page}, format_type={format_type}, job_submitted_by={job_submitted_by}")
        """
        Move all proposal volumes to review
        """
        volumes = []
        
        volumes.extend(volume_data if volume_data is not None else [] for volume_data in all_volumes)
        logger.info(f"All volumes to format: {volumes}")

        volume_content = json.dumps(volumes).encode('utf-8')
                    
        async for db in get_customer_db():
            # Encrypt the volume data
            encrypted_data = await self.proposal_decoding_service.encrypt_with_public_key(
                db, tenant_id, volume_content
            )
            current_version = await self.proposal_format_queue.get_current_version(db, tenant_id, opportunity_id)
            next_version = current_version + 1 if current_version is not None else 1
            
            await self.proposal_format_queue.add(
                db=db,
                client_short_name=client_short_name,
                tenant_id=tenant_id,
                opps_id=opportunity_id,
                version=next_version,
                format_type=format_type,  # You may want to set this dynamically
                proposal_data=encrypted_data,
                job_submitted_by=job_submitted_by,  # Or another field if available
                is_rfp=True,
                cover_page=cover_page,  # Set as needed
                opp_source=source.upper()
            )
            logger.info(f"Moved all volumes to format for opportunity_id={opportunity_id}, version={next_version}")
                
    
    
    async def generate_rfp(self, job_instruction: str, job_submitted_by: str):
        logger.info(f"generate_rfp called with job_instruction={job_instruction}, job_submitted_by={job_submitted_by}")
        job = json.loads(job_instruction)

        ## Get necessary fields from job instruction
        opportunity_id = job.get("opportunityId", None)
        tenant_id = job.get("tenantId", None)
        client_short_name = job.get("clientShortName", None)
        opportunity_type = job.get("opportunityType", None)
        source_documents = job.get("sourceDocuments", [])
        profile_id = job.get("profileId", None)
        set_for_review = job.get("setForReview", True)
        cover_page = job.get("coverPage", None)
        export_type = job.get("exportType", None)
        personality_id = job.get("aiPersonalityId", None)

        required_fields = {
            "opportunityId": opportunity_id,
            "tenantId": tenant_id,
            "clientShortName": client_short_name,
            "opportunityType": opportunity_type,
            "profileId": profile_id,
            "aiPersonalityId": personality_id,
        }
        missing_fields = [k for k, v in required_fields.items() if v is None]
        if missing_fields:
            raise ValueError(f"The following required fields are None: {', '.join(missing_fields)}")

        is_rfp = True
        requested_volumes = job.get("generatedVolumes", [1, 2, 3, 4, 5])

        logger.info(f"Job fields: opportunity_id={opportunity_id}, tenant_id={tenant_id}, client_short_name={client_short_name}, opportunity_type={opportunity_type}, profile_id={profile_id}, set_for_review={set_for_review}, cover_page={cover_page}, export_type={export_type}, is_rfp={is_rfp}, requested_volumes={requested_volumes}")

        opportunity_metadata = await self.get_opportunity(opportunity_id, tenant_id, opportunity_type)
        logger.info(f"Opportunity metadata: {opportunity_metadata}")
        
        opportunity_metadata = json.dumps(opportunity_metadata.as_dict())
        tenant_metadata = await self.get_tenant(tenant_id)
        logger.info(f"Tenant metadata: {tenant_metadata}")
        
        content_compliance = await self.get_content_compliance(opportunity_id, tenant_id, opportunity_type)
        logger.info(f"Content compliance: {content_compliance}")
        
        technical_requirements = await self.get_technical_requirements(opportunity_id, tenant_id, opportunity_type)
        logger.info(f"Technical requirements: {technical_requirements}")

        all_table_of_contents = await self.get_all_table_of_contents(opportunity_id, tenant_id, opportunity_type)
        #logger.info(f"All table_of_contents: {all_table_of_contents}")
        
        outlines = await self.get_all_outlines(opportunity_id, tenant_id, opportunity_type)
        #logger.info(f"All outlines: {outlines}")

        personality = await self.get_personality(int(personality_id))

        ## Initalize List for all requested volumes
        proposal_volumes: List[List[Dict[str, Any]] | None] = []

        ## Get the table of contents for each volume
        for idx, table_of_contents in enumerate(all_table_of_contents):
            proposal = []
            current_volume = idx + 1

            logger.info(f"Processing volume {current_volume}")

            ## Skip current volume if it is not part of requested volumes
            if current_volume not in requested_volumes:
                logger.info(f"Skipping volume {current_volume} as it is not in requested_volumes")
                ## Append a None if the proposal volumes list to maintain order for
                ## requested volumes, i.e if user requested volumes [2, 3]
                ## propoal_volumes will be [None, data, data, None, None]
                proposal_volumes.append(None)
                continue
            
            ## if table of contents has not been generate dbefore generate it
            if table_of_contents is None or len(table_of_contents) == 0:
                logger.info(f"Table of contents for volume {current_volume} not found, generating...")
                table_of_contents = await self.generate_table_of_contents(
                    is_rfp=is_rfp,
                    volume_number=current_volume,
                    opportunity_id=opportunity_id,
                    tenant_id=tenant_id,
                    source=opportunity_type,
                    content_compliance=content_compliance,
                )
                logger.info(f"Generated table_of_contents for volume {current_volume}: {table_of_contents}")

            ## Get the generated outline for this volume
            outline = outlines[idx]

            ## If outlien ahs not been genrated, generate it
            if outline is None or len(outline) == 0:
                logger.info(f"Outline for volume {current_volume} not found, generating...")
                outline = await self.generate_outline(current_volume, table_of_contents, opportunity_id, tenant_id, opportunity_type)
                logger.info(f"Generated outline for volume {current_volume}: {outline}")

            logger.info("Beginning proposal section generation...")

            for index, item in enumerate(table_of_contents):
                logger.info(f"Processing section {index+1}/{len(table_of_contents)}: {item.get('title', '')}")
                
                outline_guide = outline[index]
                logger.info(f"Outline guide for section {index+1}: {outline_guide}")

                rfp_section_generator = RFPSection(
                    opportunity_id=opportunity_id,
                    outline_guide = outline_guide,
                    technical_requirements = technical_requirements,
                    content_compliance=content_compliance,
                    tenant_metadata=tenant_metadata or '',
                    opportunity_metadata=opportunity_metadata,
                    tenant_id=tenant_id,
                    client=client_short_name,
                    profile_id=profile_id,
                    source_documents=source_documents,
                    personality=personality,
                    internet_search_results=True  # Set to True to show search queries
                )

                title = item.get("title", "")
                description = item.get("description", "")
                subsections = item.get("subsections", [])

                logger.info(f"Generating RFP section header for '{title}' with description '{description}' and outline_guide '{outline_guide}'")
                header = await rfp_section_generator.generate_rfp_section_header(title, description, outline_guide)
                logger.info(f"Generated header for section '{title}': {header}")
                section = ProposalUtilities.extract_json_from_brackets(header)
                logger.info(f"Extracted section JSON for '{title}': {section}")

                # Show search queries if available
                if hasattr(rfp_section_generator, "last_search_query"):
                    logger.info(f"Search query for section '{title}': {getattr(rfp_section_generator, 'last_search_query', None)}")

                if section is None:
                    logger.error(f"Failed to generate header for section '{title}'. Exiting pipeline.")
                    ## This ensures order is maintained for volume sections incase of error
                    proposal.append(None)
                    continue

                ## Generate subsections
                section["subsections"] = []

                for i, subsection_item in enumerate(subsections):
                    sub_title = subsection_item.get("title", "")
                    sub_description = subsection_item.get("description", "")

                    try:
                        sub_outline_guide = outline_guide.get("subsections", [])[i] if outline and index < len(outline) else {}
                    except (IndexError, KeyError, TypeError) as e:
                        logger.warning(f"Error retrieving outline guide for subsection {i} of section {index}: {e}")
                        sub_outline_guide = {}

                    logger.info(f"Generating RFP subsection for '{sub_title}' with description '{sub_description}' and outline_guide '{sub_outline_guide}'")
                    subsection = await rfp_section_generator.generate_rfp_subsection(sub_title, sub_description, sub_outline_guide)
                    logger.info(f"Generated subsection for '{sub_title}': {subsection}")
                    subsection_json = ProposalUtilities.extract_json_from_brackets(subsection)
                    logger.info(f"Extracted subsection JSON for '{sub_title}': {subsection_json}")

                    # Show search queries if available
                    if hasattr(rfp_section_generator, "last_search_query"):
                        logger.info(f"Search query for subsection '{sub_title}': {getattr(rfp_section_generator, 'last_search_query', None)}")

                    section["subsections"].append(subsection_json)

                # Show the full section (header + subsections)
                logger.info(f"Full section for '{title}': {section}")

                proposal.append(section)

            ## Append current volume to list of proposal_volumes
            logger.info(f"Completed proposal for volume {current_volume}: {proposal}")
            proposal_volumes.append(proposal)

        logger.info(f"All proposal_volumes: {proposal_volumes}")

        if set_for_review:
            logger.info("Moving all volumes to review...")
            await self.move_all_volumes_to_review(
                all_volumes=proposal_volumes,
                tenant_id=tenant_id,
                opportunity_id=opportunity_id,
                source=opportunity_type,
                client_short_name=client_short_name,
                job_instruction=job_instruction
            )
        else:
            logger.info("Moving all volumes to format...")
            await self.move_all_volumes_to_format(
                all_volumes=proposal_volumes,
                tenant_id=tenant_id,
                opportunity_id=opportunity_id,
                source=opportunity_type,
                client_short_name=client_short_name,
                cover_page=cover_page,
                format_type=export_type,
                job_submitted_by=job_submitted_by
            )

        # Automatically queue criticism analysis after successful proposal generation
        await self._queue_criticism_analysis(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            client_short_name=client_short_name,
            submitted_by=job_submitted_by
        )

    async def _queue_criticism_analysis(
        self,
        opportunity_id: str,
        tenant_id: str,
        client_short_name: str,
        submitted_by: str = "system"
    ):
        """
        Automatically queue criticism analysis for the generated proposal.
        """
        try:
            async for db in get_customer_db():
                criticism_item = await ProposalCriticismQueueService.create_item(
                    db=db,
                    opportunity_id=opportunity_id,
                    tenant_id=tenant_id,
                    client_short_name=client_short_name,
                    priority=1,
                    analysis_type="full",
                    submitted_by=f"auto_{submitted_by}"
                )

                if criticism_item:
                    logger.info(f"Successfully queued criticism analysis for opportunity {opportunity_id}")
                else:
                    logger.warning(f"Failed to queue criticism analysis for opportunity {opportunity_id}")
                break

        except Exception as e:
            logger.error(f"Error queuing criticism analysis for opportunity {opportunity_id}: {e}")



    

