import logging
import json
from typing import Any, Dict, List, Optional

from langchain_ollama import ChatOllama
from controllers.kontratar.chromadb_mapping_controller import ChromaDBMappingController
from services.proposal.utilities import ProposalUtilities
from services.chroma.chroma_service import ChromaService
from services.proposal.internet_search import InternetSearchService
from database import get_kontratar_db

class RFPSection:
    def __init__(
        self,
        opportunity_metadata: str,
        opportunity_id: str,
        tenant_metadata: str,
        technical_requirements: str,
        content_compliance: str,
        outline_guide: Dict[str, Any],
        tenant_id: str,
        client: str,
        internet_search_results: bool = True,
        source_documents: List[str] = [],
        profile_id: Optional[str] = None,
        personality: Optional[str] = None,
    ):
        self.opportunity_metadata: str = opportunity_metadata
        self.opportunity_id: str = opportunity_id
        self.tenant_metadata: str = tenant_metadata
        self.tenant_id: str = tenant_id
        self.client: str = client
        self.profile_id = profile_id
        self.technical_requirements = technical_requirements
        self.content_compliance = content_compliance
        self.outline_guide = outline_guide
        self.internet_search_results = internet_search_results
        self.source_documents = source_documents
        self.personality = personality or ""
        self.logger = logging.getLogger(self.__class__.__name__)

        self.MAX_TOKENS = 8000
        self.model = "gemma3:27b"
        self.llm = ChatOllama(
            model=self.model, 
            base_url="http://ai.kontratar.com:11434", 
            num_predict=self.MAX_TOKENS,
            num_ctx=32000
        )

        self.internet_search_service = InternetSearchService()


    async def get_source_documents_context(self, tenant_id: str, source_documents: List[str], query: str) -> str:
        if len(source_documents) == 0:
            return ""
        
        context = ""
        
        for document in source_documents:
            collection_name = f"{tenant_id}_{document}"
            chunks = await self.get_relevant_chunks(collection_name, query, 2)
            if chunks:
                context += f"{chunks}\n"

        return f"""
            <documents>
                {context}
            </documents>
        """

    def search_internet(self, query: str) -> str:
        results = self.internet_search_service.search(query)
        answer = results.get("answer")
        if answer is None:
            return ""

        return answer

    def get_internet_search_data(self, search_queries: List[str]) -> str:
        if not self.internet_search_results:
            return ""

        result = [f"Query:{query}\nAnswer:{self.search_internet(query)}" for query in search_queries]
        data = '\n'.join(result)
        ProposalUtilities.save_text_to_file(data, 'internet-search-results.txt')
        return f"""
            <internet-search-results>
                {data}
            </internet-search-results>
        """

    @property
    def system_prompt(self):
        return f"""
        {self.personality}

        **Writing Style:**
        - Use Active Voice, Crisp Language, and Customer-Centric Writing. Incorporate proof points, data, and case studies.
        - Follow APB (Annotated Proposal Bibliography) for content structuring.
        - The generated and developed RFP must align with the government agency’s evaluation scoring model,
        ensuring that the RFP response content meets specified evaluation factors and criteria.
        - YOU MUST be solution-oriented
        - ENSURE that the RFP section achieves clarity by clearly communicating
        how the RFP meets government specifications and specified project needs
        - ENSURE that the RFP section achieves persuasiveness through differentiating proposed solutions,
        recommendations and proposition as a properly, adequately and profoundly differentiated from competitor offerings
        - ENSURE that the RFP section is in consonance with the Shipley methodology
        - ENSURE that the RFP section aligns with standard federal procurement standards and structuring.

        **Rules:**
        - DO NOT ask any follow-up questions in the result you generate.
        - You are generating a DETAILED RFP for submission.
        - NEVER USE placeholders like `[ ]`, `( )`, or "TBD" for missing info.
        - DO NOT use "the contractor" or "the vendor". USE personal proonouns.
        - You WILL be given the RFP requirements in <rfp-requirements>, PLEASE ensure you address them and USE the naming conventions found there
        - BE consistent when using the company name. ONLY USE the EXACT tenant name name you find in <company-information>
        - You will be given a possible list of tables/images to generate, generate ONLY the tables AND in markdown format
        - You will be given a page limit to adhere to, ENSURE you adhrer to it, for context 5 paragraphs make a page. SO multiply 
        the page limit by 5 paragraphs BUT take into account, images and tables.
        - Before finalizing, scan for any bracketed text or vague pronouns.

        **Important**:
        The section SHOULD be returned as JSON so ensure to generate accurate, parsebale and valid JSON.
        You will be given a JSON schema to comply to, ensure to follow it strictly.
        """

    async def generate_rfp_subsection(self, sub_section_title: str, description: str, outline: Dict[str, Any]):
        client_query = outline.get("client_vector_db_query", None)
        rfp_query = outline.get("rfp_vector_db_query", None)

        if client_query is None:
            raise ValueError("client_vector_db_query is missing in outline")
        if rfp_query is None:
            raise ValueError("rfp_vector_db_query is missing in outline")

        
        # Cast to string and append version if version is not None and greater than 0
        client_collection = f"{self.tenant_id}_{self.client}" if self.profile_id is None else f"{self.client}.{self.profile_id}"
        rfp_collection = self.opportunity_id if self.tenant_id is None else f"{self.tenant_id}_{self.opportunity_id}"
        

        self.logger.info(f"Generating section for title {sub_section_title} and subtitle {description}")

        # Placeholder: Replace with actual ChromaDBService call
        client_context = await self.get_relevant_chunks(client_collection, client_query, 5)
        rfp_context = await self.get_relevant_chunks(rfp_collection, rfp_query, 5)

        source_documents_context = await self.get_source_documents_context(self.tenant_id, self.source_documents, client_query)
        
        search_queries = self.generate_internet_query(outline.get("content") or '')
        internet_search_results = self.get_internet_search_data(search_queries)

        system_prompt = f"""
        {outline.get("custom_prompt")}

        {self.system_prompt}
 
        """
        
        image_list = (
            '\n'.join([f"{i+1}. {desc}" for i, desc in enumerate(outline.get('image_descriptions', []))])
            if outline.get('image_descriptions') else "No images or tables needed"
        )
        
        user_prompt = f"""
            Generate a {sub_section_title} section for this RFP

            Description of what the {sub_section_title} is about: {description}

            The aim of this section is to {outline.get('purpose')}

            The page limit for this section is {outline.get('page_limit')} pages.
            
            {internet_search_results}

            <rfp-requirements>
                {rfp_context}
            </rfp-requirements>

            <company-information>
                {self.tenant_metadata}
                {client_context or ''}
            </company-information>

            <guide>
                {outline.get('content')}
            </guide>

            List of Tables or Images:
            {image_list}    

            {source_documents_context}


            Leverage all provided details—including opportunity information, company data, and specific requirements—to craft a comprehensive,
            persuasive, and FULLY compliant RFP section without any placeholders. Ensure your output integrates every relevant element seamlessly.

            Use the JSON schema below:
            {{ "title": "string", "subtitle": "string", "paragraphs": [] }}

            - "title" field SHOULD be the title of the subsection you are addressing
            - "subtitle" field SHOULD provide a more detailed explanation that expands on the title's meaning
            - "paragraphs" field MUST be an ARRAY of STRINGS with AT LEAST {int(str(outline.get('page_limit')) or '2') * 5} items and with EACH array item containing AT LEAST 4 sentences that thoroughly
            explain the subsection's key points.

            **Important:**
            - YOU MUST return ALL specified fields
            - RETURN a SINGLE JSON object, DO NOT return multiple objects
            - ONLY return A COMPLIANT JSON, DO NOT return any other thing.
        """

        # Placeholder: Replace with actual LLMService call
        generated_text = self.generate_text(system_prompt, user_prompt)
        if generated_text is None:
            return ""
        return generated_text

    async def generate_rfp_section_header(self, title: str, description: str, outline: Dict[str, Any]):
        client_query = outline.get("client_vector_db_query", None)
        rfp_query = outline.get("rfp_vector_db_query", None)

        if client_query is None:
            raise ValueError("client_vector_db_query is missing in outline")
        if rfp_query is None:
            raise ValueError("rfp_vector_db_query is missing in outline")

        client_query = outline.get("client_vector_db_query", None)
        rfp_query = outline.get("rfp_vector_db_query", None)

        if client_query is None:
            raise ValueError("client_vector_db_query is missing in outline")
        if rfp_query is None:
            raise ValueError("rfp_vector_db_query is missing in outline")

        
        # Cast to string and append version if version is not None and greater than 0
        client_collection = f"{self.tenant_id}_{self.client}" if self.profile_id is None else f"{self.client}.{self.profile_id}"
        rfp_collection = self.opportunity_id if self.tenant_id is None else f"{self.tenant_id}_{self.opportunity_id}"
        
        self.logger.info(f"Generating section for title {title} and subtitle {description}")

        # Placeholder: Replace with actual ChromaDBService call
        client_context = await self.get_relevant_chunks(client_collection, client_query, 5)
        rfp_context = await self.get_relevant_chunks(rfp_collection, rfp_query, 5)

        source_documents_context = await self.get_source_documents_context(self.tenant_id, self.source_documents, client_query)
        
        search_queries = self.generate_internet_query(outline.get("content") or '')
        internet_search_results = self.get_internet_search_data(search_queries)

        #images_tables = json.loads(outline.get(""))
        
        system_prompt = f"""
        {outline.get("custom_prompt")}

        {self.system_prompt}
        """
        image_list = (
            '\n'.join([f"{i+1}. {desc}" for i, desc in enumerate(outline.get('image_descriptions', []))])
            if outline.get('image_descriptions') else "No images or tables needed"
        )
        
        user_prompt = f"""
            Generate a {title} section for this RFP

            Description of what the {title} is about: {description}

            The aim of this section is to {outline.get('purpose')}
            
            {internet_search_results}

            <rfp-requirements>
                {rfp_context}
            </rfp-requirements>

            <company-information>
                {self.tenant_metadata}
                {client_context or ''}
            </company-information>

            <guide>
                {outline.get('content')}
            </guide>

            {source_documents_context}

            Use the JSON schema below:
            {{ "title": "string", "subtitle": "string", "number": "string", "paragraphs": [] }}

            - "title" field SHOULD be the title of the section you are addressing
            - "subtitle" field SHOULD provide a more detailed explanation that expands on the title's meaning
            - "paragraphs" field MUST be an ARRAY of STRINGS with AT LEAST {int(str(outline.get('page_limit')) or '2') * 5} items 
            and with EACH array item containing AT LEAST 4 sentences that thoroughly explain the section's key points.
            - "number" will be the EXACT number in the table of contents this header is addressing, eg 1.0, 3.0

            **Important:**
            - YOU MUST return ALL specified fields
            - RETURN a SINGLE JSON object, DO NOT return multiple objects
            - ONLY return A COMPLIANT JSON, DO NOT return any other thing.
        """

        # Placeholder: Replace with actual LLMService call
        generated_text = self.generate_text(system_prompt, user_prompt)
        if generated_text is None:
            return ""
        return generated_text

    def generate_section_table(self, table_information: str, rfp_context: str, client_context: str, section_title: str, section_description: str):
        """Generate a Markdown Tabe for a Proposal Section"""
        system_prompt = f"""
        **Role:**
        You are a proposal expert skilled at creating winning proposals for government opportunities.

        **Task:**
        Your job is to generate a table for the section of a proposal. THIS table MUST be generated in
        Markdown. YOU will be given information on the columns and the information that this table would have.
        You will also be given the relevant RFP requirements for this table as well relevant company information.

        **Rules:**
        1. DO NOT include meta-phrases before and after the table. ONLY return the table
        2. If the table information given to you is not a table, RETURN "NOT A TABLE"
        3. RETURN the table inside <table> tags
        """

        user_prompt = f"""
        You are generating a table for an RFP section called {section_title}

        This is the description of this section: {section_description}

        <table-information>
            {table_information}
        </table-information>

        <rfp-requirements>
            {rfp_context}
        </rfp-requirements>

        <company-information>
            {self.tenant_metadata}
            {client_context or ''}
        </company-information>

        """

        return self.generate_text(system_prompt, user_prompt)


    def generate_subsection_image_text(self, subsection_content):
        system_prompt = """
        You are a highly specialized image alt text generator.
        Your job is to generate alt text for an image that will describe a section of the proposal.
        This text should fully describe the image as the image will be places at the end of this section.
        The text will be passed to an image generation model so ensure the text fully descibes the image.

        The section content is found in <section-content>.

        ENSURE the alt text is descriptive and to the point, and is no more than 100 characters.

        **Example Response**:
        -  Good Example:
        "A company team in a conference room with laptops and whiteboards, surrounded by documents and
        technology equipment, with a calendar and timeline on the wall behind them."

        - Bad Examples:
        Here is a possible alt text for an image describing this section:
        "A company team in a conference room with laptops and whiteboards, surrounded by documents and
        technology equipment, with a calendar and timeline on the wall behind them."

        "Proposal page with title 'FCI Big Spring - HVAC Package Units Solicitation' and subtitle
        'List of Assumptions Made During Cost Estimation'"
        """

        user_prompt = f"""
            Here is the section content:
            <section-content>
                {subsection_content}
            </section-content>

            Generate a text for an image that will desribe this section.
        """
        # Placeholder: Replace with actual LLMService call
        return self.generate_text(system_prompt, user_prompt)

    # --- Placeholders for external service calls ---
    async def get_relevant_chunks(self, collection_name, query, limit):
        chroma_service = ChromaService("http://ai.kontratar.com:5000")
        
        async for db in get_kontratar_db():
            chunks = await chroma_service.get_relevant_chunks(db, collection_name, query, limit)
            context = "\n".join(chunks)
            break
        
        return context

    def generate_text(self, system_prompt, user_prompt):
        messages = [
            ("system", system_prompt.strip()),
            ("user", user_prompt.strip()),
        ]
        response = self.llm.invoke(messages)
        return str(response.content)


    def generate_internet_query(self, text: str) -> list:
        if not text or not self.internet_search_results:
            return []

        llm = ChatOllama(model="gemma3:27b", base_url="http://ai.kontratar.com:11434")
        prompt = (
            "Given the following government opportunity section, generate two highly relevant and niche search queries "
            "that would retrieve additional information to augment the context of this opportunity. "
            "Both queries should be general (not company-specific) and focused on surfacing more details about the opportunity, "
            "such as requirements, evaluation criteria, agency background, or related regulations. "
            "Return your response as a JSON array of two strings, e.g. [\"query 1\", \"query 2\"].\n\n"
            f"{text}\n\n"
            "JSON Array:"
        )
        response = llm.invoke(prompt)
        try:
            queries = ProposalUtilities.extract_json_from_brackets(str(response.content), "[]")
            if isinstance(queries, list) and len(queries) == 2:
                return queries
            else:
                raise ValueError("Response is not a JSON array of two queries.")
        except Exception as e:
            self.logger.error(f"Failed to parse search queries from LLM response: {e} | Raw response: {response.content}")
            return []
        