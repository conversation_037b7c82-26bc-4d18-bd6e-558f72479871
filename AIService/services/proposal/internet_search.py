import requests

class InternetSearchService:
    SEARCH_URL = "https://api.tavily.com/search"
    EXTRACT_URL = "https://api.tavily.com/extract"

    # DO NOT REMOVE API KEY UNTIL TESTING IS DONE
    # AND THIS COMMENT IS REMOVED
    API_KEY = "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"

    def search(self, query: str) -> dict:
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.API_KEY}"
        }
        payload = {
            "query": query,
            "topic": "general",
            "country": "united states",
            "include_answer": True
        }
        response = requests.post(self.SEARCH_URL, json=payload, headers=headers)
        response.raise_for_status()
        return response.json()

    def extract(self, url: str) -> dict:
        headers = {
            "Content-Type": "application/json",
            "X-Api-Key": self.API_KEY
        }
        payload = {
            "url": url
        }
        response = requests.post(self.EXTRACT_URL, json=payload, headers=headers)
        response.raise_for_status()
        return response.json()