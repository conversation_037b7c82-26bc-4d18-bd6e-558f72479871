import asyncio
import httpx
from typing import Dict, List, Any, Optional
from loguru import logger
import json


class SemanticScholarService:
    """
    Service for integrating with Semantic Scholar API to enhance proposal content
    with relevant research and academic papers.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        self.base_url = "https://api.semanticscholar.org/graph/v1"
        self.api_key = api_key
        self.headers = {
            "Content-Type": "application/json",
            "User-Agent": "GovBD-BackEnd-Python/1.0"
        }
        if api_key:
            self.headers["x-api-key"] = api_key
    
    async def search_papers(
        self, 
        query: str, 
        limit: int = 10,
        fields: List[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for academic papers related to a query.
        """
        if fields is None:
            fields = [
                "paperId", "title", "abstract", "authors", "year", 
                "publicationDate", "citationCount", "influentialCitationCount",
                "venue", "publicationTypes", "publicationVenue", "url"
            ]
        
        params = {
            "query": query,
            "limit": min(limit, 100),
            "fields": ",".join(fields)
        }
        
        try:
            timeout = httpx.Timeout(30.0)
            async with httpx.AsyncClient(timeout=timeout, headers=self.headers) as client:
                url = f"{self.base_url}/paper/search"
                response = await client.get(url, params=params)
                response.raise_for_status()
                
                data = response.json()
                papers = data.get("data", [])
                
                logger.info(f"Found {len(papers)} papers for query: {query[:50]}...")
                return papers
                
        except httpx.HTTPError as e:
            logger.error(f"HTTP error searching papers: {e}")
            return []
        except Exception as e:
            logger.error(f"Error searching papers: {e}")
            return []
    
    async def get_paper_details(self, paper_id: str, fields: List[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about a specific paper.
        """
        if fields is None:
            fields = [
                "paperId", "title", "abstract", "authors", "year",
                "publicationDate", "citationCount", "influentialCitationCount",
                "venue", "publicationTypes", "publicationVenue", "url",
                "references", "citations"
            ]
        
        params = {
            "fields": ",".join(fields)
        }
        
        try:
            timeout = httpx.Timeout(30.0)
            async with httpx.AsyncClient(timeout=timeout, headers=self.headers) as client:
                url = f"{self.base_url}/paper/{paper_id}"
                response = await client.get(url, params=params)
                response.raise_for_status()
                
                paper = response.json()
                logger.info(f"Retrieved details for paper: {paper.get('title', 'Unknown')}")
                return paper
                
        except httpx.HTTPError as e:
            logger.error(f"HTTP error getting paper details: {e}")
            return None
        except Exception as e:
            logger.error(f"Error getting paper details: {e}")
            return None
    
    async def search_relevant_research(
        self, 
        topic: str, 
        keywords: List[str] = None,
        limit: int = 5
    ) -> Dict[str, Any]:
        """
        Search for research relevant to a specific topic with enhanced context.
        """
        logger.info(f"Searching for research on topic: {topic}")
        
        # Construct enhanced search query
        search_query = topic
        if keywords:
            search_query += " " + " ".join(keywords)
        
        # Search for papers
        papers = await self.search_papers(search_query, limit=limit)
        
        if not papers:
            return {
                "topic": topic,
                "papers_found": 0,
                "papers": [],
                "summary": f"No relevant research papers found for topic: {topic}"
            }
        
        # Process papers for better context
        processed_papers = []
        for paper in papers:
            processed_paper = {
                "title": paper.get("title", ""),
                "abstract": paper.get("abstract", ""),
                "authors": [author.get("name", "") for author in paper.get("authors", [])],
                "year": paper.get("year"),
                "citation_count": paper.get("citationCount", 0),
                "venue": paper.get("venue", ""),
                "url": paper.get("url", ""),
                "relevance_score": self._calculate_relevance_score(paper, topic, keywords)
            }
            processed_papers.append(processed_paper)
        
        processed_papers.sort(key=lambda x: x["relevance_score"], reverse=True)
        
        summary = self._generate_research_summary(topic, processed_papers)
        
        return {
            "topic": topic,
            "papers_found": len(processed_papers),
            "papers": processed_papers,
            "summary": summary
        }
    
    def _calculate_relevance_score(
        self, 
        paper: Dict[str, Any], 
        topic: str, 
        keywords: List[str] = None
    ) -> float:
        """Calculate a simple relevance score for a paper."""
        score = 0.0
        
        title = paper.get("title", "").lower()
        abstract = paper.get("abstract", "").lower()
        
        # Topic relevance in title (higher weight)
        if topic.lower() in title:
            score += 3.0
        
        # Topic relevance in abstract
        if topic.lower() in abstract:
            score += 1.0
        
        # Keyword relevance
        if keywords:
            for keyword in keywords:
                if keyword.lower() in title:
                    score += 2.0
                if keyword.lower() in abstract:
                    score += 0.5
        
        # Citation count factor (normalized)
        citation_count = paper.get("citationCount", 0)
        if citation_count > 0:
            score += min(citation_count / 100, 2.0)
        
        year = paper.get("year")
        if year and year >= 2020:
            score += 0.5
        
        return score
    
    def _generate_research_summary(self, topic: str, papers: List[Dict[str, Any]]) -> str:
        """Generate a summary of the research findings."""
        if not papers:
            return f"No research papers found for {topic}."
        
        summary = f"Research Summary for {topic}:\n\n"
        summary += f"Found {len(papers)} relevant academic papers. "
        
        # Get top papers
        top_papers = papers[:3]
        
        if top_papers:
            summary += "Key findings include:\n\n"
            for i, paper in enumerate(top_papers, 1):
                title = paper.get("title", "Unknown Title")
                authors = paper.get("authors", [])
                year = paper.get("year", "Unknown Year")
                citations = paper.get("citation_count", 0)
                
                author_str = ", ".join(authors[:2]) 
                if len(authors) > 2:
                    author_str += " et al."
                
                summary += f"{i}. {title}\n"
                summary += f"   Authors: <AUTHORS>
                summary += f"   Citations: {citations}\n"
                
                abstract = paper.get("abstract", "")
                if abstract:
                    truncated_abstract = abstract[:200] + "..." if len(abstract) > 200 else abstract
                    summary += f"   Summary: {truncated_abstract}\n"
                
                summary += "\n"
        
        return summary
    
    async def enhance_proposal_content(
        self, 
        section_content: str, 
        research_topics: List[str],
        max_papers_per_topic: int = 3
    ) -> Dict[str, Any]:
        """
        Enhance proposal content with relevant research findings.
        """
        logger.info(f"Enhancing proposal content with research on {len(research_topics)} topics")
        
        research_results = {}
        all_papers = []
        
        # Search for research on each topic
        for topic in research_topics:
            research = await self.search_relevant_research(
                topic=topic,
                limit=max_papers_per_topic
            )
            research_results[topic] = research
            all_papers.extend(research["papers"])
        
        # Generate enhanced content suggestion
        enhancement_summary = self._generate_enhancement_summary(research_results)
        
        return {
            "original_content": section_content,
            "research_topics": research_topics,
            "research_results": research_results,
            "total_papers_found": len(all_papers),
            "enhancement_summary": enhancement_summary,
            "suggested_improvements": self._suggest_content_improvements(section_content, research_results)
        }
    
    def _generate_enhancement_summary(self, research_results: Dict[str, Any]) -> str:
        """Generate a summary of research enhancements."""
        total_papers = sum(result["papers_found"] for result in research_results.values())
        
        summary = f"Research Enhancement Summary:\n"
        summary += f"- Searched {len(research_results)} research topics\n"
        summary += f"- Found {total_papers} relevant academic papers\n"
        summary += f"- Research areas covered: {', '.join(research_results.keys())}\n\n"
        
        for topic, result in research_results.items():
            if result["papers_found"] > 0:
                summary += f"**{topic}**: {result['papers_found']} papers found\n"
                top_paper = result["papers"][0] if result["papers"] else None
                if top_paper:
                    summary += f"  Top result: {top_paper['title']} ({top_paper['year']})\n"
        
        return summary
    
    def _suggest_content_improvements(
        self, 
        original_content: str, 
        research_results: Dict[str, Any]
    ) -> List[str]:
        """Suggest improvements based on research findings."""
        suggestions = []
        
        for topic, result in research_results.items():
            if result["papers_found"] > 0:
                suggestions.append(
                    f"Consider incorporating recent research on {topic} "
                    f"({result['papers_found']} relevant papers found)"
                )
                
                high_impact_papers = [
                    p for p in result["papers"] 
                    if p.get("citation_count", 0) > 50
                ]
                
                if high_impact_papers:
                    suggestions.append(
                        f"Reference high-impact research in {topic} area "
                        f"(found {len(high_impact_papers)} highly-cited papers)"
                    )
        
        if not suggestions:
            suggestions.append("No specific research-based improvements identified")
        
        return suggestions
