#!/usr/bin/env python3
"""
Final Test of Fixed Criticism System

Tests all the fixes:
1. Proper compliance requirements generation
2. Correct data structure for criticism chain
3. Technical requirements in content_compliance
4. LLM error handling without fallbacks
5. Complete criticism analysis workflow
"""

import asyncio
import json
import logging
from services.queue_service.proposal_criticism_queue_service import ProposalCriticismQueueService
from services.scheduler_service.proposal_criticism_scheduler_service import ProposalCriticismSchedulerService
from database import get_customer_db

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("CRITICISM_FINAL_TEST")


async def test_complete_criticism_workflow():
    """Test the complete criticism workflow with real data"""
    
    print("🚀 TESTING COMPLETE CRITICISM WORKFLOW")
    print("=" * 60)
    
    try:
        criticism_scheduler = ProposalCriticismSchedulerService()
        
        async for db in get_customer_db():
            print("✅ Database connection established")
            
            # Create a test criticism queue item
            test_opportunity_id = "iRiYNgd8RC"
            test_tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
            
            print(f"📋 Testing with opportunity: {test_opportunity_id}")
            
            queue_item = await ProposalCriticismQueueService.create_item(
                db=db,
                opportunity_id=test_opportunity_id,
                tenant_id=test_tenant_id,
                client_short_name="TestClient",
                priority=1,
                analysis_type="full",
                submitted_by="test_user"
            )
            
            if not queue_item:
                print("❌ Failed to create test queue item")
                return False
            
            print(f"✅ Created test queue item with ID: {queue_item.id}")
            
            try:
                # Test the complete workflow
                print("\n🔄 TESTING COMPLETE WORKFLOW:")
                
                # 1. Test proposal data retrieval
                print("1️⃣ Testing proposal data retrieval...")
                proposal_data = await criticism_scheduler._get_proposal_data_for_criticism(db, queue_item)
                
                if proposal_data:
                    print(f"   ✅ Proposal data retrieved")
                    print(f"   📊 Keys: {list(proposal_data.keys())}")
                    
                    # Check draft structure
                    draft = proposal_data.get('draft', {})
                    if isinstance(draft, dict) and 'draft' in draft:
                        sections = draft['draft']
                        print(f"   📋 Draft sections: {len(sections) if isinstance(sections, list) else 'Not a list'}")
                    else:
                        print(f"   ⚠️ Draft structure: {type(draft)}")
                else:
                    print(f"   ❌ No proposal data retrieved")
                    return False
                
                # 2. Test compliance requirements loading
                print("\n2️⃣ Testing compliance requirements loading...")
                compliance_types = ['structure', 'content', 'formatting', 'technical']
                compliance_results = {}
                
                for comp_type in compliance_types:
                    compliance = await criticism_scheduler._load_compliance_requirements_from_db(
                        db, test_opportunity_id, comp_type
                    )
                    compliance_results[comp_type] = bool(compliance)
                    
                    if compliance:
                        print(f"   ✅ {comp_type}: Found compliance requirements")
                        if isinstance(compliance, dict):
                            print(f"      📊 Keys: {list(compliance.keys())}")
                    else:
                        print(f"   ❌ {comp_type}: No compliance requirements")
                
                # 3. Test compliance generation if missing
                if not any(compliance_results.values()):
                    print("\n3️⃣ Testing compliance requirements generation...")
                    
                    generation_success = await criticism_scheduler._generate_missing_compliance_requirements(
                        db, test_opportunity_id, test_tenant_id
                    )
                    
                    if generation_success:
                        print(f"   ✅ Compliance requirements generated successfully")
                        
                        # Reload compliance requirements
                        for comp_type in compliance_types:
                            compliance = await criticism_scheduler._load_compliance_requirements_from_db(
                                db, test_opportunity_id, comp_type
                            )
                            compliance_results[comp_type] = bool(compliance)
                            
                            if compliance:
                                print(f"   ✅ {comp_type}: Generated and loaded")
                    else:
                        print(f"   ❌ Compliance requirements generation failed")
                        return False
                else:
                    print("\n3️⃣ Compliance requirements already exist, skipping generation")
                
                # 4. Test criticism analysis
                print("\n4️⃣ Testing criticism analysis...")
                
                try:
                    criticism_results = await criticism_scheduler._run_criticism_analysis(
                        db, queue_item, proposal_data
                    )
                    
                    if criticism_results:
                        print(f"   ✅ Criticism analysis completed")
                        print(f"   📊 Results keys: {list(criticism_results.keys())}")
                        
                        # Check individual criticism results
                        criticism_types = ['structure_criticism', 'content_criticism', 'formatting_criticism', 'technical_criticism']
                        
                        for crit_type in criticism_types:
                            if crit_type in criticism_results:
                                crit_data = criticism_results[crit_type]
                                if isinstance(crit_data, dict):
                                    score = crit_data.get('score', 0)
                                    print(f"   🎯 {crit_type}: Score {score}")
                                else:
                                    print(f"   📝 {crit_type}: {type(crit_data)}")
                            else:
                                print(f"   ❌ {crit_type}: Missing")
                        
                        # Check if we got meaningful results (not all zeros)
                        scores = []
                        for crit_type in criticism_types:
                            if crit_type in criticism_results:
                                crit_data = criticism_results[crit_type]
                                if isinstance(crit_data, dict):
                                    scores.append(crit_data.get('score', 0))
                        
                        if scores and any(score > 0 for score in scores):
                            print(f"   🎉 Got meaningful criticism results!")
                            avg_score = sum(scores) / len(scores)
                            print(f"   📊 Average score: {avg_score:.1f}")
                        else:
                            print(f"   ⚠️ All scores are 0 - may indicate LLM issues")
                    else:
                        print(f"   ❌ No criticism results returned")
                        return False
                        
                except Exception as e:
                    print(f"   ❌ Criticism analysis failed: {e}")
                    return False
                
                # 5. Test complete queue item processing
                print("\n5️⃣ Testing complete queue item processing...")
                
                try:
                    await criticism_scheduler._process_criticism_queue_item(db, queue_item)
                    
                    # Check final status
                    updated_item = await ProposalCriticismQueueService.get_by_id(db, queue_item.id)
                    
                    if updated_item:
                        print(f"   ✅ Queue item processed")
                        print(f"   📊 Final status: {updated_item.status}")
                        
                        if updated_item.status == "COMPLETED":
                            print(f"   🎉 Processing completed successfully!")
                            return True
                        elif updated_item.status == "FAILED":
                            print(f"   ⚠️ Processing failed: {updated_item.error_message}")
                            return False
                        else:
                            print(f"   ⚠️ Unexpected status: {updated_item.status}")
                            return False
                    else:
                        print(f"   ❌ Could not retrieve updated queue item")
                        return False
                        
                except Exception as e:
                    print(f"   ❌ Queue item processing failed: {e}")
                    return False
                
            finally:
                # Clean up
                try:
                    await ProposalCriticismQueueService.delete_item(db, queue_item.id)
                    print(f"✅ Cleaned up test queue item")
                except:
                    pass
            
            break
            
    except Exception as e:
        print(f"❌ Complete workflow test failed: {e}")
        return False


async def main():
    """Main test function"""
    
    print("🔧 FINAL CRITICISM SYSTEM TEST")
    print("=" * 70)
    print("Testing all fixes:")
    print("✅ Proper compliance requirements generation")
    print("✅ Correct data structure for criticism chain")
    print("✅ Technical requirements in content_compliance")
    print("✅ LLM error handling without fallbacks")
    print("✅ Complete criticism analysis workflow")
    print("=" * 70)
    
    success = await test_complete_criticism_workflow()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 ALL FIXES WORKING CORRECTLY!")
        print("✅ Criticism system is now fully functional")
        print("✅ Ready for production use")
        
        print("\n💡 System Status:")
        print("🔧 Compliance requirements: Generated automatically when missing")
        print("📊 Data structure: Fixed for criticism chain compatibility")
        print("🎯 Technical requirements: Stored in content_compliance field")
        print("🔗 LLM handling: Proper error propagation without fallbacks")
        print("⚡ Queue processing: Complete workflow tested")
        
    else:
        print("❌ SOME ISSUES REMAIN!")
        print("🔧 Review the test output above for specific failures")
        
        print("\n🔍 Common Issues to Check:")
        print("1. LLM server connectivity (Ollama)")
        print("2. Database permissions")
        print("3. Missing opportunity data")
        print("4. Service import errors")
    
    print("\n🚀 Next Steps:")
    print("1. Monitor criticism queue in production")
    print("2. Check criticism results quality")
    print("3. Verify all compliance types work correctly")
    print("=" * 70)


if __name__ == "__main__":
    asyncio.run(main())
