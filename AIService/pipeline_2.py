import asyncio
import json
from services.proposal.cover_page import CoverPageService
from services.proposal.rfp.generate_rfp import RFPGenerationService
from services.proposal.cover_letter import CoverLetterService
from services.proposal.technical_requirements import TechnicalRequirementsService
from services.proposal.content_compliance import ContentComplianceService
from services.proposal.structure_compliance import StructureComplianceService
from services.proposal.formatting_requirements import FormattingRequirementsService
from services.proposal.utilities import ProposalUtilities

from dotenv import load_dotenv
load_dotenv()


async def main():
    # Example/dummy values
    #opportunity_id = "tA30VTcyMj"
    opportunity_id = "iRiYNgd8RC"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    source = "custom"  # or "sam", "ebuy"
    #opportunity_metadata = "Example opportunity metadata for technical requirements."
    is_rfp = True

    # Instantiate services
    tech_service = TechnicalRequirementsService()
    content_service = ContentComplianceService()
    structure_service = StructureComplianceService()
    formatting_service = FormattingRequirementsService()

    rfp_generation_service = RFPGenerationService()
    cover_page_service = CoverPageService()

    #opportunity_metadata = await rfp_generation_service.get_opportunity(opportunity_id, tenant_id, source)
    #tenant_metadata = await rfp_generation_service.get_tenant(tenant_id)    

    '''
    cover_letter_service = CoverLetterService(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source,
        opportunity_metadata=opportunity_metadata,
        tenant_metadata=tenant_metadata,
    )
    '''

    #cover_letter = await cover_letter_service.generate_cover_letter("Volume I")
    #print(cover_letter)

    # print("Generating Technical Requirements...")
    # tech_result = await tech_service.generate_technical_requirements(
    #     opportunity_id=opportunity_id,
    #     tenant_id=tenant_id,
    #     source=source
    # )
    # print("\n--- Technical Requirements ---")
    # print(tech_result["content"])

    print("\nGenerating Content Compliance...")
    content_result = await content_service.generate(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source,
    )
    print("\n--- Content Compliance ---")
    print(content_result)

    ProposalUtilities.save_text_to_file(content_result, "content-compliance.txt")
    '''
    cover_page_fields = await cover_page_service.generate_cover_page(opportunity_id, tenant_id, source)
    print(cover_page_fields)
    '''
    
    

    

    # print("\nGenerating Structure Compliance...")
    # structure_result = await structure_service.generate_structure_compliance(
    #     opportunity_id=opportunity_id,
    #     tenant_id=tenant_id,
    #     source=source,
    #     is_rfp=is_rfp,
    # )
    # print("\n--- Content Compliance ---")
    # print(content_result["content"])

    # print("\nGenerating Structure Compliance...")
    # structure_result = await structure_service.generate_structure_compliance(
    #     opportunity_id=opportunity_id,
    #     tenant_id=tenant_id,
    #     source=source,
    # )
    # print("\n--- Structure Compliance ---")
    # print(structure_result["content"])
    # structure = ProposalUtilities.extract_json_from_backticks(structure_result["content"])
    # ProposalUtilities.save_json_to_file(structure, "structure-compliance.json")
    # print(structure_result["context"])

    '''
    print("\nGenerating Formatting Requirements...")
    formatting_result = await formatting_service.generate_formatting_requirements(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source,
    )
    print("\n--- Formatting Requirements ---")
    print(formatting_result["content"])
    '''

if __name__ == "__main__":
    asyncio.run(main())
