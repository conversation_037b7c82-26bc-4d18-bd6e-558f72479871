import asyncio
import json
import logging
from datetime import datetime
from controllers.customer.tenant_controller import Tenant<PERSON><PERSON>roller
from services.proposal.utilities import ProposalUtilities
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from database import get_customer_db
from services.proposal.multi_agent.workflow import MultiAgentWorkflow

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger("MULTI_AGENT_PIPELINE")


async def main():
    """Main pipeline execution"""
    
    print("Multi-Agent Proposal Generation Pipeline")
    print("=" * 60)
    
    # Configuration
    opportunity_id = "iRiYNgd8RC"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    client = "adeptengineeringsolutions"
    source = "custom"
    
    print(f" Configuration:")
    print(f"   Opportunity ID: {opportunity_id}")
    print(f"   Tenant ID: {tenant_id}")
    print(f"   Client: {client}")
    print(f"   Source: {source}")
    
    try:
        # Initialize workflow
        print(f"\n🔧 Initializing Multi-Agent Workflow...")
        workflow = MultiAgentWorkflow()
        
        # Validate workflow readiness
        readiness = await workflow.validate_workflow_readiness()
        if not readiness['ready']:
            print(f"❌ Workflow not ready: {readiness['issues']}")
            return
        
        print(f"✅ Workflow ready with {readiness['agent_status'].__len__()} agents")
        
        # Get database context
        print(f"\n📊 Retrieving database context...")
        custom_controller = CustomOpportunitiesController
        tenant_controller = TenantController
        
        tenant_metadata = ""
        table_of_contents = []
        
        async for db in get_customer_db():
            # Get table of contents
            record = await custom_controller.get_table_of_contents(db, opportunity_id)
            table_of_contents = json.loads(str(record[0])) if record and record[0] is not None else []
            
            # Get tenant information
            tenant = await tenant_controller.get_by_tenant_id(db, tenant_id)
            tenant_metadata = f"{tenant}"
            break
        
        # Also try to load from file as fallback
        if not table_of_contents:
            try:
                file_toc = ProposalUtilities.read_json_from_file("table-of-contents.json")
                table_of_contents = file_toc.get("table_of_contents", [])
                print(f"📁 Loaded table of contents from file ({len(table_of_contents)} sections)")
            except Exception as e:
                print(f"⚠️ Could not load table of contents from file: {e}")
                table_of_contents = []
        
        print(f"✅ Retrieved context:")
        print(f"   Table of contents sections: {len(table_of_contents)}")
        print(f"   Tenant metadata available: {bool(tenant_metadata)}")
        
        # Generate content for each section
        print(f"\n🎯 Starting Multi-Agent Content Generation...")
        
        proposal_sections = {}
        generation_summary = {
            'total_sections': len(table_of_contents),
            'successful_sections': 0,
            'failed_sections': 0,
            'section_results': {}
        }
        
        for i, section in enumerate(table_of_contents, 1):
            section_title = section.get('title', f'Section {i}')
            section_content = section.get('content', '')
            section_type = _map_section_to_type(section_title)
            
            print(f"\n📝 Generating Section {i}/{len(table_of_contents)}: {section_title}")
            print(f"   Type: {section_type}")
            print(f"   Content length: {len(section_content)} chars")
            
            try:
                # Generate content using multi-agent workflow
                result = await workflow.generate_section_content(
                    opportunity_id=opportunity_id,
                    tenant_id=tenant_id,
                    section_type=section_type,
                    section_content=section_content,
                    client_short_name=client
                )
                
                if result['success']:
                    print(f"   ✅ Generation successful")
                    print(f"   📏 Content length: {len(result.get('content', ''))}")
                    print(f"   🎯 Quality score: {result.get('quality_score', 'N/A')}")
                    
                    proposal_sections[section_title] = {
                        'content': result['content'],
                        'quality_score': result.get('quality_score'),
                        'metadata': result.get('workflow_summary', {})
                    }
                    
                    generation_summary['successful_sections'] += 1
                    generation_summary['section_results'][section_title] = 'success'
                    
                else:
                    print(f"   ❌ Generation failed: {result.get('error', 'Unknown error')}")
                    generation_summary['failed_sections'] += 1
                    generation_summary['section_results'][section_title] = 'failed'
                
            except Exception as e:
                print(f"   ❌ Generation exception: {e}")
                logger.error(f"Section generation failed for {section_title}: {e}")
                generation_summary['failed_sections'] += 1
                generation_summary['section_results'][section_title] = 'exception'
        
        # Create final proposal
        print(f"\n📋 Creating Final Proposal...")
        
        final_proposal = {
            'opportunity_id': opportunity_id,
            'tenant_id': tenant_id,
            'client_short_name': client,
            'generation_timestamp': datetime.now().isoformat(),
            'generation_method': 'multi_agent_workflow',
            'sections': proposal_sections,
            'generation_summary': generation_summary,
            'tenant_metadata': tenant_metadata
        }
        
        # Save proposal
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"proposal-multi-agent-{opportunity_id}-{timestamp}.json"
        
        ProposalUtilities.save_json_to_file(final_proposal, filename)
        print(f"💾 Saved proposal to: {filename}")
        
        # Print summary
        print(f"\n📊 GENERATION SUMMARY")
        print(f"=" * 40)
        print(f"Total sections: {generation_summary['total_sections']}")
        print(f"Successful: {generation_summary['successful_sections']}")
        print(f"Failed: {generation_summary['failed_sections']}")
        
        success_rate = (generation_summary['successful_sections'] / 
                       generation_summary['total_sections'] * 100 
                       if generation_summary['total_sections'] > 0 else 0)
        print(f"Success rate: {success_rate:.1f}%")
        
        if generation_summary['successful_sections'] > 0:
            print(f"\n✅ Multi-agent generation completed with {generation_summary['successful_sections']} successful sections")
        else:
            print(f"\n⚠️ No sections were successfully generated")
            print(f"   This may indicate LLM server issues")
            print(f"   Check LLM connectivity and try again")
        
        print(f"\n🎯 Key Benefits of Multi-Agent System:")
        print(f"   ✅ Robust LLM handling with intelligent retries")
        print(f"   ✅ Clean failure handling (no fallback content)")
        print(f"   ✅ Specialized agents for different content types")
        print(f"   ✅ Quality assurance and compliance checking")
        print(f"   ✅ Comprehensive logging and monitoring")
        
    except Exception as e:
        print(f"\n❌ Pipeline failed: {e}")
        logger.error(f"Pipeline execution failed: {e}")


def _map_section_to_type(section_title: str) -> str:
    """Map section title to section type for the multi-agent system"""
    
    title_lower = section_title.lower()
    
    # Mapping based on common section titles
    if any(term in title_lower for term in ['cover', 'letter', 'introduction']):
        return 'cover_letter'
    elif any(term in title_lower for term in ['executive', 'summary', 'overview']):
        return 'executive_summary'
    elif any(term in title_lower for term in ['technical', 'approach', 'solution', 'methodology']):
        return 'technical_approach'
    elif any(term in title_lower for term in ['management', 'plan', 'project', 'organization']):
        return 'management_plan'
    elif any(term in title_lower for term in ['past', 'performance', 'experience', 'case']):
        return 'past_performance'
    elif any(term in title_lower for term in ['pricing', 'cost', 'budget', 'financial']):
        return 'pricing'
    elif any(term in title_lower for term in ['compliance', 'requirement', 'regulation']):
        return 'compliance'
    elif any(term in title_lower for term in ['appendix', 'attachment', 'supporting']):
        return 'appendix'
    else:
        return 'custom'


async def test_llm_connectivity():
    """Test LLM connectivity before running the main pipeline"""
    
    print("🔍 Testing LLM Connectivity...")
    
    try:
        from services.proposal.multi_agent.robust_llm import RobustLLMManager
        from langchain_core.messages import SystemMessage, HumanMessage
        
        llm_manager = RobustLLMManager()
        
        messages = [
            SystemMessage(content="You are a test assistant."),
            HumanMessage(content="Respond with exactly: 'LLM connectivity test successful'")
        ]
        
        response = await llm_manager.generate_content(messages)
        print(f"✅ LLM connectivity test successful")
        print(f"📝 Response: {response[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ LLM connectivity test failed: {e}")
        print(f"⚠️ The pipeline will likely fail without LLM connectivity")
        return False


if __name__ == "__main__":
    print("🧪 Multi-Agent Proposal Generation Pipeline")
    print("=" * 60)
    
    # Test LLM connectivity first
    connectivity_result = asyncio.run(test_llm_connectivity())
    
    if connectivity_result:
        print(f"\n🚀 LLM is available - proceeding with pipeline")
        asyncio.run(main())
    else:
        print(f"\n⚠️ LLM is not available")
        print(f"   You can still run the pipeline to see clean failure handling:")
        print(f"   python pipeline_multi_agent.py --force")
        
        import sys
        if '--force' in sys.argv:
            print(f"\n🔧 Force mode - running pipeline anyway...")
            asyncio.run(main())
        else:
            print(f"\n💡 To test clean failure handling, run:")
            print(f"   python pipeline_multi_agent.py --force")
