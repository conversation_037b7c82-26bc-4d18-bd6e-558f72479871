#!/usr/bin/env python3
"""
Test Both Fixes

Tests:
1. Letter type fix - tentative/contingent letters generate correct content
2. Criticism queue fix - database queries work correctly
"""

import asyncio
import json
import logging
from services.proposal.outline import ProposalOutlineService
from services.queue_service.proposal_criticism_queue_service import ProposalCriticismQueueService
from database import get_customer_db

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger("TEST_FIXES")


async def test_letter_type_fix():
    """Test that tentative/contingent letters generate correct content"""
    
    print("🧪 Testing Letter Type Fix")
    print("=" * 40)
    
    outline_service = ProposalOutlineService()
    
    # Test tentative offer letter
    table_of_contents = [
        {
            "title": "Tentative Offer Letter",
            "description": "Tentative offer letter for employment",
            "content": "Generate a tentative offer letter for the position described in this opportunity.",
            "number": "1"
        }
    ]
    
    try:
        print("🔄 Testing Tentative Offer Letter generation...")
        
        result = await outline_service.generate_draft(
            opportunity_id="TEST_LETTER_001",
            tenant_id="TEST_TENANT_001",
            source="custom",
            client_short_name="TestClient",
            tenant_metadata="Test Company\n123 Test Street\nTest City, TS 12345\nPhone: (*************",
            table_of_contents=table_of_contents
        )
        
        if result.get("draft") and len(result["draft"]) > 0:
            content = result["draft"][0].get("content", "")
            
            print(f"✅ Generated content ({len(content)} characters)")
            
            # Check if content is appropriate for tentative offer letter
            content_lower = content.lower()
            
            # Positive indicators for tentative offer letter
            tentative_indicators = [
                'tentative', 'contingent', 'conditional', 'offer', 'employment',
                'background', 'verification', 'reference', 'eligibility'
            ]
            
            # Negative indicators (should not be present)
            cover_letter_indicators = [
                'proposal', 'rfp', 'government contract', 'solicitation'
            ]
            
            positive_count = sum(1 for indicator in tentative_indicators if indicator in content_lower)
            negative_count = sum(1 for indicator in cover_letter_indicators if indicator in content_lower)
            
            print(f"   Tentative letter indicators: {positive_count}/{len(tentative_indicators)}")
            print(f"   Cover letter indicators: {negative_count}/{len(cover_letter_indicators)}")
            
            if positive_count >= 3 and negative_count == 0:
                print(f"   ✅ Content appears to be a proper tentative offer letter")
                return True
            elif negative_count > 0:
                print(f"   ⚠️ Content may contain cover letter elements")
                print(f"   First 200 chars: {content[:200]}...")
                return False
            else:
                print(f"   ⚠️ Content may not be specific enough for tentative offer letter")
                print(f"   First 200 chars: {content[:200]}...")
                return False
        else:
            print(f"   ❌ No content generated")
            return False
            
    except Exception as e:
        print(f"❌ Letter type test failed: {e}")
        return False


async def test_criticism_queue_fix():
    """Test that criticism queue database operations work correctly"""
    
    print("\n🧪 Testing Criticism Queue Fix")
    print("=" * 40)
    
    try:
        async for db in get_customer_db():
            print("✅ Database connection established")
            
            # Test creating a criticism queue item
            print("🔄 Testing criticism queue item creation...")
            
            queue_item = await ProposalCriticismQueueService.create_item(
                db=db,
                opportunity_id="TEST_CRITICISM_001",
                tenant_id="TEST_TENANT_001",
                client_short_name="TestClient",
                priority=1,
                analysis_type="full",
                submitted_by="test_user"
            )
            
            if queue_item:
                print(f"✅ Created criticism queue item with ID: {queue_item.id}")
                
                # Test getting the item back
                retrieved_item = await ProposalCriticismQueueService.get_by_id(db, queue_item.id)
                
                if retrieved_item:
                    print(f"✅ Retrieved criticism queue item: {retrieved_item.opportunity_id}")
                    
                    # Test updating status
                    success = await ProposalCriticismQueueService.update_status(
                        db, queue_item.id, "PROCESSING"
                    )
                    
                    if success:
                        print(f"✅ Updated criticism queue item status")
                        
                        # Clean up - delete the test item
                        deleted = await ProposalCriticismQueueService.delete_item(db, queue_item.id)
                        if deleted:
                            print(f"✅ Cleaned up test criticism queue item")
                        
                        return True
                    else:
                        print(f"❌ Failed to update criticism queue item status")
                        return False
                else:
                    print(f"❌ Failed to retrieve criticism queue item")
                    return False
            else:
                print(f"❌ Failed to create criticism queue item")
                return False
            
            break  # Only test with first database connection
            
    except Exception as e:
        print(f"❌ Criticism queue test failed: {e}")
        return False


async def test_database_model_fix():
    """Test that the database model references are correct"""
    
    print("\n🧪 Testing Database Model Fix")
    print("=" * 40)
    
    try:
        from models.customer_models import CustomOppsTable
        
        # Check that the model has the expected fields
        expected_fields = ['opportunity_id', 'tenant_id', 'draft', 'title', 'last_mod_date']
        
        print("🔄 Checking CustomOppsTable model fields...")
        
        for field in expected_fields:
            if hasattr(CustomOppsTable, field):
                print(f"   ✅ {field} field exists")
            else:
                print(f"   ❌ {field} field missing")
                return False
        
        print("✅ All required fields present in CustomOppsTable model")
        return True
        
    except Exception as e:
        print(f"❌ Database model test failed: {e}")
        return False


async def main():
    """Main test function"""
    
    print("🚀 Testing Both Fixes")
    print("=" * 60)
    print("Testing:")
    print("1. Letter type fix (tentative/contingent letters)")
    print("2. Criticism queue database fix")
    print("3. Database model references")
    print("=" * 60)
    
    test_results = []
    
    # Run all tests
    tests = [
        ("Letter Type Fix", test_letter_type_fix),
        ("Criticism Queue Fix", test_criticism_queue_fix),
        ("Database Model Fix", test_database_model_fix)
    ]
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            test_results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} | {test_name}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Letter type fix working - tentative/contingent letters generate correctly")
        print("✅ Criticism queue fix working - database operations successful")
        print("✅ Database model references corrected")
        print("🎯 Both issues resolved!")
    else:
        print("⚠️ SOME TESTS FAILED!")
        print("🔧 Review failed tests and address remaining issues")
    
    print("\n💡 Next Steps:")
    print("1. Test with real proposal generation")
    print("2. Monitor criticism queue processing")
    print("3. Verify letter content quality in production")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
