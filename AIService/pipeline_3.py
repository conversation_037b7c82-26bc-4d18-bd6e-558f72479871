import asyncio
from services.proposal.content_compliance import ContentComplianceService
from services.proposal.utilities import ProposalUtilities
from services.proposal.outline import ProposalOutlineService

async def main():
    opportunity_id = "iRiYNgd8RC"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    source = "custom"
    is_rfp = True

    proposal_outline_service = ProposalOutlineService()
    content_service = ContentComplianceService()
    structure_compliance = ProposalUtilities.read_json_from_file("structure-compliance.json")
    
    #This is an array of the expected Volumes
    structure_compliance = structure_compliance.get("structure")

    if structure_compliance is None:
        return

    content_compliance = ProposalUtilities.read_text_from_file("content-compliance.txt")

    structure = ProposalUtilities.read_json_from_file("structure-compliance.json")

    structuress = structure.get("structure", [])
    '''
    print("\nGenerating Content Compliance...")
    content_result = await content_service.generate_content_compliance(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source,
        is_rfp=is_rfp,
    )
    print("\n--- Content Compliance ---")
    print(content_result["content"])
    '''

    for volume in structuress:
        table_of_contents = await proposal_outline_service.generate_table_of_contents(
            opportunity_id=opportunity_id,
            volume_information=volume,
            content_compliance=content_compliance,
            tenant_id=tenant_id,
            source=source,
            is_rfp=True
        )
        print(table_of_contents)




if __name__ == "__main__":
    asyncio.run(main())