#!/usr/bin/env python3
"""
Test script to verify the outline flow implementation in proposal scheduler.
This script tests the new flow where proposal generation waits for outline completion.
"""

import asyncio
import json
import sys
import os

# Add the AIService directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import only the necessary components for testing
from models.customer_models import ProposalQueue, ProposalOutlineQueue
from database import get_customer_db, get_kontratar_db
from loguru import logger


async def test_outline_flow():
    """Test the outline flow implementation"""

    # Test data
    test_opps_id = "test-outline-flow-123"
    test_tenant_id = "test-tenant"

    logger.info("Starting outline flow test...")

    # Test 1: Create a test outline queue item
    logger.info("Test 1: Creating test outline queue item")
    async for db in get_kontratar_db():
        # Clean up any existing test data first
        from sqlalchemy import delete
        delete_stmt = delete(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == test_opps_id,
            ProposalOutlineQueue.tenant_id == test_tenant_id
        )
        await db.execute(delete_stmt)
        await db.commit()

        # Create new test item
        test_outline_item = ProposalOutlineQueue(
            opps_id=test_opps_id,
            tenant_id=test_tenant_id,
            status="NEW",
            outline_type="SAM",
            first_request=True
        )
        db.add(test_outline_item)
        await db.commit()
        break
    logger.info("✓ Test 1 passed: Test outline queue item created")
    
    # Test 2: Verify the outline queue item exists and has correct properties
    logger.info("Test 2: Verifying outline queue item properties")
    async for db in get_kontratar_db():
        from sqlalchemy import select
        query = select(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == test_opps_id,
            ProposalOutlineQueue.tenant_id == test_tenant_id
        )
        result = await db.execute(query)
        queue_item = result.scalar_one_or_none()
        assert queue_item is not None, "Outline queue item should exist"
        assert queue_item.status == "NEW", f"Status should be NEW, got {queue_item.status}"
        assert queue_item.outline_type == "SAM", f"Type should be SAM, got {queue_item.outline_type}"
        assert queue_item.first_request == True, f"first_request should be True, got {queue_item.first_request}"
        break
    logger.info("✓ Test 2 passed: Outline queue item has correct properties")
    
    # Test 3: Update status to PROCESSING and verify
    logger.info("Test 3: Testing PROCESSING status")
    async for db in get_kontratar_db():
        from sqlalchemy import update
        update_stmt = update(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == test_opps_id,
            ProposalOutlineQueue.tenant_id == test_tenant_id
        ).values(status="PROCESSING")
        await db.execute(update_stmt)
        await db.commit()

        # Verify the update
        query = select(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == test_opps_id,
            ProposalOutlineQueue.tenant_id == test_tenant_id
        )
        result = await db.execute(query)
        queue_item = result.scalar_one_or_none()
        assert queue_item.status == "PROCESSING", f"Status should be PROCESSING, got {queue_item.status}"
        break
    logger.info("✓ Test 3 passed: Status updated to PROCESSING correctly")
    
    # Test 4: Check outline availability when item is FAILED
    logger.info("Test 4: Checking outline availability when item is FAILED")
    async for db in get_kontratar_db():
        update_stmt = update(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == test_opps_id,
            ProposalOutlineQueue.tenant_id == test_tenant_id
        ).values(status="FAILED", error_message="Test failure")
        await db.execute(update_stmt)
        await db.commit()
        break
    
    outline_ready = await scheduler_service._check_outline_availability(
        test_opps_id, test_tenant_id, test_opportunity_type
    )
    assert not outline_ready, "Outline should not be ready when FAILED"
    logger.info("✓ Test 4 passed: Outline correctly identified as not ready when FAILED")
    
    # Test 5: Check outline availability when item is COMPLETED
    logger.info("Test 5: Checking outline availability when item is COMPLETED")
    async for db in get_kontratar_db():
        update_stmt = update(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == test_opps_id,
            ProposalOutlineQueue.tenant_id == test_tenant_id
        ).values(status="COMPLETED")
        await db.execute(update_stmt)
        await db.commit()
        break
    
    outline_ready = await scheduler_service._check_outline_availability(
        test_opps_id, test_tenant_id, test_opportunity_type
    )
    assert outline_ready, "Outline should be ready when COMPLETED"
    logger.info("✓ Test 5 passed: Outline correctly identified as ready when COMPLETED")
    
    # Test 6: Test source type determination
    logger.info("Test 6: Testing source type determination")
    assert scheduler_service._determine_source_type("sam") == "sam"
    assert scheduler_service._determine_source_type("SAM") == "sam"
    assert scheduler_service._determine_source_type("sam.gov") == "sam"
    assert scheduler_service._determine_source_type("ebuy") == "ebuy"
    assert scheduler_service._determine_source_type("ebuy.gsa.gov") == "ebuy"
    assert scheduler_service._determine_source_type("custom") == "custom"
    assert scheduler_service._determine_source_type("anything_else") == "custom"
    assert scheduler_service._determine_source_type("") == "custom"
    assert scheduler_service._determine_source_type(None) == "custom"
    logger.info("✓ Test 6 passed: Source type determination working correctly")
    
    # Cleanup: Remove test data
    logger.info("Cleaning up test data...")
    async for db in get_kontratar_db():
        from sqlalchemy import delete
        delete_stmt = delete(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == test_opps_id,
            ProposalOutlineQueue.tenant_id == test_tenant_id
        )
        await db.execute(delete_stmt)
        await db.commit()
        break
    
    logger.info("✓ All tests passed! Outline flow implementation is working correctly.")


async def test_proposal_processing_with_outline_flow():
    """Test the complete proposal processing flow with outline checking"""
    
    logger.info("Testing complete proposal processing flow...")
    
    # Create a test proposal queue item
    test_job_instruction = json.dumps({
        "opportunityId": "test-proposal-flow-456",
        "tenantId": "test-tenant",
        "opportunityType": "sam",
        "is_rfp": False,
        "clientShortName": "Test Client"
    })
    
    async for db in get_customer_db():
        test_proposal = ProposalQueue(
            job_id="test-job-456",
            job_instruction=test_job_instruction,
            job_submitted_by="test-user",
            status="N",
            opps_id="test-proposal-flow-456",
            tenant_id="test-tenant",
            request_type=1
        )
        db.add(test_proposal)
        await db.commit()
        break
    
    # Initialize scheduler and process the item
    scheduler_service = ProposalSchedulerService()
    
    # This should create an outline queue item and set status to PENDING
    await scheduler_service._process_proposal_item(test_proposal)
    
    # Check that the proposal status was updated to PENDING
    async for db in get_customer_db():
        from sqlalchemy import select
        query = select(ProposalQueue).where(ProposalQueue.job_id == "test-job-456")
        result = await db.execute(query)
        updated_proposal = result.scalar_one_or_none()
        assert updated_proposal.status == "PENDING", f"Expected PENDING, got {updated_proposal.status}"
        assert updated_proposal.next_state == "WAITING_FOR_OUTLINE", f"Expected WAITING_FOR_OUTLINE, got {updated_proposal.next_state}"
        break
    
    # Check that outline queue item was created
    async for db in get_kontratar_db():
        query = select(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == "test-proposal-flow-456",
            ProposalOutlineQueue.tenant_id == "test-tenant"
        )
        result = await db.execute(query)
        outline_item = result.scalar_one_or_none()
        assert outline_item is not None, "Outline queue item should have been created"
        assert outline_item.status == "NEW", f"Expected NEW, got {outline_item.status}"
        break
    
    logger.info("✓ Proposal processing flow test passed!")
    
    # Cleanup
    async for db in get_customer_db():
        from sqlalchemy import delete
        delete_stmt = delete(ProposalQueue).where(ProposalQueue.job_id == "test-job-456")
        await db.execute(delete_stmt)
        await db.commit()
        break
    
    async for db in get_kontratar_db():
        delete_stmt = delete(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == "test-proposal-flow-456"
        )
        await db.execute(delete_stmt)
        await db.commit()
        break


async def main():
    """Run all tests"""
    try:
        await test_outline_flow()
        await test_proposal_processing_with_outline_flow()
        logger.info("🎉 All tests completed successfully!")
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
