# AI Service Environment Configuration Template

#if ENVIRONMENT=development
# Development Environment Settings
DEBUG=true
#else
# Production Environment Settings
DEBUG=&debug;
#endif

# Database Configuration - Kontratar Database
KONTRATAR_DB_HOST=&kontratar_db_host;
KONTRATAR_DB_PORT=&kontratar_db_port;
KONTRATAR_DB_NAME=&kontratar_db_name;
KONTRATAR_DB_USER=&kontratar_db_user;
KONTRATAR_DB_PASSWORD=&kontratar_db_password;

# Database Configuration - Customer Database
CUSTOMER_DB_HOST=&customer_db_host;
CUSTOMER_DB_PORT=&customer_db_port;
CUSTOMER_DB_NAME=&customer_db_name;
CUSTOMER_DB_USER=&customer_db_user;
CUSTOMER_DB_PASSWORD=&customer_db_password;


# Application Configuration
APP_HOST=&app_host;
APP_PORT=&app_port;

# ChromaDB Configuration
CHROMADB_PROTOCOL=&chromadb_protocol;
CHROMADB_SERVER_NAME=&chromadb_server_name;
CHROMADB_PORT_1=&chromadb_port_1;
CHROMADB_PORT_2=&chromadb_port_2;
CHROMADB_PORT_3=&chromadb_port_3;
CHROMADB_PORT_4=&chromadb_port_4;
CHROMADB_PORT_5=&chromadb_port_5;

# Legacy Kontratar DB fields (for compatibility)
DB_KONTRATAR_HOST=&db_kontratar_host;
DB_KONTRATAR_NAME=&db_kontratar_name;
DB_KONTRATAR_PASSWORD=&db_kontratar_password;
DB_KONTRATAR_PORT=&db_kontratar_port;
DB_KONTRATAR_USER=&db_kontratar_user;

# LangChain Configuration
LANGCHAIN_API_KEY=&langchain_api_key;
LANGCHAIN_PROJECT=&langchain_project;
LANGCHAIN_TRACING_V2=&langchain_tracing_v2;

# Scheduler Configuration
SCHEDULER_INTERVAL_SECONDS=&scheduler_interval_seconds;

GOOGLE_CLIENT_ID=&google_client_id;
GOOGLE_CLIENT_SECRET=&google_client_secret;

#if OS=Linux
# Linux-specific environment variables
#PYTHONPATH=/usr/local/lib/python3.10/site-packages
#endif

#if OS=Windows
# Windows-specific environment variables
#PYTHONPATH=C:\Python310\Lib\site-packages
#endif
