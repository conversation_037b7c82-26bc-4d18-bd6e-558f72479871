import asyncio
import json
import os
import time
from datetime import datetime
from sqlalchemy import select, desc
from controllers.customer.tenant_controller import TenantController
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from controllers.kontratar.sam_opps_table_coontroller import OppsController
from controllers.kontratar.ebuy_opps_controller import EBUYOppsController
from services.proposal.utilities import ProposalUtilities
from services.proposal.outline import ProposalOutlineService
from services.proposal.rfi.rfi_generation_service import RFIGenerationService
from services.proposal.rfp.generate_rfp import RFPGenerationService
from services.exports.generate_pdf import PDFGenerator
from controllers.customer.proposals_format_queue_controller import ProposalsFormatQueueController
from database import get_customer_db, get_kontratar_db
from models.customer_models import ProposalOutlineQueue, ProposalsFormatQueue
from loguru import logger

async def determine_source_type(opportunity_id: str) -> str:
    """Determine source type based on opportunity ID format and database presence"""
    try:
        # Check if it's a 32-character SAM ID
        if len(opportunity_id) == 32:
            async for db in get_kontratar_db():
                from sqlalchemy import select
                from models.kontratar_models import OppsTable
                query = select(OppsTable).where(OppsTable.notice_id == opportunity_id)
                result = await db.execute(query)
                record = result.scalar_one_or_none()
                if record:
                    return "sam"
                break

        # Check if it's in EBUY
        async for db in get_kontratar_db():
            from sqlalchemy import select
            from models.kontratar_models import EBUYOppsTable
            query = select(EBUYOppsTable).where(EBUYOppsTable.rfq_id == opportunity_id)
            result = await db.execute(query)
            record = result.scalar_one_or_none()
            if record:
                return "ebuy"
            break

        # Default to custom
        return "custom"
    except Exception as e:
        logger.error(f"Error determining source type for {opportunity_id}: {e}")
        return "custom"


async def check_existing_outlines(opportunity_id: str, source: str) -> bool:
    """Check if outlines already exist in the database"""
    try:
        if source == "sam":
            async for db in get_kontratar_db():
                from sqlalchemy import select
                from models.kontratar_models import OppsTable
                query = select(OppsTable).where(OppsTable.notice_id == opportunity_id)
                result = await db.execute(query)
                record = result.scalar_one_or_none()
                if record and any(
                    getattr(record, f"proposal_outline_{i}") is not None and
                    getattr(record, f"proposal_outline_{i}") != ""
                    for i in range(1, 6)
                ):
                    return True
                break
        elif source == "ebuy":
            async for db in get_kontratar_db():
                from sqlalchemy import select
                from models.kontratar_models import EBUYOppsTable
                query = select(EBUYOppsTable).where(EBUYOppsTable.rfq_id == opportunity_id)
                result = await db.execute(query)
                record = result.scalar_one_or_none()
                if record and getattr(record, "proposal_outline") is not None and getattr(record, "proposal_outline") != "":
                    return True
                break
        elif source == "custom":
            async for db in get_customer_db():
                from sqlalchemy import select
                from models.customer_models import CustomOppsTable
                query = select(CustomOppsTable).where(CustomOppsTable.opportunity_id == opportunity_id)
                result = await db.execute(query)
                record = result.scalar_one_or_none()
                if record and any(
                    getattr(record, f"proposal_outline_{i}") is not None and
                    getattr(record, f"proposal_outline_{i}") != ""
                    for i in range(1, 6)
                ):
                    return True
                break
        return False
    except Exception as e:
        logger.error(f"Error checking existing outlines for {opportunity_id}: {e}")
        return False


async def export_proposal_to_pdf(opportunity_id: str, tenant_id: str, client_short_name: str, source: str) -> dict:
    """
    Export the generated proposal to PDF using the latest format queue entry.
    """
    try:
        logger.info(f"Starting PDF export for {opportunity_id}")

        format_queue_item = None

        # Get the latest format queue entry for this opportunity
        async for db in get_customer_db():
            query = select(ProposalsFormatQueue).where(
                ProposalsFormatQueue.opps_id == opportunity_id,
                ProposalsFormatQueue.tenant_id == tenant_id
            ).order_by(desc(ProposalsFormatQueue.created_date)).limit(1)

            result = await db.execute(query)
            format_queue_item = result.scalar_one_or_none()

            if not format_queue_item:
                logger.warning(f"No format queue entry found for {opportunity_id}")
                return {"error": "No proposal found in format queue for PDF export"}

            logger.info(f"Found format queue entry with ID: {format_queue_item.id}")
            break

        if not format_queue_item:
            return {"error": "No format queue entry found"}

        # Initialize PDF generator
        pdf_generator = PDFGenerator()

        # Generate timestamp for filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create filename
        proposal_type = "RFP" if format_queue_item.is_rfp else "RFI"
        filename = f"{proposal_type}_Draft_{opportunity_id}_{client_short_name}_{timestamp}.pdf"

        # Set output directory
        output_dir = "generated-pdfs"
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, filename)

        logger.info(f"Generating PDF: {output_path}")

        # Extract markdown content from format queue
        markdown_content = format_queue_item.markdown_content or ""
        if not markdown_content:
            logger.warning("No markdown content found in format queue item")
            return {"error": "No markdown content available for PDF generation"}

        # Extract table of contents if available
        toc_data = None
        if format_queue_item.toc_text:
            try:
                toc_data = json.loads(format_queue_item.toc_text)
            except json.JSONDecodeError:
                logger.warning("Failed to parse TOC data from format queue")

        # Generate PDF using the PDFGenerator
        file_path, success_message = pdf_generator.generate_pdf(
            markdown_content=markdown_content,
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            toc_data=toc_data,
            output_dir=output_dir,
            volume_number=1
        )

        logger.info(f"PDF generated successfully: {file_path}")

        return {
            "success": f"PDF exported successfully to {file_path}",
            "filename": os.path.basename(file_path),
            "path": file_path,
            "format_queue_id": format_queue_item.id,
            "message": success_message
        }

    except Exception as e:
        logger.error(f"Error exporting proposal to PDF: {e}")
        return {"error": f"PDF export failed: {str(e)}"}


async def wait_for_format_queue_completion(opportunity_id: str, tenant_id: str, max_wait_seconds: int = 60) -> bool:
    """
    Wait for the proposal to appear in the format queue after generation.
    """
    import time
    start_time = time.time()

    while time.time() - start_time < max_wait_seconds:
        try:
            async for db in get_customer_db():
                from sqlalchemy import select, desc
                from models.customer_models import ProposalsFormatQueue

                query = select(ProposalsFormatQueue).where(
                    ProposalsFormatQueue.opps_id == opportunity_id,
                    ProposalsFormatQueue.tenant_id == tenant_id
                ).order_by(desc(ProposalsFormatQueue.created_date)).limit(1)

                result = await db.execute(query)
                format_queue_item = result.scalar_one_or_none()

                if format_queue_item:
                    logger.info(f"Format queue entry found after {time.time() - start_time:.1f} seconds")
                    return True
                break

            # Wait a bit before checking again
            await asyncio.sleep(2)

        except Exception as e:
            logger.error(f"Error checking format queue: {e}")
            return False

    logger.warning(f"Format queue entry not found after {max_wait_seconds} seconds")
    return False


async def fast_proposal_generation(opportunity_id: str, tenant_id: str, client_short_name: str, is_rfp: bool = False):
    """
    Fast proposal generation that checks tables directly and uses the outline queue system.
    Similar to scheduler-based generation but optimized for speed.
    """
    logger.info(f"Starting fast proposal generation for {opportunity_id}")

    # Step 1: Determine source type
    source = await determine_source_type(opportunity_id)
    logger.info(f"Determined source type: {source}")

    # Step 2: Check if outlines already exist
    outlines_exist = await check_existing_outlines(opportunity_id, source)
    logger.info(f"Outlines exist: {outlines_exist}")

    # Step 3: If no outlines, check outline queue and handle accordingly
    if not outlines_exist:
        logger.info("No existing outlines found, checking outline queue...")

        # Check outline queue status
        async for db in get_kontratar_db():
            from sqlalchemy import select
            query = select(ProposalOutlineQueue).where(
                ProposalOutlineQueue.opps_id == opportunity_id,
                ProposalOutlineQueue.tenant_id == tenant_id
            ).order_by(ProposalOutlineQueue.created_date.desc())
            result = await db.execute(query)
            queue_item = result.scalar_one_or_none()

            if queue_item:
                if queue_item.status == "PROCESSING":
                    logger.warning("Outline generation in progress, cannot proceed with fast generation")
                    return {"error": "Outline generation in progress, please try again later"}
                elif queue_item.status == "FAILED":
                    logger.warning("Outline generation failed, cannot proceed")
                    return {"error": "Outline generation failed, cannot proceed"}
                elif queue_item.status == "NEW":
                    logger.warning("Outline generation queued but not started")
                    return {"error": "Outline generation queued, please try again later"}
            else:
                # Add to queue for future processing
                logger.info("Adding to outline queue for future processing")
                new_queue_item = ProposalOutlineQueue(
                    opps_id=opportunity_id,
                    tenant_id=tenant_id,
                    status="NEW",
                    outline_type=source.upper(),
                    first_request=True
                )
                db.add(new_queue_item)
                await db.commit()
                return {"error": "Outline generation queued, please try again later"}
            break

    # Step 4: Generate proposal using appropriate service
    try:
        if is_rfp:
            logger.info("Generating RFP proposal")
            rfp_service = RFPGenerationService()

            # Create job instruction similar to scheduler
            job_instruction = json.dumps({
                "opportunityId": opportunity_id,
                "tenantId": tenant_id,
                "clientShortName": client_short_name,
                "opportunityType": source,
                "is_rfp": True,
                "setForReview": True,
                "sourceDocuments": [],
                "profileId": None,
                "gradingCriteria": "",
                "coverPage": None,
                "exportType": None
            })

            await rfp_service.generate_rfp(job_instruction, "pipeline_4_fast")
            logger.info("RFP generation completed successfully")
            return {"success": "RFP proposal generated successfully"}

        else:
            logger.info("Generating RFI proposal")
            rfi_service = RFIGenerationService()

            # Create job instruction similar to scheduler
            job_instruction = json.dumps({
                "opportunityId": opportunity_id,
                "tenantId": tenant_id,
                "clientShortName": client_short_name,
                "opportunityType": source,
                "is_rfp": False,
                "setForReview": True,
                "sourceDocuments": [],
                "profileId": None,
                "gradingCriteria": "",
                "coverPage": None,
                "exportType": None
            })

            await rfi_service.generate_rfi(job_instruction, "pipeline_4_fast")
            logger.info("RFI generation completed successfully")
            return {"success": "RFI proposal generated successfully"}

    except Exception as e:
        logger.error(f"Error during proposal generation: {e}")
        return {"error": f"Proposal generation failed: {str(e)}"}


async def main():
    """
    Main function for fast proposal generation pipeline.
    Checks tables directly and uses the outline queue system for coordination.
    """

    # Configuration - modify these as needed
    # opportunity_id = "iRiYNgd8RC"  # Can be SAM, EBUY, or Custom ID
    # opportunity_id = "tA30VTcyMj"  # Alternative test ID
    opportunity_id = "vSe1unlCj9"  # Alternative test ID

    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    client_short_name = "adeptengineeringsolutions"
    is_rfp = False  # Set to True for RFP, False for RFI
    export_to_pdf = True  # Set to True to export PDF after generation

    logger.info("="*80)
    logger.info("FAST PROPOSAL GENERATION PIPELINE")
    logger.info("="*80)
    logger.info(f"Opportunity ID: {opportunity_id}")
    logger.info(f"Tenant ID: {tenant_id}")
    logger.info(f"Client: {client_short_name}")
    logger.info(f"Type: {'RFP' if is_rfp else 'RFI'}")
    logger.info("="*80)

    # Generate proposal
    result = await fast_proposal_generation(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        client_short_name=client_short_name,
        is_rfp=is_rfp
    )

    # Display result
    if "success" in result:
        logger.info(f"✅ SUCCESS: {result['success']}")
        logger.info("Check the proposals_in_review or proposals_format_queue tables for the generated proposal")

        # Export to PDF if requested
        if export_to_pdf:
            logger.info("="*80)
            logger.info("STARTING PDF EXPORT")
            logger.info("="*80)

            # Wait for the proposal to appear in format queue
            logger.info("Waiting for proposal to appear in format queue...")
            format_ready = await wait_for_format_queue_completion(opportunity_id, tenant_id, max_wait_seconds=60)

            if format_ready:
                # Export to PDF
                pdf_result = await export_proposal_to_pdf(
                    opportunity_id=opportunity_id,
                    tenant_id=tenant_id,
                    client_short_name=client_short_name,
                    source=await determine_source_type(opportunity_id)
                )

                if "success" in pdf_result:
                    logger.info(f"✅ PDF EXPORT SUCCESS: {pdf_result['success']}")
                    logger.info(f"📄 PDF File: {pdf_result['filename']}")
                    logger.info(f"📁 PDF Path: {pdf_result['path']}")
                else:
                    logger.warning(f"⚠️  PDF EXPORT FAILED: {pdf_result['error']}")
            else:
                logger.warning("⚠️  PDF EXPORT SKIPPED: Proposal not found in format queue within timeout")

    else:
        logger.warning(f"⚠️  RESULT: {result['error']}")
        logger.info("You may need to wait for outline generation to complete or check the outline queue status")

    logger.info("="*80)
    logger.info("PIPELINE COMPLETED")
    logger.info("="*80)
    
    


if __name__ == "__main__":
    asyncio.run(main())