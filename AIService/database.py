from config import get_customer_db_url, get_kontratar_db_url
from sqlalchemy import MetaData
from sqlalchemy.ext.asyncio import (AsyncSession, async_sessionmaker,
                                    create_async_engine)
from sqlalchemy.ext.declarative import declarative_base

# Create base class for models
Base = declarative_base()

# Create engines for both databases
kontratar_engine = create_async_engine(
    get_kontratar_db_url(),
    echo=False,
    pool_pre_ping=True,
     pool_recycle=1800,
    pool_timeout=300,
    pool_size=10,
    max_overflow=20  
)

customer_engine = create_async_engine(
    get_customer_db_url(),
    echo=False,
    pool_pre_ping=True,
    pool_recycle=1800,
    pool_timeout=300,
    pool_size=10,
    max_overflow=20
)

# Create session factories
KontratarSessionLocal = async_sessionmaker(
    kontratar_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

CustomerSessionLocal = async_sessionmaker(
    customer_engine,
    class_=AsyncSession,
    expire_on_commit=False
)


async def get_kontratar_db():
    """Dependency to get kontratar database session"""
    async with KontratarSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


async def get_customer_db():
    """Dependency to get customer database session"""
    async with CustomerSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


async def init_db():
    """Initialize database tables"""
    async with kontratar_engine.begin() as conn:
        # Create tables for kontratar database
        await conn.run_sync(Base.metadata.create_all)
    
    async with customer_engine.begin() as conn:
        # Create tables for customer database
        await conn.run_sync(Base.metadata.create_all)


async def close_db():
    """Close database connections"""
    await kontratar_engine.dispose()
    await customer_engine.dispose() 