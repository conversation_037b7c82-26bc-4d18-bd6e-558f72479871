#!/usr/bin/env python3
import asyncio
import json
import sys
import os

# Add the AIService directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.proposal.rfi.rfi_generation_service import RFIGenerationService
from services.proposal.rfp.generate_rfp import RFPGenerationService
from models.customer_models import ProposalOutlineQueue
from database import get_customer_db, get_kontratar_db
from loguru import logger


async def test_rfi_outline_integration():
    """Test RFI generation service integration with outline queue"""
    
    rfi_service = RFIGenerationService()
    
    # Test data
    test_opps_id = ""
    test_tenant_id = ""
    
    logger.info("Testing RFI outline integration...")
    
    # Clean up any existing test data
    async for db in get_kontratar_db():
        from sqlalchemy import delete
        delete_stmt = delete(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == test_opps_id,
            ProposalOutlineQueue.tenant_id == test_tenant_id
        )
        await db.execute(delete_stmt)
        await db.commit()
        break
    
    # Test 1: Check outline queue status when no queue item exists
    logger.info("Test 1: Checking outline queue status when no item exists")
    status = await rfi_service._check_outline_queue_status(test_opps_id, test_tenant_id)
    assert status == "NOT_FOUND", f"Expected NOT_FOUND, got {status}"
    logger.info("✓ Test 1 passed: Queue status correctly identified as NOT_FOUND")
    
    # Test 2: Add to outline queue
    logger.info("Test 2: Adding to outline queue")
    await rfi_service._add_to_outline_queue(test_opps_id, test_tenant_id, "sam")
    
    # Verify it was added
    status = await rfi_service._check_outline_queue_status(test_opps_id, test_tenant_id)
    assert status == "NEW", f"Expected NEW, got {status}"
    logger.info("✓ Test 2 passed: Item added to queue with NEW status")
    
    # Test 3: Check wait_for_outline_or_queue when status is NEW
    logger.info("Test 3: Testing wait_for_outline_or_queue with NEW status")
    is_ready, message = await rfi_service._wait_for_outline_or_queue(test_opps_id, test_tenant_id, "sam")
    assert not is_ready, "Should not be ready when status is NEW"
    assert "queued" in message.lower(), f"Message should mention queuing, got: {message}"
    logger.info("✓ Test 3 passed: Correctly identified as not ready when NEW")
    
    # Test 4: Update status to PROCESSING and test
    logger.info("Test 4: Testing with PROCESSING status")
    async for db in get_kontratar_db():
        from sqlalchemy import update
        update_stmt = update(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == test_opps_id,
            ProposalOutlineQueue.tenant_id == test_tenant_id
        ).values(status="PROCESSING")
        await db.execute(update_stmt)
        await db.commit()
        break
    
    is_ready, message = await rfi_service._wait_for_outline_or_queue(test_opps_id, test_tenant_id, "sam")
    assert not is_ready, "Should not be ready when status is PROCESSING"
    assert "in progress" in message.lower(), f"Message should mention in progress, got: {message}"
    logger.info("✓ Test 4 passed: Correctly identified as not ready when PROCESSING")
    
    # Test 5: Update status to FAILED and test
    logger.info("Test 5: Testing with FAILED status")
    async for db in get_kontratar_db():
        update_stmt = update(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == test_opps_id,
            ProposalOutlineQueue.tenant_id == test_tenant_id
        ).values(status="FAILED", error_message="Test failure")
        await db.execute(update_stmt)
        await db.commit()
        break
    
    is_ready, message = await rfi_service._wait_for_outline_or_queue(test_opps_id, test_tenant_id, "sam")
    assert not is_ready, "Should not be ready when status is FAILED"
    assert "failed" in message.lower(), f"Message should mention failed, got: {message}"
    logger.info("✓ Test 5 passed: Correctly identified as not ready when FAILED")
    
    # Test 6: Update status to COMPLETED and test
    logger.info("Test 6: Testing with COMPLETED status")
    async for db in get_kontratar_db():
        update_stmt = update(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == test_opps_id,
            ProposalOutlineQueue.tenant_id == test_tenant_id
        ).values(status="COMPLETED", error_message=None)
        await db.execute(update_stmt)
        await db.commit()
        break
    
    is_ready, message = await rfi_service._wait_for_outline_or_queue(test_opps_id, test_tenant_id, "sam")
    assert is_ready, "Should be ready when status is COMPLETED"
    assert "ready" in message.lower(), f"Message should mention ready, got: {message}"
    logger.info("✓ Test 6 passed: Correctly identified as ready when COMPLETED")
    
    # Cleanup
    async for db in get_kontratar_db():
        delete_stmt = delete(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == test_opps_id,
            ProposalOutlineQueue.tenant_id == test_tenant_id
        )
        await db.execute(delete_stmt)
        await db.commit()
        break
    
    logger.info("✓ All RFI outline integration tests passed!")


async def test_rfp_outline_integration():
    """Test RFP generation service integration with outline queue"""
    
    rfp_service = RFPGenerationService()
    
    # Test data
    test_opps_id = "test-rfp-outline-integration"
    test_tenant_id = "test-tenant"
    
    logger.info("Testing RFP outline integration...")
    
    # Clean up any existing test data
    async for db in get_kontratar_db():
        from sqlalchemy import delete
        delete_stmt = delete(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == test_opps_id,
            ProposalOutlineQueue.tenant_id == test_tenant_id
        )
        await db.execute(delete_stmt)
        await db.commit()
        break
    
    # Test 1: Check outline queue status when no queue item exists
    logger.info("Test 1: Checking RFP outline queue status when no item exists")
    status = await rfp_service._check_outline_queue_status(test_opps_id, test_tenant_id)
    assert status == "NOT_FOUND", f"Expected NOT_FOUND, got {status}"
    logger.info("✓ Test 1 passed: RFP queue status correctly identified as NOT_FOUND")
    
    # Test 2: Add to outline queue
    logger.info("Test 2: Adding to RFP outline queue")
    await rfp_service._add_to_outline_queue(test_opps_id, test_tenant_id, "sam")
    
    # Verify it was added
    status = await rfp_service._check_outline_queue_status(test_opps_id, test_tenant_id)
    assert status == "NEW", f"Expected NEW, got {status}"
    logger.info("✓ Test 2 passed: RFP item added to queue with NEW status")
    
    # Test 3: Check wait_for_outline_or_queue for volume 1
    logger.info("Test 3: Testing RFP wait_for_outline_or_queue with NEW status")
    is_ready, message = await rfp_service._wait_for_outline_or_queue(test_opps_id, test_tenant_id, "sam", 1)
    assert not is_ready, "Should not be ready when status is NEW"
    assert "queued" in message.lower(), f"Message should mention queuing, got: {message}"
    logger.info("✓ Test 3 passed: RFP correctly identified as not ready when NEW")
    
    # Test 4: Update status to COMPLETED and test
    logger.info("Test 4: Testing RFP with COMPLETED status")
    async for db in get_kontratar_db():
        from sqlalchemy import update
        update_stmt = update(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == test_opps_id,
            ProposalOutlineQueue.tenant_id == test_tenant_id
        ).values(status="COMPLETED")
        await db.execute(update_stmt)
        await db.commit()
        break
    
    is_ready, message = await rfp_service._wait_for_outline_or_queue(test_opps_id, test_tenant_id, "sam", 1)
    assert is_ready, "Should be ready when status is COMPLETED"
    assert "ready" in message.lower(), f"Message should mention ready, got: {message}"
    logger.info("✓ Test 4 passed: RFP correctly identified as ready when COMPLETED")
    
    # Cleanup
    async for db in get_kontratar_db():
        delete_stmt = delete(ProposalOutlineQueue).where(
            ProposalOutlineQueue.opps_id == test_opps_id,
            ProposalOutlineQueue.tenant_id == test_tenant_id
        )
        await db.execute(delete_stmt)
        await db.commit()
        break
    
    logger.info("✓ All RFP outline integration tests passed!")


async def main():
    """Run all tests"""
    try:
        await test_rfi_outline_integration()
        await test_rfp_outline_integration()
        logger.info("All RFP/RFI outline integration tests completed successfully!")
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
