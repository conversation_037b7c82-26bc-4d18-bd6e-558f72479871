#!/usr/bin/env python3
"""
Deep Investigation of Criticism System Issues

Investigates:
1. Database compliance requirements
2. Proposal data structure and content
3. LLM connectivity and configuration
4. Criticism chain functionality
5. Overall system health
"""

import asyncio
import json
import logging
from sqlalchemy import select, text
from database import get_customer_db
from models.customer_models import CustomOppsTable, ProposalCriticismResults
from services.proposal.criticism_chain import Criticism<PERSON>hain
from langchain_ollama import ChatOllama

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger("CRITICISM_INVESTIGATION")


async def investigate_database_compliance():
    """Investigate compliance requirements in database"""
    
    print("🔍 INVESTIGATING DATABASE COMPLIANCE REQUIREMENTS")
    print("=" * 60)
    
    try:
        async for db in get_customer_db():
            # Check the specific opportunity
            test_opportunity_id = "iRiYNgd8RC"
            
            print(f"📋 Checking opportunity: {test_opportunity_id}")
            
            query = select(CustomOppsTable).where(
                CustomOppsTable.opportunity_id == test_opportunity_id
            )
            
            result = await db.execute(query)
            opportunity = result.scalars().first()
            
            if not opportunity:
                print(f"❌ Opportunity {test_opportunity_id} not found in database")
                return False
            
            print(f"✅ Opportunity found: {opportunity.title}")
            
            # Check compliance fields
            compliance_fields = {
                'structure_compliance': opportunity.structure_compliance,
                'content_compliance': opportunity.content_compliance,
                'format_compliance': opportunity.format_compliance
            }
            
            print(f"\n📊 COMPLIANCE FIELDS STATUS:")
            for field_name, field_value in compliance_fields.items():
                if field_value and field_value.strip():
                    print(f"   ✅ {field_name}: {len(field_value)} characters")
                    print(f"      Preview: {field_value[:100]}...")
                else:
                    print(f"   ❌ {field_name}: Empty or None")
            
            # Check if draft exists
            if opportunity.draft:
                try:
                    draft_data = json.loads(opportunity.draft)
                    print(f"\n📄 DRAFT STATUS:")
                    print(f"   ✅ Draft exists: {len(opportunity.draft)} characters")
                    print(f"   📊 Draft type: {type(draft_data)}")
                    
                    if isinstance(draft_data, list):
                        print(f"   📋 Draft sections: {len(draft_data)}")
                    elif isinstance(draft_data, dict):
                        print(f"   📋 Draft keys: {list(draft_data.keys())}")
                        if 'draft' in draft_data:
                            print(f"   📋 Nested draft sections: {len(draft_data['draft'])}")
                    
                except json.JSONDecodeError as e:
                    print(f"   ❌ Draft JSON invalid: {e}")
            else:
                print(f"\n❌ No draft found for opportunity")
            
            # Check other opportunities for comparison
            print(f"\n🔍 CHECKING OTHER OPPORTUNITIES FOR COMPLIANCE DATA:")
            
            sample_query = select(CustomOppsTable).where(
                CustomOppsTable.structure_compliance.isnot(None)
            ).limit(5)
            
            sample_result = await db.execute(sample_query)
            sample_opportunities = sample_result.scalars().all()
            
            if sample_opportunities:
                print(f"   ✅ Found {len(sample_opportunities)} opportunities with structure compliance")
                for opp in sample_opportunities:
                    print(f"      - {opp.opportunity_id}: {opp.title[:50]}...")
            else:
                print(f"   ❌ No opportunities found with structure compliance data")
            
            break
            
        return True
        
    except Exception as e:
        print(f"❌ Database investigation failed: {e}")
        return False


async def investigate_llm_connectivity():
    """Investigate LLM connectivity and configuration"""
    
    print(f"\n🔍 INVESTIGATING LLM CONNECTIVITY")
    print("=" * 50)
    
    try:
        # Test different LLM endpoints
        llm_endpoints = [
            "http://ai.kontratar.com:11434",
            "http://127.0.0.1:11434",
            "http://localhost:11434"
        ]
        
        for endpoint in llm_endpoints:
            print(f"\n🔗 Testing endpoint: {endpoint}")
            
            try:
                llm = ChatOllama(
                    model="gemma3:27b",
                    temperature=0.0,
                    base_url=endpoint,
                    timeout=10
                )
                
                # Simple test message
                test_messages = [
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": "Say 'Hello' if you can hear me."}
                ]
                
                response = llm.invoke(test_messages)
                print(f"   ✅ Connection successful")
                print(f"   📝 Response: {str(response.content)[:100]}...")
                
                return True
                
            except Exception as e:
                print(f"   ❌ Connection failed: {e}")
        
        print(f"\n❌ All LLM endpoints failed")
        return False
        
    except Exception as e:
        print(f"❌ LLM investigation failed: {e}")
        return False


async def investigate_criticism_chain():
    """Investigate criticism chain functionality"""
    
    print(f"\n🔍 INVESTIGATING CRITICISM CHAIN")
    print("=" * 45)
    
    try:
        criticism_chain = CriticismChain()
        
        # Test with minimal data
        test_proposal = {
            'draft': {
                'draft': [
                    {
                        'title': 'Test Section',
                        'content': 'This is a test proposal section for criticism analysis.'
                    }
                ]
            }
        }
        
        test_compliance = {
            'requirements': 'The proposal must include clear objectives and methodology.'
        }
        
        print(f"🧪 Testing criticism chain with minimal data...")
        
        try:
            # Test structure criticism
            structure_result = criticism_chain.criticize_structure(test_proposal, test_compliance)
            print(f"   ✅ Structure criticism: Score {structure_result.get('score', 'N/A')}")
            
        except Exception as e:
            print(f"   ❌ Structure criticism failed: {e}")
        
        try:
            # Test content criticism
            content_result = criticism_chain.criticize_content(test_proposal, test_compliance)
            print(f"   ✅ Content criticism: Score {content_result.get('score', 'N/A')}")
            
        except Exception as e:
            print(f"   ❌ Content criticism failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Criticism chain investigation failed: {e}")
        return False


async def investigate_criticism_results():
    """Investigate existing criticism results in database"""
    
    print(f"\n🔍 INVESTIGATING CRITICISM RESULTS")
    print("=" * 45)
    
    try:
        async for db in get_customer_db():
            # Check recent criticism results
            query = select(ProposalCriticismResults).order_by(
                ProposalCriticismResults.created_date.desc()
            ).limit(10)
            
            result = await db.execute(query)
            results = result.scalars().all()
            
            if results:
                print(f"✅ Found {len(results)} recent criticism results:")
                
                for i, res in enumerate(results, 1):
                    print(f"\n   {i}. Opportunity: {res.opportunity_id}")
                    print(f"      Created: {res.created_date}")
                    print(f"      Overall Score: {res.overall_score}")
                    print(f"      Total Issues: {res.total_issues}")
                    print(f"      Total Strengths: {res.total_strengths}")
                    
                    # Check if criticism data exists
                    has_structure = bool(res.structure_criticism)
                    has_content = bool(res.content_criticism)
                    has_formatting = bool(res.formatting_criticism)
                    has_technical = bool(res.technical_criticism)
                    
                    print(f"      Criticism Data: S:{has_structure} C:{has_content} F:{has_formatting} T:{has_technical}")
                    
            else:
                print(f"❌ No criticism results found in database")
            
            break
            
        return True
        
    except Exception as e:
        print(f"❌ Criticism results investigation failed: {e}")
        return False


async def investigate_system_configuration():
    """Investigate system configuration and environment"""
    
    print(f"\n🔍 INVESTIGATING SYSTEM CONFIGURATION")
    print("=" * 50)
    
    try:
        # Check database connection
        async for db in get_customer_db():
            print(f"✅ Database connection successful")
            
            # Check table existence
            tables_to_check = [
                'opportunity.custom_oppstable',
                'opportunity.proposal_criticism_results',
                'opportunity.proposal_criticism_queue'
            ]
            
            for table_name in tables_to_check:
                try:
                    count_query = text(f"SELECT COUNT(*) FROM {table_name}")
                    count_result = await db.execute(count_query)
                    count = count_result.scalar()
                    print(f"   ✅ {table_name}: {count} records")
                except Exception as e:
                    print(f"   ❌ {table_name}: Error - {e}")
            
            break
        
        # Check environment variables or configuration
        import os
        
        print(f"\n🔧 ENVIRONMENT CHECK:")
        env_vars = [
            'DATABASE_URL',
            'LLM_API_URL',
            'OLLAMA_HOST'
        ]
        
        for var in env_vars:
            value = os.getenv(var)
            if value:
                print(f"   ✅ {var}: {value[:50]}...")
            else:
                print(f"   ⚠️ {var}: Not set")
        
        return True
        
    except Exception as e:
        print(f"❌ System configuration investigation failed: {e}")
        return False


async def provide_recommendations():
    """Provide recommendations based on investigation"""
    
    print(f"\n💡 RECOMMENDATIONS")
    print("=" * 30)
    
    print(f"""
🎯 IMMEDIATE ACTIONS NEEDED:

1. 📋 POPULATE COMPLIANCE REQUIREMENTS:
   - Add structure_compliance data to opportunities
   - Add content_compliance data to opportunities  
   - Add format_compliance data to opportunities
   
2. 🔗 FIX LLM CONNECTIVITY:
   - Check Ollama server status
   - Verify correct LLM endpoint URL
   - Ensure model 'gemma3:27b' is available
   
3. 🧪 TEST WITH SAMPLE DATA:
   - Create test opportunity with compliance data
   - Run criticism analysis on test data
   - Verify results are meaningful

4. 🔧 SYSTEM IMPROVEMENTS:
   - Add better error handling for missing compliance
   - Add LLM health checks before analysis
   - Add fallback mechanisms for LLM failures

📊 SAMPLE COMPLIANCE DATA TO ADD:

Structure Compliance:
{{
  "required_sections": ["Executive Summary", "Technical Approach", "Management Plan"],
  "max_pages": 50,
  "format_requirements": "12pt font, double-spaced"
}}

Content Compliance:
{{
  "key_requirements": ["Address all SOW tasks", "Include past performance", "Demonstrate understanding"],
  "evaluation_criteria": ["Technical Merit", "Management Capability", "Cost"]
}}

🚀 NEXT STEPS:
1. Run this investigation script
2. Populate compliance data for test opportunity
3. Test criticism analysis with real data
4. Monitor LLM server health
5. Implement improvements based on findings
""")


async def main():
    """Main investigation function"""
    
    print("🔍 DEEP INVESTIGATION: CRITICISM SYSTEM FAILURE")
    print("=" * 70)
    print("Investigating why criticism analysis failed with score 0...")
    print("=" * 70)
    
    investigations = [
        ("Database Compliance Requirements", investigate_database_compliance),
        ("LLM Connectivity", investigate_llm_connectivity),
        ("Criticism Chain Functionality", investigate_criticism_chain),
        ("Criticism Results History", investigate_criticism_results),
        ("System Configuration", investigate_system_configuration)
    ]
    
    results = []
    
    for name, func in investigations:
        try:
            result = await func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} investigation failed: {e}")
            results.append((name, False))
    
    # Summary
    print(f"\n" + "=" * 70)
    print("📊 INVESTIGATION SUMMARY")
    print("=" * 70)
    
    for name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {name}")
    
    await provide_recommendations()
    
    print("\n" + "=" * 70)
    print("🎯 INVESTIGATION COMPLETE")
    print("=" * 70)


if __name__ == "__main__":
    asyncio.run(main())
