#!/usr/bin/env python3
"""
Debug Criticism System Data Flow

Investigates the exact data structures and flow:
1. What proposal data looks like in database
2. What criticism chain expects vs receives
3. What compliance data exists
4. What results are stored
"""

import asyncio
import json
import logging
from sqlalchemy import select
from database import get_customer_db
from models.customer_models import CustomOppsTable, ProposalCriticismResults

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("CRITICISM_DEBUG")


async def debug_proposal_data_structure():
    """Debug the actual proposal data structure in database"""
    
    print("🔍 DEBUGGING PROPOSAL DATA STRUCTURE")
    print("=" * 50)
    
    try:
        async for db in get_customer_db():
            # Get the specific opportunity that failed
            opportunity_id = "iRiYNgd8RC"
            
            query = select(CustomOppsTable).where(
                CustomOppsTable.opportunity_id == opportunity_id
            )
            
            result = await db.execute(query)
            opportunity = result.scalars().first()
            
            if not opportunity:
                print(f"❌ Opportunity {opportunity_id} not found")
                return False
            
            print(f"✅ Found opportunity: {opportunity.title}")
            
            # Check draft structure
            if opportunity.draft:
                print(f"\n📄 DRAFT ANALYSIS:")
                print(f"   Raw length: {len(opportunity.draft)} characters")
                
                try:
                    draft_data = json.loads(opportunity.draft)
                    print(f"   ✅ Valid JSON")
                    print(f"   📊 Type: {type(draft_data)}")
                    
                    if isinstance(draft_data, dict):
                        print(f"   🔑 Keys: {list(draft_data.keys())}")
                        
                        # Check if it has the expected structure
                        if 'draft' in draft_data:
                            sections = draft_data['draft']
                            print(f"   📋 Draft sections: {len(sections)} items")
                            
                            if sections and len(sections) > 0:
                                print(f"   📝 First section structure:")
                                first_section = sections[0]
                                if isinstance(first_section, dict):
                                    print(f"      Keys: {list(first_section.keys())}")
                                    if 'title' in first_section:
                                        print(f"      Title: {first_section['title']}")
                                    if 'content' in first_section:
                                        content_preview = first_section['content'][:200] if first_section['content'] else "Empty"
                                        print(f"      Content preview: {content_preview}...")
                                else:
                                    print(f"      Type: {type(first_section)}")
                        else:
                            print(f"   ⚠️ No 'draft' key found in data")
                            
                    elif isinstance(draft_data, list):
                        print(f"   📋 List with {len(draft_data)} items")
                        if draft_data and len(draft_data) > 0:
                            print(f"   📝 First item structure:")
                            first_item = draft_data[0]
                            if isinstance(first_item, dict):
                                print(f"      Keys: {list(first_item.keys())}")
                            else:
                                print(f"      Type: {type(first_item)}")
                    
                    # Show what criticism chain would receive
                    print(f"\n🔄 WHAT CRITICISM CHAIN RECEIVES:")
                    
                    # This is what the scheduler currently sends
                    if isinstance(draft_data, list):
                        proposal_structure = {'draft': draft_data}
                    elif isinstance(draft_data, dict) and 'draft' in draft_data:
                        proposal_structure = draft_data
                    else:
                        proposal_structure = {'draft': [draft_data] if isinstance(draft_data, dict) else draft_data}
                    
                    print(f"   📊 Proposal structure type: {type(proposal_structure)}")
                    print(f"   🔑 Proposal structure keys: {list(proposal_structure.keys()) if isinstance(proposal_structure, dict) else 'Not a dict'}")
                    
                    if isinstance(proposal_structure, dict) and 'draft' in proposal_structure:
                        sections = proposal_structure['draft']
                        print(f"   📋 Sections to analyze: {len(sections) if isinstance(sections, list) else 'Not a list'}")
                    
                except json.JSONDecodeError as e:
                    print(f"   ❌ Invalid JSON: {e}")
                    return False
            else:
                print(f"❌ No draft found")
                return False
            
            # Check compliance data
            print(f"\n📋 COMPLIANCE DATA ANALYSIS:")
            
            compliance_fields = {
                'structure_compliance': opportunity.structure_compliance,
                'content_compliance': opportunity.content_compliance,
                'format_compliance': opportunity.format_compliance
            }
            
            for field_name, field_value in compliance_fields.items():
                print(f"\n   {field_name}:")
                if field_value and field_value.strip():
                    print(f"      ✅ Has data: {len(field_value)} characters")
                    
                    # Try to parse as JSON
                    try:
                        compliance_json = json.loads(field_value)
                        print(f"      📊 Valid JSON with keys: {list(compliance_json.keys())}")
                    except json.JSONDecodeError:
                        print(f"      📝 Text format")
                        print(f"      Preview: {field_value[:100]}...")
                else:
                    print(f"      ❌ Empty or None")
            
            break
            
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        return False


async def debug_criticism_results():
    """Debug what criticism results look like"""
    
    print(f"\n🔍 DEBUGGING CRITICISM RESULTS")
    print("=" * 40)
    
    try:
        async for db in get_customer_db():
            # Get the most recent criticism result
            query = select(ProposalCriticismResults).order_by(
                ProposalCriticismResults.created_date.desc()
            ).limit(1)
            
            result = await db.execute(query)
            criticism_result = result.scalars().first()
            
            if criticism_result:
                print(f"✅ Found recent criticism result:")
                print(f"   Opportunity: {criticism_result.opportunity_id}")
                print(f"   Overall Score: {criticism_result.overall_score}")
                print(f"   Total Issues: {criticism_result.total_issues}")
                print(f"   Total Strengths: {criticism_result.total_strengths}")
                
                # Check individual criticism fields
                criticism_fields = {
                    'structure_criticism': criticism_result.structure_criticism,
                    'content_criticism': criticism_result.content_criticism,
                    'formatting_criticism': criticism_result.formatting_criticism,
                    'technical_criticism': criticism_result.technical_criticism
                }
                
                print(f"\n📊 CRITICISM FIELD ANALYSIS:")
                for field_name, field_value in criticism_fields.items():
                    print(f"\n   {field_name}:")
                    if field_value:
                        try:
                            criticism_data = json.loads(field_value)
                            print(f"      ✅ Valid JSON")
                            print(f"      📊 Keys: {list(criticism_data.keys())}")
                            
                            if 'score' in criticism_data:
                                print(f"      🎯 Score: {criticism_data['score']}")
                            if 'strengths' in criticism_data:
                                print(f"      💪 Strengths: {len(criticism_data['strengths'])} items")
                            if 'issues' in criticism_data:
                                print(f"      ⚠️ Issues: {len(criticism_data['issues'])} items")
                                
                        except json.JSONDecodeError:
                            print(f"      📝 Text format: {len(field_value)} characters")
                    else:
                        print(f"      ❌ Empty or None")
                        
            else:
                print(f"❌ No criticism results found")
            
            break
            
        return True
        
    except Exception as e:
        print(f"❌ Criticism results debug failed: {e}")
        return False


async def debug_criticism_chain_expectations():
    """Debug what the criticism chain expects vs what it gets"""
    
    print(f"\n🔍 DEBUGGING CRITICISM CHAIN EXPECTATIONS")
    print("=" * 50)
    
    print(f"""
📋 CRITICISM CHAIN ANALYSIS:

🔍 WHAT CRITICISM CHAIN EXPECTS:
   - proposal_outline: dict
   - structure_compliance: dict
   - content_compliance: dict
   - formatting_requirements: dict
   - technical_requirements: dict

📊 METHOD SIGNATURES:
   - criticize_structure(proposal_outline: dict, structure_compliance: dict) -> dict
   - criticize_content(proposal_outline: dict, content_compliance: dict) -> dict
   - criticize_formatting(proposal_outline: dict, formatting_requirements: dict) -> dict
   - criticize_technical(proposal_outline: dict, technical_requirements: dict) -> dict

📤 WHAT EACH METHOD RETURNS:
   {{
       "score": int (0-100),
       "strengths": list[str],
       "issues": list[str],
       "llm_analysis": dict
   }}

🔄 HOW LLM METHODS ACCESS DATA:
   - json.dumps(proposal_outline, indent=2)  # Serializes entire proposal structure
   - json.dumps(structure_compliance, indent=2)  # Serializes compliance requirements

⚠️ POTENTIAL ISSUES:
   1. If proposal_outline is not a dict, json.dumps will fail
   2. If compliance data is None, json.dumps will serialize "null"
   3. If LLM fails, methods return default dict with score=0
   4. If compliance data is empty, analysis will be meaningless

🎯 WHAT SCHEDULER SHOULD SEND:
   - proposal_outline: The full proposal structure (dict with 'draft' key containing sections)
   - compliance_data: Parsed JSON from database fields or structured dict

🔧 CURRENT SCHEDULER LOGIC:
   1. Gets proposal data from database
   2. Parses draft JSON
   3. Structures it as {'draft': sections}
   4. Gets compliance from database fields
   5. Calls criticism_chain methods
   6. Stores results in database
""")


async def main():
    """Main debug function"""
    
    print("🐛 DEBUGGING CRITICISM SYSTEM DATA FLOW")
    print("=" * 60)
    
    debug_functions = [
        ("Proposal Data Structure", debug_proposal_data_structure),
        ("Criticism Results", debug_criticism_results),
        ("Criticism Chain Expectations", debug_criticism_chain_expectations)
    ]
    
    for name, func in debug_functions:
        try:
            print(f"\n{'='*60}")
            await func()
        except Exception as e:
            print(f"❌ {name} debug failed: {e}")
    
    print(f"\n{'='*60}")
    print("🎯 DEBUG COMPLETE")
    print("=" * 60)
    
    print(f"""
💡 KEY FINDINGS TO LOOK FOR:

1. 📄 PROPOSAL DATA:
   - Is the draft JSON valid?
   - Does it have the expected structure?
   - Are sections properly formatted?

2. 📋 COMPLIANCE DATA:
   - Do compliance fields have data?
   - Is the data in JSON or text format?
   - Are the requirements meaningful?

3. 🔄 DATA FLOW:
   - Does scheduler structure data correctly?
   - Does criticism chain receive expected format?
   - Are results properly stored?

4. ⚠️ FAILURE POINTS:
   - Missing compliance data → score 0
   - LLM connection issues → score 0
   - Invalid data structure → errors
   - Empty proposal content → meaningless analysis
""")


if __name__ == "__main__":
    asyncio.run(main())
