#!/usr/bin/env python3
"""
Test script for the fast proposal generation pipeline with PDF export.
"""

import asyncio
import sys
import os

# Add the AIService directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from pipeline_4 import (
    determine_source_type,
    check_existing_outlines,
    fast_proposal_generation,
    export_proposal_to_pdf,
    wait_for_format_queue_completion
)
from loguru import logger


async def test_source_type_determination():
    """Test the source type determination logic"""
    logger.info("Testing source type determination...")
    
    # Test cases
    test_cases = [
        ("iRiYNgd8RC", "custom"),  # 10-char custom ID
        ("tA30VTcyMj", "custom"),  # 10-char custom ID
        ("12345678901234567890123456789012", "sam"),  # 32-char SAM ID (if exists)
    ]
    
    for opportunity_id, expected_source in test_cases:
        source = await determine_source_type(opportunity_id)
        logger.info(f"ID: {opportunity_id} -> Source: {source} (expected: {expected_source})")
    
    logger.info("✓ Source type determination test completed")


async def test_outline_checking():
    """Test the outline checking logic"""
    logger.info("Testing outline checking...")
    
    test_opportunity_id = "iRiYNgd8RC"
    source = await determine_source_type(test_opportunity_id)
    
    outlines_exist = await check_existing_outlines(test_opportunity_id, source)
    logger.info(f"Outlines exist for {test_opportunity_id}: {outlines_exist}")
    
    logger.info("✓ Outline checking test completed")


async def test_fast_generation():
    """Test the fast proposal generation"""
    logger.info("Testing fast proposal generation...")
    
    # Test configuration
    opportunity_id = "iRiYNgd8RC"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    client_short_name = "adeptengineeringsolutions"
    is_rfp = False
    
    logger.info(f"Testing with opportunity_id: {opportunity_id}")
    
    result = await fast_proposal_generation(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        client_short_name=client_short_name,
        is_rfp=is_rfp
    )
    
    if "success" in result:
        logger.info(f"✅ Generation successful: {result['success']}")
        
        # Test PDF export if generation was successful
        logger.info("Testing PDF export...")
        
        # Wait for format queue
        format_ready = await wait_for_format_queue_completion(opportunity_id, tenant_id, max_wait_seconds=30)
        
        if format_ready:
            pdf_result = await export_proposal_to_pdf(
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                client_short_name=client_short_name,
                source=await determine_source_type(opportunity_id)
            )
            
            if "success" in pdf_result:
                logger.info(f"✅ PDF export successful: {pdf_result['success']}")
                logger.info(f"📄 PDF file: {pdf_result['filename']}")
            else:
                logger.warning(f"⚠️  PDF export failed: {pdf_result['error']}")
        else:
            logger.warning("⚠️  Format queue not ready for PDF export")
            
    else:
        logger.warning(f"⚠️  Generation result: {result['error']}")
    
    logger.info("✓ Fast generation test completed")


async def main():
    """Run all tests"""
    logger.info("="*80)
    logger.info("TESTING FAST PROPOSAL GENERATION PIPELINE")
    logger.info("="*80)
    
    try:
        await test_source_type_determination()
        await test_outline_checking()
        await test_fast_generation()
        
        logger.info("="*80)
        logger.info("🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
        logger.info("="*80)
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
