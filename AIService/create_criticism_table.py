#!/usr/bin/env python3
"""
Create Criticism Table Migration

Creates the proposal_criticism_results table in the database.
"""

import asyncio
import logging
from database import get_customer_db

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger("CREATE_CRITICISM_TABLE")


async def create_criticism_table():
    """Create the proposal_criticism_results table"""
    
    # Read the SQL file
    try:
        with open('create_criticism_table.sql', 'r') as f:
            sql_content = f.read()
    except FileNotFoundError:
        logger.error("create_criticism_table.sql file not found")
        return False
    
    try:
        async for db in get_customer_db():
            logger.info("Creating proposal_criticism_results table...")
            
            # Execute the SQL
            await db.execute(sql_content)
            await db.commit()
            
            logger.info("✅ proposal_criticism_results table created successfully")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error creating table: {e}")
        return False


async def verify_table_creation():
    """Verify that the table was created successfully"""
    
    try:
        async for db in get_customer_db():
            # Check if table exists
            result = await db.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'opportunity' 
                    AND table_name = 'proposal_criticism_results'
                );
            """)
            
            table_exists = result.scalar()
            
            if table_exists:
                logger.info("✅ Table verification successful")
                
                # Get table info
                result = await db.execute("""
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns 
                    WHERE table_schema = 'opportunity' 
                    AND table_name = 'proposal_criticism_results'
                    ORDER BY ordinal_position;
                """)
                
                columns = result.fetchall()
                logger.info(f"📋 Table has {len(columns)} columns:")
                for col in columns:
                    logger.info(f"   - {col[0]} ({col[1]}) {'NULL' if col[2] == 'YES' else 'NOT NULL'}")
                
                return True
            else:
                logger.error("❌ Table verification failed - table does not exist")
                return False
                
    except Exception as e:
        logger.error(f"❌ Error verifying table: {e}")
        return False


async def main():
    """Main function"""
    
    print("🚀 Creating Proposal Criticism Results Table")
    print("=" * 50)
    
    # Create the table
    success = await create_criticism_table()
    
    if success:
        # Verify creation
        verified = await verify_table_creation()
        
        if verified:
            print("\n🎉 SUCCESS!")
            print("✅ proposal_criticism_results table created and verified")
            print("✅ Database is ready for criticism system")
            print("\n💡 Next steps:")
            print("1. Run the criticism scheduler")
            print("2. Generate proposals to test the system")
            print("3. Check criticism results in the database")
        else:
            print("\n⚠️ Table created but verification failed")
    else:
        print("\n❌ FAILED!")
        print("Could not create the criticism table")
        print("Check database connection and permissions")


if __name__ == "__main__":
    asyncio.run(main())
