import asyncio
import json
from services.proposal.rfp.generate_rfp import RFPGenerationService

async def main():

    opportunity_id = "iRiYNgd8RC"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    source = "custom"

    job_instruction = {
        "opportunityId": opportunity_id,
        "clientShortName": "adeptengineeringsolutions",
        "tenantId": "8d9e9729-f7bd-44a0-9cf1-777f532a2db2",
        "profileId": "2",
        "opportunityType": source,
        "sourceDocuments": [],
        "forceRefresh": False,
        "setForReview": True,
        "exportType": 1,
        "proposalRequestType": 1,
        "coverPage": None,
        "trailingPage": None,
        "systemPromptParameters": None,
        "isRFP": True,
        "generatedVolumes": [1],
        "aiPersonalityId": "6"
    }

    rfp_generator = RFPGenerationService()
    await rfp_generator.generate_rfp(json.dumps(job_instruction), "69")


if __name__ == "__main__":
    asyncio.run(main())