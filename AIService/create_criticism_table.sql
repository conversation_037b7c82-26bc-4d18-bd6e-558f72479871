-- Create proposal criticism tables for queue-based criticism analysis system
-- This includes both the queue table and results table

-- Create proposal_criticism_queue table for managing criticism analysis requests
CREATE TABLE IF NOT EXISTS opportunity.proposal_criticism_queue (
    id BIGSERIAL PRIMARY KEY,
    opportunity_id VARCHAR(255) NOT NULL,
    tenant_id VARCHAR(255) NOT NULL,
    client_short_name VARCHAR(255),
    status VARCHAR(50) DEFAULT 'NEW' NOT NULL,
    priority INTEGER DEFAULT 1,
    analysis_type VARCHAR(100) DEFAULT 'full',
    creation_date TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
    completion_date TIMESTAMP WITHOUT TIME ZONE,
    submitted_by <PERSON><PERSON><PERSON><PERSON>(255),
    analysis_results TEXT,
    error_message TEXT,
    processing_time_seconds FLOAT
);

-- Create indexes for the queue table
CREATE INDEX IF NOT EXISTS idx_proposal_criticism_queue_status
    ON opportunity.proposal_criticism_queue(status);

CREATE INDEX IF NOT EXISTS idx_proposal_criticism_queue_priority
    ON opportunity.proposal_criticism_queue(priority, creation_date);

CREATE INDEX IF NOT EXISTS idx_proposal_criticism_queue_tenant_id
    ON opportunity.proposal_criticism_queue(tenant_id);

-- Create proposal_criticism_results table for storing criticism analysis results
CREATE TABLE IF NOT EXISTS opportunity.proposal_criticism_results (
    id BIGSERIAL PRIMARY KEY,
    opportunity_id VARCHAR(255) NOT NULL,
    tenant_id VARCHAR(255) NOT NULL,
    client_short_name VARCHAR(255),
    analysis_timestamp TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW() NOT NULL,
    generation_method VARCHAR(255),
    
    -- Criticism scores (0-100)
    structure_score INTEGER,
    content_score INTEGER,
    formatting_score INTEGER,
    technical_score INTEGER,
    overall_score INTEGER,
    
    -- Detailed criticism results (JSON stored as TEXT)
    structure_criticism TEXT,
    content_criticism TEXT,
    formatting_criticism TEXT,
    technical_criticism TEXT,
    
    -- Summary fields
    total_issues INTEGER DEFAULT 0,
    total_strengths INTEGER DEFAULT 0,
    improvement_suggestions TEXT,
    
    -- Metadata
    proposal_version INTEGER DEFAULT 1,
    analyzed_sections_count INTEGER,
    processing_time_seconds FLOAT,
    
    -- Timestamps
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_proposal_criticism_opportunity_id 
    ON opportunity.proposal_criticism_results(opportunity_id);

CREATE INDEX IF NOT EXISTS idx_proposal_criticism_tenant_id 
    ON opportunity.proposal_criticism_results(tenant_id);

CREATE INDEX IF NOT EXISTS idx_proposal_criticism_analysis_timestamp 
    ON opportunity.proposal_criticism_results(analysis_timestamp);

CREATE INDEX IF NOT EXISTS idx_proposal_criticism_overall_score 
    ON opportunity.proposal_criticism_results(overall_score);

-- Create composite index for common queries
CREATE INDEX IF NOT EXISTS idx_proposal_criticism_tenant_timestamp 
    ON opportunity.proposal_criticism_results(tenant_id, analysis_timestamp);

-- Add comments for documentation
COMMENT ON TABLE opportunity.proposal_criticism_results IS 'Stores criticism analysis results for generated proposals';
COMMENT ON COLUMN opportunity.proposal_criticism_results.opportunity_id IS 'Unique identifier for the opportunity';
COMMENT ON COLUMN opportunity.proposal_criticism_results.tenant_id IS 'Tenant identifier';
COMMENT ON COLUMN opportunity.proposal_criticism_results.generation_method IS 'Method used to generate the proposal (multi_agent, traditional, etc.)';
COMMENT ON COLUMN opportunity.proposal_criticism_results.structure_score IS 'Structure compliance score (0-100)';
COMMENT ON COLUMN opportunity.proposal_criticism_results.content_score IS 'Content quality score (0-100)';
COMMENT ON COLUMN opportunity.proposal_criticism_results.formatting_score IS 'Formatting compliance score (0-100)';
COMMENT ON COLUMN opportunity.proposal_criticism_results.technical_score IS 'Technical quality score (0-100)';
COMMENT ON COLUMN opportunity.proposal_criticism_results.overall_score IS 'Overall quality score (0-100)';
COMMENT ON COLUMN opportunity.proposal_criticism_results.structure_criticism IS 'Detailed structure criticism results (JSON)';
COMMENT ON COLUMN opportunity.proposal_criticism_results.content_criticism IS 'Detailed content criticism results (JSON)';
COMMENT ON COLUMN opportunity.proposal_criticism_results.formatting_criticism IS 'Detailed formatting criticism results (JSON)';
COMMENT ON COLUMN opportunity.proposal_criticism_results.technical_criticism IS 'Detailed technical criticism results (JSON)';
COMMENT ON COLUMN opportunity.proposal_criticism_results.total_issues IS 'Total number of issues found';
COMMENT ON COLUMN opportunity.proposal_criticism_results.total_strengths IS 'Total number of strengths identified';
COMMENT ON COLUMN opportunity.proposal_criticism_results.processing_time_seconds IS 'Time taken to complete the analysis in seconds';
