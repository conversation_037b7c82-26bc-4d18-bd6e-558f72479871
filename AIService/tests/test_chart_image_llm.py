import asyncio
import sys
import os
from datetime import datetime

from langchain_core.runnables.graph_mermaid import draw_mermaid_png

# THIS is a temporary debug file would be removed later
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from loguru import logger

# Import the controller and models
from controllers.customer.datametastore_controller import DataMetastoreController
from database import get_customer_db
from services.scheduler_service.datametastore_scheduler_service import DataMetastoreSchedulerService
from services.llm.ollama_llm import OllamaLLMService

datametastore_controller = DataMetastoreSchedulerService()
 
async def debug_datametastore():
    # Configure logging
    logger.remove()
    logger.add(sys.stdout, level="INFO")
    ollama_llm = OllamaLLMService()
   
    
    async for session in get_customer_db():
        try:
            # Directly call the method with a specific datetime
            logger.info("Calling get_partner_doc_by_date method...")
            # result = await datametastore_controller.process_datametastore_partner_docs()
            user_prompts = [
                "Company team of experts in compliance and technology, working on computers and whiteboards, surrounded by documents and regulatory materials.", 
                "Map of joint venture partners, with company logo, subcontractor name, and Department of Justice logo.",
                "A technical diagram showing a scalable cloud-based system with AI-powered tools integrated into existing web-based examiner search tools, surrounded by security controls and compliance badges.",
                "USPTO Requirements Diagram: Application Type, Security Controls, Technology Stack"
                ]
            result = ollama_llm.chart_image_prompt(user_prompts[3])
            # Convert Mermaid syntax to PNG image
            
            
            # Save the Mermaid diagram as a PNG file
            png_bytes = draw_mermaid_png(result)
            
            # Optional: Save the PNG to a file in the current directory
            output_file_path = f"generated_diagram_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            with open(output_file_path, 'wb') as f:
                f.write(png_bytes)
            
            logger.info(f"Diagram saved as {output_file_path}")
            logger.info(result)
            logger.info("Proposal Image generated...", result)
        
        except Exception as e:
            logger.error(f"Error in debug_datametastore: {e}")
            import traceback
            traceback.print_exc()
        finally:
            await session.close()

if __name__ == '__main__':
    asyncio.run(debug_datametastore()) 