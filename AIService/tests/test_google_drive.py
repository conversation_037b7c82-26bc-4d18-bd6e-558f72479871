from services.google.google_drive_service import GoogleDriveService

drive_service = GoogleDriveService()
access_token = "YOUR_ACCESS_TOKEN"      # Replace with a valid access token
refresh_token = "YOUR_REFRESH_TOKEN"    # Replace with a valid refresh token

# List files
files = drive_service.list_all_files(access_token)
for f in files:
    print(f["id"], f["name"], f["mimeType"])

# Download file content
if files:
    file_id = files[0]["id"]
    mime_type = files[0]["mimeType"]
    content = drive_service.get_file_content(access_token, file_id, mime_type)
    print(content)

    # Download with token refresh
    def refresh_callback(refresh_token):
        # Implement your token refresh logic here
        return "NEW_ACCESS_TOKEN"

    content = drive_service.get_file_content_with_refresh(access_token, refresh_token, file_id, mime_type, refresh_callback)
    print(content)
else:
    print("No files found.")