import numpy as np
from typing import List
from utils.embedding_model import Ko<PERSON><PERSON>r<PERSON>mbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter

def embed_large_text(text: str, max_chunk_size: int = 1000) -> List[float]:
    """Simple function to embed large text by chunking and averaging."""
    
    # Initialize embeddings
    embeddings = KontratarEmbeddings("http://ai.kontratar.com:5000")
    
    # Split text into chunks
    splitter = RecursiveCharacterTextSplitter(
        chunk_size=max_chunk_size,
        chunk_overlap=200,
        length_function=len
    )
    chunks = splitter.split_text(text)
    
    # Get embeddings for all chunks
    chunk_embeddings = embeddings.embed_documents(chunks)
    
    # Average the embeddings
    avg_embedding = np.mean(chunk_embeddings, axis=0)
    
    return avg_embedding.tolist()


def create_average_embedding(embeddings: List[List[float]]) -> List[float]:
    """Simple function to embed large text by chunking and averaging."""
    # Average the embeddings
    avg_embedding = np.mean(embeddings, axis=0)
    
    return avg_embedding.tolist()
