from typing import List, Optional

import requests
from langchain.embeddings.base import Embeddings


class KontratarEmbeddings(Embeddings):
    """
    LangChain wrapper for a self-hosted embedding model with an HTTP API.
    """

    def __init__(self, api_url: str, api_key: Optional[str] = None):
        self.api_url = api_url
        self.api_key = api_key

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Embed a list of documents.
        """
        headers = {"Authorization": f"Bearer {self.api_key}"} if self.api_key else {}
        response = requests.post(
            f"{self.api_url}/embed_batch",
            json={"texts": texts},
            headers=headers,
            timeout=60,
        )
        response.raise_for_status()
        return response.json()["embeddings"]

    def embed_query(self, text: str) -> List[float]:
        """
        Embed a single query.
        """
        return self.embed_documents([text])[0]