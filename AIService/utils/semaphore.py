import asyncio
from typing import Awaitable, Callable, Iterable, Optional, TypeVar

T = TypeVar("T")

async def run_with_semaphore(
    items: Iterable[T],
    max_jobs: int,
    worker: Callable[[T], Awaitable[None]],
    on_enter: Optional[Callable[[T], Awaitable[None]]] = None,
) -> None:
    sem = asyncio.Semaphore(max_jobs)

    async def runner(item: T):
        async with sem:
            if on_enter:
                await on_enter(item)
            await worker(item)

    await asyncio.gather(*(runner(item) for item in items))