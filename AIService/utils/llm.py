from typing import Any, List, Optional

import requests
from langchain.llms.base import LLM
from pydantic import Field
#from langsmith import traceable


class KontratarLLM(LLM):
    """
    LangChain wrapper for a self-hosted LLM with an HTTP API.
    """

    api_url: str = Field(default="http://ai.kontratar.com:8080")
    api_key: Optional[str] = None

    def __init__(self, api_url: str = "http://ai.kontratar.com:8080", api_key: Optional[str] = None, **kwargs):
        super().__init__(api_url=api_url, api_key=api_key, **kwargs)

    @property
    def _llm_type(self) -> str:
        return "myapi-llm"

    #@traceable(name="kontratar_llm_call")
    def _call(self, system_prompt: str, user_prompt: str, max_tokens: int = 2048, stop: Optional[List[str]] = None, **kwargs: Any) -> str:
        #headers = {"Authorization": f"Bearer {self.api_key}"} if self.api_key else {}
        payload = {
            "user_prompt": user_prompt,
            "system_prompt": system_prompt,
            "max_tokens": max_tokens,
            "model": "gemma3:27b"
        }
        if stop:
            payload["stop"] = stop
        response = requests.post(
            f"{self.api_url}/generate",
            json=payload,
            #headers=headers,
            timeout=60,
        )
        response.raise_for_status()
        return response.json()["generated_text"]
