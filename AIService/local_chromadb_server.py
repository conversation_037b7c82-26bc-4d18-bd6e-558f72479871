import chromadb
from chromadb.config import Settings
import time
import sys
from pathlib import Path
import threading
import signal

class LocalChromaDBServer:
    def __init__(self, port=8001, data_dir="./chromadb_data"):
        self.port = port
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        self.client = None
        self.running = False
        
    def start(self):
        """Start the local ChromaDB server"""
        
        print(f"Starting local ChromaDB server on port {self.port}...")
        print(f"Data directory: {self.data_dir.absolute()}")
        
        try:
            # Create persistent ChromaDB client
            self.client = chromadb.PersistentClient(
                path=str(self.data_dir.absolute()),
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            print(f"ChromaDB client created successfully")
            print(f"Data persisted to: {self.data_dir.absolute()}")
            
            # Test the client
            self._test_client()
            
            self.running = True
            print(f"Local ChromaDB is ready!")
            print(f"Access via: http://localhost:{self.port}")
            print(f"Collections: {len(self.client.list_collections())}")
            
            return True
            
        except Exception as e:
            print(f"Failed to start ChromaDB: {e}")
            return False
    
    def _test_client(self):
        """Test the ChromaDB client"""
        
        try:
            # List existing collections
            collections = self.client.list_collections()
            print(f"Found {len(collections)} existing collections")
            
            # Create a test collection
            test_name = "test_connection"
            try:
                # Try to get existing test collection
                test_collection = self.client.get_collection(test_name)
                print(f"Found existing test collection")
            except:
                # Create new test collection
                test_collection = self.client.create_collection(test_name)
                print(f"Created test collection")
            
            # Test basic operations
            test_collection.add(
                documents=["Test document for connection"],
                metadatas=[{"test": "true"}],
                ids=["test_1"]
            )
            
            # Query test
            results = test_collection.query(
                query_texts=["test"],
                n_results=1
            )
            
            print(f"ChromaDB operations working correctly")
            
        except Exception as e:
            print(f"ChromaDB test warning: {e}")
    
    def stop(self):
        """Stop the ChromaDB server"""
        
        print(f"Stopping ChromaDB server...")
        self.running = False
        
        if self.client:
            try:
                # Clean up test collection
                try:
                    self.client.delete_collection("test_connection")
                except:
                    pass
                print(f"ChromaDB stopped cleanly")
            except Exception as e:
                print(f"ChromaDB stop warning: {e}")
    
    def get_client(self):
        """Get the ChromaDB client"""
        return self.client
    
    def is_running(self):
        """Check if server is running"""
        return self.running and self.client is not None

def main():
    """Main function to run the server"""
    
    print(" LOCAL CHROMADB SERVER")
    print("=" * 40)
    
    server = LocalChromaDBServer()
    
    def signal_handler(sig, frame):
        print(f"\nReceived stop signal...")
        server.stop()
        sys.exit(0)
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    if server.start():
        print(f"\nChromaDB server running... Press Ctrl+C to stop")
        print(f"You can now restart your application to use local ChromaDB")
        
        try:
            # Keep server running
            while server.is_running():
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            server.stop()
    else:
        print(f"Failed to start ChromaDB server")
        sys.exit(1)

if __name__ == "__main__":
    main()
