import asyncio
import sys
from sqlalchemy import text
from database import get_kontratar_db
from loguru import logger

async def migrate_chromadb_mappings():
    """Update all ChromaDB instance mappings to use local instance"""
    
    print("Migrating ChromaDB mappings to local instance...")
    
    local_chromadb_url = "http://localhost:8001"
    
    try:
        async for db in get_kontratar_db():
            # Check current mappings
            result = await db.execute(
                text("SELECT COUNT(*) as count FROM kontratar_main.chromadb_instance_mapping")
            )
            total_mappings = result.scalar()
            
            print(f"Found {total_mappings} existing ChromaDB mappings")
            
            if total_mappings == 0:
                print("No existing mappings to migrate")
                return True
            
            # Check how many are already local
            result = await db.execute(
                text("SELECT COUNT(*) as count FROM kontratar_main.chromadb_instance_mapping WHERE chroma_instance_url LIKE '%localhost%' OR chroma_instance_url LIKE '%127.0.0.1%'")
            )
            local_mappings = result.scalar()
            
            print(f"{local_mappings} mappings already point to local instance")
            
            remote_mappings = total_mappings - local_mappings
            if remote_mappings == 0:
                print("All mappings already use local ChromaDB")
                return True
            
            print(f"{remote_mappings} mappings need to be updated to local")
            
            # Update remote mappings to local
            update_result = await db.execute(
                text("""
                    UPDATE kontratar_main.chromadb_instance_mapping 
                    SET chroma_instance_url = :local_url,
                        last_accessed_date = NOW()
                    WHERE chroma_instance_url NOT LIKE '%localhost%' 
                    AND chroma_instance_url NOT LIKE '%127.0.0.1%'
                """),
                {"local_url": local_chromadb_url}
            )
            
            await db.commit()
            
            updated_count = update_result.rowcount
            print(f"Updated {updated_count} mappings to use local ChromaDB")
            
            # Verify the update
            result = await db.execute(
                text("SELECT COUNT(*) as count FROM kontratar_main.chromadb_instance_mapping WHERE chroma_instance_url = :local_url"),
                {"local_url": local_chromadb_url}
            )
            final_local_count = result.scalar()
            
            print(f"Migration complete! {final_local_count}/{total_mappings} mappings now use local ChromaDB")
            
            # Show some example mappings
            result = await db.execute(
                text("""
                    SELECT unique_id, tenant_id, collection_name, chroma_instance_url, status 
                    FROM kontratar_main.chromadb_instance_mapping 
                    LIMIT 5
                """)
            )
            
            print(f"\nSample mappings after migration:")
            for row in result:
                print(f"   {row.unique_id[:8]}... -> {row.chroma_instance_url} ({row.status})")
            
            return True
            
    except Exception as e:
        print(f"Migration failed: {e}")
        return False

async def verify_local_chromadb():
    """Verify that local ChromaDB is accessible"""
    
    print(f"\nVerifying local ChromaDB accessibility...")
    
    try:
        import httpx
        
        # Test connection to local ChromaDB
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8001/api/v1/heartbeat", timeout=5.0)
            
            if response.status_code == 200:
                print(f"Local ChromaDB is accessible at http://localhost:8001")
                
                # Test collections endpoint
                try:
                    collections_response = await client.get("http://localhost:8001/api/v1/collections", timeout=5.0)
                    if collections_response.status_code == 200:
                        collections = collections_response.json()
                        print(f"Found {len(collections)} collections in local ChromaDB")
                    else:
                        print(f"Could not list collections (status: {collections_response.status_code})")
                except Exception as e:
                    print(f"Could not list collections: {e}")
                
                return True
            else:
                print(f"Local ChromaDB returned status {response.status_code}")
                return False
                
    except Exception as e:
        print(f"Cannot connect to local ChromaDB: {e}")
        print(f"Make sure local ChromaDB server is running on port 8001")
        return False

async def main():
    """Main migration function"""
    
    print("CHROMADB MIGRATION TO LOCAL")
    print("=" * 50)
    print("This will update all existing ChromaDB mappings to use the local instance")
    print()
    
    # First verify local ChromaDB is running
    if not await verify_local_chromadb():
        print(f"\nLocal ChromaDB is not accessible. Please start it first:")
        print(f"   python3 local_chromadb_server.py")
        return False
    
    # Migrate the mappings
    if await migrate_chromadb_mappings():
        print(f"\nMIGRATION SUCCESSFUL!")
        print(f"   All ChromaDB mappings now point to local instance")
        print(f"   Local ChromaDB is accessible and ready")
        return True
    else:
        print(f"\nMIGRATION FAILED!")
        print(f"   Please check the error messages above and try again")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
