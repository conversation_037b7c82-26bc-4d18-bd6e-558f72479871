# ---- Builder Stage ----
FROM python:3.11-slim AS builder

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DEBIAN_FRONTEND=noninteractive

# Install build dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    pkg-config \
    libpq-dev \
    gcc && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* \
    && apt-get purge -y --auto-remove

# Create builder user
RUN useradd -m -s /bin/bash builder
ENV HOME=/home/<USER>

# Install Python dependencies
WORKDIR /build
COPY AIService/requirements.txt .
RUN pip install --user --no-cache-dir -r requirements.txt && \
    pip install --user configparser python-dotenv

# ---- Token Replacement Stage ----
FROM python:3.11-slim AS token-replacer

# Install bash
RUN apt-get update && \
    apt-get install -y --no-install-recommends bash && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Copy Python packages
COPY --from=builder /home/<USER>/.local /home/<USER>/.local
ENV PATH="/home/<USER>/.local/bin:$PATH" \
    PYTHONPATH="/home/<USER>/.local/lib/python3.11/site-packages"

# Copy required directories to root level (as expected by script)
COPY PythonTokenReplacement/ /PythonTokenReplacement/
COPY properties/ /properties/
COPY scripts/ /scripts/
COPY AIService/ /AIService/

# Run token replacement from root directory
WORKDIR /
RUN chmod +x /scripts/replace-tokens.sh && \
    /scripts/replace-tokens.sh AIService

# ---- Runtime Stage ----
FROM python:3.11-slim

# Build arguments
ARG APP_USER=kontratar
ARG APP_UID=1000  
ARG APP_GID=1000
ARG APP_PORT=3011
ARG APP_HOST=0.0.0.0
ARG TAG=latest

# Environment variables
ENV DEBIAN_FRONTEND=noninteractive \
    APP_USER=${APP_USER} \
    APP_UID=${APP_UID} \
    APP_GID=${APP_GID} \
    APP_PORT=${APP_PORT} \
    APP_HOST=${APP_HOST} \
    APP_VERSION=${TAG} \
    PATH="/opt/python-packages/bin:$PATH" \
    PYTHONPATH="/opt/python-packages/lib/python3.11/site-packages:/app" \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# Install runtime dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    ca-certificates && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Create user and directories
RUN groupadd --gid ${APP_GID} ${APP_USER} && \
    useradd --uid ${APP_UID} --gid ${APP_USER} --shell /bin/bash --create-home ${APP_USER} && \
    mkdir -p /app /app/logs /app/data && \
    chown -R ${APP_USER}:${APP_USER} /app /home/<USER>

# Copy Python packages
COPY --from=builder --chown=root:root /home/<USER>/.local /opt/python-packages

# Copy entire AIService directory and generated .env
COPY --chown=${APP_USER}:${APP_USER} AIService/ /app/
COPY --from=token-replacer --chown=${APP_USER}:${APP_USER} /AIService/.env /app/.env

# Set permissions
RUN chmod -R 755 /app && \
    chmod 644 /app/.env

WORKDIR /app

# Create startup script
RUN echo '#!/bin/bash\n\
set -e\n\
echo "=== AIService Starting ==="\n\
echo "User: $(whoami)"\n\
echo "Working Directory: $(pwd)"\n\
echo "App Version: ${APP_VERSION}"\n\
cd /app\n\
if [ ! -f .env ]; then\n\
    echo "ERROR: .env file not found!"\n\
    exit 1\n\
fi\n\
echo "Configuration loaded successfully"\n\
echo "Starting AIService..."\n\
exec python3 start.py' > /home/<USER>/run_app.sh && \
    chown ${APP_USER}:${APP_USER} /home/<USER>/run_app.sh && \
    chmod 750 /home/<USER>/run_app.sh

# Create log file
RUN touch /app/logs/AIService.log && \
    chown ${APP_USER}:${APP_USER} /app/logs/AIService.log

# Labels
LABEL maintainer="kontratar" \
      service="AIService" \
      version="${TAG}"

# Switch to app user
USER ${APP_USER}

EXPOSE ${APP_PORT}

HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://${APP_HOST}:${APP_PORT}/health || exit 1

CMD ["/home/<USER>/run_app.sh"]