#!/usr/bin/env python3
"""
Comprehensive Test of Criticism System

Tests the exact data flow and identifies the root cause:
1. Check real proposal data structure
2. Check real compliance data
3. Test criticism chain with real data
4. Identify why score is 0
"""

import asyncio
import json
import logging
from sqlalchemy import select
from database import get_customer_db
from models.customer_models import CustomOppsTable
from services.proposal.criticism_chain import CriticismChain
from services.queue_service.proposal_criticism_queue_service import ProposalCriticismQueueService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("CRITICISM_TEST")


async def test_real_data_flow():
    """Test with the exact same data that failed"""
    
    print("🔍 TESTING REAL CRITICISM SYSTEM DATA FLOW")
    print("=" * 60)
    
    try:
        async for db in get_customer_db():
            # Use the exact opportunity that failed
            opportunity_id = "iRiYNgd8RC"
            
            print(f"📋 Testing with opportunity: {opportunity_id}")
            
            # 1. Get the real proposal data
            query = select(CustomOppsTable).where(
                CustomOppsTable.opportunity_id == opportunity_id
            )
            
            result = await db.execute(query)
            opportunity = result.scalars().first()
            
            if not opportunity:
                print(f"❌ Opportunity not found")
                return False
            
            print(f"✅ Found opportunity: {opportunity.title}")
            
            # 2. Check draft data
            if not opportunity.draft:
                print(f"❌ No draft data found")
                return False
            
            try:
                draft_content = json.loads(opportunity.draft)
                print(f"✅ Draft data loaded: {type(draft_content)}")
                
                # Structure the data like the scheduler does
                if isinstance(draft_content, list):
                    draft_structure = {'draft': draft_content}
                elif isinstance(draft_content, dict) and 'draft' in draft_content:
                    draft_structure = draft_content
                else:
                    draft_structure = {'draft': [draft_content] if isinstance(draft_content, dict) else draft_content}
                
                print(f"📊 Draft structure: {type(draft_structure)}")
                if isinstance(draft_structure, dict) and 'draft' in draft_structure:
                    sections = draft_structure['draft']
                    print(f"📋 Sections to analyze: {len(sections) if isinstance(sections, list) else 'Not a list'}")
                    
                    if isinstance(sections, list) and len(sections) > 0:
                        first_section = sections[0]
                        print(f"📝 First section type: {type(first_section)}")
                        if isinstance(first_section, dict):
                            print(f"   Keys: {list(first_section.keys())}")
                            if 'content' in first_section:
                                content_length = len(first_section['content']) if first_section['content'] else 0
                                print(f"   Content length: {content_length} characters")
                
            except json.JSONDecodeError as e:
                print(f"❌ Invalid draft JSON: {e}")
                return False
            
            # 3. Check compliance data
            print(f"\n📋 CHECKING COMPLIANCE DATA:")
            
            compliance_fields = {
                'structure_compliance': opportunity.structure_compliance,
                'content_compliance': opportunity.content_compliance,
                'format_compliance': opportunity.format_compliance
            }
            
            compliance_data = {}
            for field_name, field_value in compliance_fields.items():
                if field_value and field_value.strip():
                    print(f"   ✅ {field_name}: {len(field_value)} characters")
                    
                    try:
                        parsed_compliance = json.loads(field_value)
                        compliance_data[field_name] = parsed_compliance
                        print(f"      📊 Valid JSON with keys: {list(parsed_compliance.keys())}")
                    except json.JSONDecodeError:
                        compliance_data[field_name] = {'requirements': field_value.strip()}
                        print(f"      📝 Text format, converted to requirements dict")
                else:
                    print(f"   ❌ {field_name}: Empty or None")
                    compliance_data[field_name] = None
            
            # 4. Test criticism chain with real data
            print(f"\n🧪 TESTING CRITICISM CHAIN:")
            
            if not any(compliance_data.values()):
                print(f"❌ No compliance data available - this explains the score 0!")
                print(f"💡 The criticism system needs compliance requirements to analyze against")
                return False
            
            try:
                criticism_chain = CriticismChain()
                
                # Test with the exact data structure the scheduler uses
                proposal_outline = draft_structure  # This is what scheduler passes
                
                print(f"📊 Testing with proposal_outline type: {type(proposal_outline)}")
                
                # Test structure criticism if we have structure compliance
                if compliance_data['structure_compliance']:
                    print(f"\n🔍 Testing structure criticism...")
                    try:
                        structure_result = criticism_chain.criticize_structure(
                            proposal_outline, 
                            compliance_data['structure_compliance']
                        )
                        print(f"   ✅ Structure criticism successful")
                        print(f"   🎯 Score: {structure_result.get('score', 'N/A')}")
                        print(f"   💪 Strengths: {len(structure_result.get('strengths', []))}")
                        print(f"   ⚠️ Issues: {len(structure_result.get('issues', []))}")
                        
                        if structure_result.get('score', 0) == 0:
                            print(f"   🔍 LLM Analysis: {structure_result.get('llm_analysis', {})}")
                        
                    except Exception as e:
                        print(f"   ❌ Structure criticism failed: {e}")
                
                # Test content criticism if we have content compliance
                if compliance_data['content_compliance']:
                    print(f"\n🔍 Testing content criticism...")
                    try:
                        content_result = criticism_chain.criticize_content(
                            proposal_outline, 
                            compliance_data['content_compliance']
                        )
                        print(f"   ✅ Content criticism successful")
                        print(f"   🎯 Score: {content_result.get('score', 'N/A')}")
                        print(f"   💪 Strengths: {len(content_result.get('strengths', []))}")
                        print(f"   ⚠️ Issues: {len(content_result.get('issues', []))}")
                        
                    except Exception as e:
                        print(f"   ❌ Content criticism failed: {e}")
                
                return True
                
            except Exception as e:
                print(f"❌ Criticism chain test failed: {e}")
                return False
            
            break
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


async def test_llm_connectivity():
    """Test LLM connectivity specifically"""
    
    print(f"\n🔗 TESTING LLM CONNECTIVITY")
    print("=" * 40)
    
    try:
        from langchain_ollama import ChatOllama
        
        # Test with the same configuration as criticism chain
        llm = ChatOllama(
            model="gemma3:27b",
            temperature=0.0,
            base_url="http://ai.kontratar.com:11434"
        )
        
        print(f"🔄 Testing LLM connection...")
        
        test_messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Respond with just the word 'SUCCESS' if you can process this message."}
        ]
        
        response = llm.invoke(test_messages)
        print(f"✅ LLM Response: {response.content}")
        
        # Test structured output (what criticism chain uses)
        print(f"\n🔄 Testing structured output...")
        
        from pydantic import BaseModel, Field
        from typing import List
        
        class TestSchema(BaseModel):
            score: int = Field(description="Score from 0 to 100")
            message: str = Field(description="Test message")
        
        structured_llm = llm.with_structured_output(TestSchema)
        
        test_structured_messages = [
            {"role": "system", "content": "You are a test assistant. Always return a score of 85 and message 'Test successful'."},
            {"role": "user", "content": "Please provide a test response."}
        ]
        
        structured_response = structured_llm.invoke(test_structured_messages)
        print(f"✅ Structured Response: {structured_response.model_dump()}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM connectivity test failed: {e}")
        return False


async def main():
    """Main test function"""
    
    print("🚀 COMPREHENSIVE CRITICISM SYSTEM TEST")
    print("=" * 70)
    
    tests = [
        ("Real Data Flow Test", test_real_data_flow),
        ("LLM Connectivity Test", test_llm_connectivity)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*70}")
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*70}")
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 70)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} | {test_name}")
    
    print(f"\n💡 DIAGNOSIS:")
    print(f"""
🔍 LIKELY ROOT CAUSES OF SCORE 0:

1. 📋 MISSING COMPLIANCE DATA:
   - If no compliance requirements in database → score 0
   - Criticism needs requirements to analyze against
   
2. 🔗 LLM CONNECTION ISSUES:
   - If LLM server down/overloaded → score 0
   - If model not available → score 0
   
3. 📊 DATA STRUCTURE MISMATCH:
   - If proposal data malformed → analysis fails
   - If compliance data invalid → analysis fails

4. ⚠️ EMPTY PROPOSAL CONTENT:
   - If no actual content to analyze → score 0
   - If content too short/meaningless → low scores

🎯 NEXT STEPS:
1. Check if compliance data exists in database
2. Verify LLM server is running and accessible
3. Ensure proposal has meaningful content
4. Test with sample compliance data if needed
""")
    
    print("=" * 70)


if __name__ == "__main__":
    asyncio.run(main())
