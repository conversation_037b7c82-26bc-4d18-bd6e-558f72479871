import os
from typing import Optional,List

from pydantic_settings import BaseSettings
from pydantic import Field

class Settings(BaseSettings):
    # Database Configuration - now using environment variables
    # Kontratar Database (kontratar_main and kontratar_global schemas)
    kontratar_db_host: str = Field("localhost", env="KONTRATAR_DB_HOST")
    kontratar_db_port: int = Field(5432, env="KONTRATAR_DB_PORT")
    kontratar_db_name: str = Field("postgres", env="KONTRATAR_DB_NAME")
    kontratar_db_user: str = Field("postgres", env="KONTRATAR_DB_USER")
    kontratar_db_password: str = Field("password", env="KONTRATAR_DB_PASSWORD")
    
    # Customer Database (opportunity schema)
    customer_db_host: str = Field("localhost", env="CUSTOMER_DB_HOST")
    customer_db_port: int = Field(5432, env="CUSTOMER_DB_PORT")
    customer_db_name: str = Field("postgres", env="CUSTOMER_DB_NAME")
    customer_db_user: str = Field("postgres", env="CUSTOMER_DB_USER")
    customer_db_password: str = Field("password", env="CUSTOMER_DB_PASSWORD")
    
    chromadb_port_1: str = Field("", env="CHROMADB_PORT_1")
    chromadb_port_2: str = Field("", env="CHROMADB_PORT_2")
    chromadb_port_3: str = Field("", env="CHROMADB_PORT_3")
    chromadb_port_4: str = Field("", env="CHROMADB_PORT_4")
    chromadb_port_5: str = Field("", env="CHROMADB_PORT_5")
    chromadb_protocol: str = Field("", env="CHROMADB_PROTOCOL")
    chromadb_server_name: str = Field("", env="CHROMADB_SERVER_NAME")
    
    # Application Configuration
    app_host: str = Field("0.0.0.0", env="APP_HOST")
    app_port: int = Field(8000, env="APP_PORT")
    debug: bool = Field(True, env="DEBUG")

    # LangSmith Configuration
    langchain_api_key: str = Field("", env="LANGCHAIN_API_KEY")
    langchain_project: str = Field("", env="LANGCHAIN_PROJECT")
    langchain_tracing_v2: str = Field("", env="LANGCHAIN_TRACING_V2")
    
    # Scheduler Configuration
    scheduler_interval_seconds: int = Field(60, env="SCHEDULER_INTERVAL_SECONDS")
    scheduler_enable_on_startup: bool = Field(False, env="SCHEDULER_ENABLE_ON_STARTUP")
    
    # Google OAuth Configuration
    google_client_id: str = Field("", env="GOOGLE_CLIENT_ID")
    google_client_secret: str = Field("", env="GOOGLE_CLIENT_SECRET")

    # Gemini API Configuration
    gemini_api_key: str = Field("", env="GEMINI_API_KEY")

    # LLM Configuration
    llm_provider: str = Field("gemini", env="LLM_PROVIDER")
    llm_model: str = Field("gemini-1.5-flash", env="LLM_MODEL")

    
    # ChromaDB Configuration - using the fields defined above
    @property
    def chromadb_ports(self) -> list[int]:
        return [
            int(port) for port in [
            self.chromadb_port_1,
            self.chromadb_port_2,
            self.chromadb_port_3,
            self.chromadb_port_4,
            self.chromadb_port_5,
        ] if port and port.isdigit()
    ]
    
    
    @property
    def chromadb_instance_urls(self) -> list[str]:
        return [
            f"{self.chromadb_protocol}://{self.chromadb_server_name}:{port}"
            for port in self.chromadb_ports
        ]
        
    
    
    class Config:
        extra = "ignore"
        env_file = ".env"
        case_sensitive = False
        extra = "allow"


settings = Settings()
print("Loaded ChromaDB ports:", settings.chromadb_ports)
print("Loaded ChromaDB instance URLs:", settings.chromadb_instance_urls)
print("loaded langchain_api_key:", settings.langchain_api_key)


def get_kontratar_db_url() -> str:
    """Get the database URL for kontratar database"""
    return f"postgresql+asyncpg://{settings.kontratar_db_user}:{settings.kontratar_db_password}@{settings.kontratar_db_host}:{settings.kontratar_db_port}/{settings.kontratar_db_name}"


def get_customer_db_url() -> str:
    """Get the database URL for customer database"""
    return f"postgresql+asyncpg://{settings.customer_db_user}:{settings.customer_db_password}@{settings.customer_db_host}:{settings.customer_db_port}/{settings.customer_db_name}" 