2025-08-12 06:43:34,203 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-12 06:43:34,207 - apscheduler.scheduler - INFO - Added job "Process Proposal Queue" to job store "default"
2025-08-12 06:43:34,207 - apscheduler.scheduler - INFO - Scheduler started
2025-08-12 06:43:34,208 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-12 06:43:34,208 - apscheduler.scheduler - INFO - Added job "Proposal Criticism Processor" to job store "default"
2025-08-12 06:43:34,208 - apscheduler.scheduler - INFO - Scheduler started
2025-08-12 06:43:34,209 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-12 06:43:34,209 - apscheduler.scheduler - INFO - Added job "Process Custom Opps Queue" to job store "default"
2025-08-12 06:43:34,209 - apscheduler.scheduler - INFO - Scheduler started
2025-08-12 06:43:34,212 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-12 06:43:34,213 - apscheduler.scheduler - INFO - Added job "Process SAM Opps Queue" to job store "default"
2025-08-12 06:43:34,214 - apscheduler.scheduler - INFO - Scheduler started
2025-08-12 06:43:34,216 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-12 06:43:34,216 - apscheduler.scheduler - INFO - Added job "Process Client Process Queue" to job store "default"
2025-08-12 06:43:34,217 - apscheduler.scheduler - INFO - Scheduler started
2025-08-12 06:43:34,218 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-12 06:43:34,218 - apscheduler.scheduler - INFO - Added job "Process Datametastore Queue" to job store "default"
2025-08-12 06:43:34,219 - apscheduler.scheduler - INFO - Scheduler started
2025-08-12 06:43:34,219 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-12 06:43:34,220 - apscheduler.scheduler - INFO - Added job "Process Simulation Queue" to job store "default"
2025-08-12 06:43:34,220 - apscheduler.scheduler - INFO - Scheduler started
2025-08-12 06:43:34,220 - apscheduler.scheduler - INFO - Adding job tentatively -- it will be properly scheduled when the scheduler starts
2025-08-12 06:43:34,221 - apscheduler.scheduler - INFO - Added job "Process Proposal Outline Queue" to job store "default"
2025-08-12 06:43:34,221 - apscheduler.scheduler - INFO - Scheduler started
2025-08-12 06:44:04,221 - apscheduler.executors.default - INFO - Running job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:44:34 WAT)" (scheduled at 2025-08-12 06:44:04.220689+01:00)
2025-08-12 06:44:04,223 - apscheduler.executors.default - INFO - Job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:44:34 WAT)" executed successfully
2025-08-12 06:44:34,203 - apscheduler.executors.default - INFO - Running job "Process Proposal Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:45:34 WAT)" (scheduled at 2025-08-12 06:44:34.203432+01:00)
2025-08-12 06:44:34,204 - apscheduler.executors.default - INFO - Job "Process Proposal Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:45:34 WAT)" executed successfully
2025-08-12 06:44:34,209 - apscheduler.executors.default - INFO - Running job "Process Custom Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:45:34 WAT)" (scheduled at 2025-08-12 06:44:34.209094+01:00)
2025-08-12 06:44:34,209 - apscheduler.executors.default - INFO - Job "Process Custom Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:45:34 WAT)" executed successfully
2025-08-12 06:44:34,212 - apscheduler.executors.default - INFO - Running job "Process SAM Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:45:34 WAT)" (scheduled at 2025-08-12 06:44:34.212265+01:00)
2025-08-12 06:44:34,213 - apscheduler.executors.default - INFO - Job "Process SAM Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:45:34 WAT)" executed successfully
2025-08-12 06:44:34,217 - apscheduler.executors.default - INFO - Running job "Process Client Process Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:45:34 WAT)" (scheduled at 2025-08-12 06:44:34.216040+01:00)
2025-08-12 06:44:34,218 - apscheduler.executors.default - INFO - Job "Process Client Process Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:45:34 WAT)" executed successfully
2025-08-12 06:44:34,218 - apscheduler.executors.default - INFO - Running job "Process Datametastore Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:45:34 WAT)" (scheduled at 2025-08-12 06:44:34.218046+01:00)
2025-08-12 06:44:34,219 - apscheduler.executors.default - INFO - Job "Process Datametastore Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:45:34 WAT)" executed successfully
2025-08-12 06:44:34,219 - apscheduler.executors.default - INFO - Running job "Process Simulation Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:45:34 WAT)" (scheduled at 2025-08-12 06:44:34.219702+01:00)
2025-08-12 06:44:34,220 - apscheduler.executors.default - INFO - Job "Process Simulation Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:45:34 WAT)" executed successfully
2025-08-12 06:44:34,221 - apscheduler.executors.default - INFO - Running job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:45:04 WAT)" (scheduled at 2025-08-12 06:44:34.220689+01:00)
2025-08-12 06:44:34,221 - apscheduler.executors.default - INFO - Job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:45:04 WAT)" executed successfully
2025-08-12 06:45:04,220 - apscheduler.executors.default - INFO - Running job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:45:34 WAT)" (scheduled at 2025-08-12 06:45:04.220689+01:00)
2025-08-12 06:45:04,222 - apscheduler.executors.default - INFO - Job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:45:34 WAT)" executed successfully
2025-08-12 06:45:34,203 - apscheduler.executors.default - INFO - Running job "Process Proposal Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:46:34 WAT)" (scheduled at 2025-08-12 06:45:34.203432+01:00)
2025-08-12 06:45:34,204 - apscheduler.executors.default - INFO - Job "Process Proposal Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:46:34 WAT)" executed successfully
2025-08-12 06:45:34,210 - apscheduler.executors.default - INFO - Running job "Process Custom Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:46:34 WAT)" (scheduled at 2025-08-12 06:45:34.209094+01:00)
2025-08-12 06:45:34,211 - apscheduler.executors.default - INFO - Job "Process Custom Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:46:34 WAT)" executed successfully
2025-08-12 06:45:34,212 - apscheduler.executors.default - INFO - Running job "Process SAM Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:46:34 WAT)" (scheduled at 2025-08-12 06:45:34.212265+01:00)
2025-08-12 06:45:34,213 - apscheduler.executors.default - INFO - Job "Process SAM Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:46:34 WAT)" executed successfully
2025-08-12 06:45:34,216 - apscheduler.executors.default - INFO - Running job "Process Client Process Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:46:34 WAT)" (scheduled at 2025-08-12 06:45:34.216040+01:00)
2025-08-12 06:45:34,217 - apscheduler.executors.default - INFO - Job "Process Client Process Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:46:34 WAT)" executed successfully
2025-08-12 06:45:34,218 - apscheduler.executors.default - INFO - Running job "Process Datametastore Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:46:34 WAT)" (scheduled at 2025-08-12 06:45:34.218046+01:00)
2025-08-12 06:45:34,218 - apscheduler.executors.default - INFO - Job "Process Datametastore Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:46:34 WAT)" executed successfully
2025-08-12 06:45:34,220 - apscheduler.executors.default - INFO - Running job "Process Simulation Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:46:34 WAT)" (scheduled at 2025-08-12 06:45:34.219702+01:00)
2025-08-12 06:45:34,220 - apscheduler.executors.default - INFO - Job "Process Simulation Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:46:34 WAT)" executed successfully
2025-08-12 06:45:34,220 - apscheduler.executors.default - INFO - Running job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:46:04 WAT)" (scheduled at 2025-08-12 06:45:34.220689+01:00)
2025-08-12 06:45:34,221 - apscheduler.executors.default - INFO - Job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:46:04 WAT)" executed successfully
2025-08-12 06:46:04,220 - apscheduler.executors.default - INFO - Running job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:46:34 WAT)" (scheduled at 2025-08-12 06:46:04.220689+01:00)
2025-08-12 06:46:04,223 - apscheduler.executors.default - INFO - Job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:46:34 WAT)" executed successfully
2025-08-12 06:46:34,204 - apscheduler.executors.default - INFO - Running job "Process Proposal Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:47:34 WAT)" (scheduled at 2025-08-12 06:46:34.203432+01:00)
2025-08-12 06:46:34,204 - apscheduler.executors.default - INFO - Job "Process Proposal Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:47:34 WAT)" executed successfully
2025-08-12 06:46:34,210 - apscheduler.executors.default - INFO - Running job "Process Custom Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:47:34 WAT)" (scheduled at 2025-08-12 06:46:34.209094+01:00)
2025-08-12 06:46:34,211 - apscheduler.executors.default - INFO - Job "Process Custom Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:47:34 WAT)" executed successfully
2025-08-12 06:46:34,212 - apscheduler.executors.default - INFO - Running job "Process SAM Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:47:34 WAT)" (scheduled at 2025-08-12 06:46:34.212265+01:00)
2025-08-12 06:46:34,213 - apscheduler.executors.default - INFO - Job "Process SAM Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:47:34 WAT)" executed successfully
2025-08-12 06:46:34,216 - apscheduler.executors.default - INFO - Running job "Process Client Process Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:47:34 WAT)" (scheduled at 2025-08-12 06:46:34.216040+01:00)
2025-08-12 06:46:34,216 - apscheduler.executors.default - INFO - Job "Process Client Process Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:47:34 WAT)" executed successfully
2025-08-12 06:46:34,218 - apscheduler.executors.default - INFO - Running job "Process Datametastore Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:47:34 WAT)" (scheduled at 2025-08-12 06:46:34.218046+01:00)
2025-08-12 06:46:34,218 - apscheduler.executors.default - INFO - Job "Process Datametastore Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:47:34 WAT)" executed successfully
2025-08-12 06:46:34,220 - apscheduler.executors.default - INFO - Running job "Process Simulation Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:47:34 WAT)" (scheduled at 2025-08-12 06:46:34.219702+01:00)
2025-08-12 06:46:34,220 - apscheduler.executors.default - INFO - Job "Process Simulation Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:47:34 WAT)" executed successfully
2025-08-12 06:46:34,220 - apscheduler.executors.default - INFO - Running job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:47:04 WAT)" (scheduled at 2025-08-12 06:46:34.220689+01:00)
2025-08-12 06:46:34,221 - apscheduler.executors.default - INFO - Job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:47:04 WAT)" executed successfully
2025-08-12 06:47:04,220 - apscheduler.executors.default - INFO - Running job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:47:34 WAT)" (scheduled at 2025-08-12 06:47:04.220689+01:00)
2025-08-12 06:47:04,221 - apscheduler.executors.default - INFO - Job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:47:34 WAT)" executed successfully
2025-08-12 06:47:34,204 - apscheduler.executors.default - INFO - Running job "Process Proposal Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:48:34 WAT)" (scheduled at 2025-08-12 06:47:34.203432+01:00)
2025-08-12 06:47:34,205 - apscheduler.executors.default - INFO - Job "Process Proposal Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:48:34 WAT)" executed successfully
2025-08-12 06:47:34,209 - apscheduler.executors.default - INFO - Running job "Process Custom Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:48:34 WAT)" (scheduled at 2025-08-12 06:47:34.209094+01:00)
2025-08-12 06:47:34,210 - apscheduler.executors.default - INFO - Job "Process Custom Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:48:34 WAT)" executed successfully
2025-08-12 06:47:34,212 - apscheduler.executors.default - INFO - Running job "Process SAM Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:48:34 WAT)" (scheduled at 2025-08-12 06:47:34.212265+01:00)
2025-08-12 06:47:34,213 - apscheduler.executors.default - INFO - Job "Process SAM Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:48:34 WAT)" executed successfully
2025-08-12 06:47:34,217 - apscheduler.executors.default - INFO - Running job "Process Client Process Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:48:34 WAT)" (scheduled at 2025-08-12 06:47:34.216040+01:00)
2025-08-12 06:47:34,217 - apscheduler.executors.default - INFO - Job "Process Client Process Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:48:34 WAT)" executed successfully
2025-08-12 06:47:34,218 - apscheduler.executors.default - INFO - Running job "Process Datametastore Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:48:34 WAT)" (scheduled at 2025-08-12 06:47:34.218046+01:00)
2025-08-12 06:47:34,218 - apscheduler.executors.default - INFO - Job "Process Datametastore Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:48:34 WAT)" executed successfully
2025-08-12 06:47:34,220 - apscheduler.executors.default - INFO - Running job "Process Simulation Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:48:34 WAT)" (scheduled at 2025-08-12 06:47:34.219702+01:00)
2025-08-12 06:47:34,222 - apscheduler.executors.default - INFO - Job "Process Simulation Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:48:34 WAT)" executed successfully
2025-08-12 06:47:34,223 - apscheduler.executors.default - INFO - Running job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:48:04 WAT)" (scheduled at 2025-08-12 06:47:34.220689+01:00)
2025-08-12 06:47:34,224 - apscheduler.executors.default - INFO - Job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:48:04 WAT)" executed successfully
2025-08-12 06:48:04,220 - apscheduler.executors.default - INFO - Running job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:48:34 WAT)" (scheduled at 2025-08-12 06:48:04.220689+01:00)
2025-08-12 06:48:04,221 - apscheduler.executors.default - INFO - Job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:48:34 WAT)" executed successfully
2025-08-12 06:48:34,203 - apscheduler.executors.default - INFO - Running job "Process Proposal Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:49:34 WAT)" (scheduled at 2025-08-12 06:48:34.203432+01:00)
2025-08-12 06:48:34,204 - apscheduler.executors.default - INFO - Job "Process Proposal Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:49:34 WAT)" executed successfully
2025-08-12 06:48:34,208 - apscheduler.executors.default - INFO - Running job "Proposal Criticism Processor (trigger: interval[0:05:00], next run at: 2025-08-12 06:53:34 WAT)" (scheduled at 2025-08-12 06:48:34.208312+01:00)
2025-08-12 06:48:34,213 - apscheduler.executors.default - INFO - Running job "Process Custom Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:49:34 WAT)" (scheduled at 2025-08-12 06:48:34.209094+01:00)
2025-08-12 06:48:34,213 - apscheduler.executors.default - INFO - Job "Process Custom Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:49:34 WAT)" executed successfully
2025-08-12 06:48:34,213 - apscheduler.executors.default - INFO - Running job "Process SAM Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:49:34 WAT)" (scheduled at 2025-08-12 06:48:34.212265+01:00)
2025-08-12 06:48:34,214 - apscheduler.executors.default - INFO - Job "Process SAM Opps Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:49:34 WAT)" executed successfully
2025-08-12 06:48:34,216 - apscheduler.executors.default - INFO - Running job "Process Client Process Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:49:34 WAT)" (scheduled at 2025-08-12 06:48:34.216040+01:00)
2025-08-12 06:48:34,217 - apscheduler.executors.default - INFO - Job "Process Client Process Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:49:34 WAT)" executed successfully
2025-08-12 06:48:34,218 - apscheduler.executors.default - INFO - Running job "Process Datametastore Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:49:34 WAT)" (scheduled at 2025-08-12 06:48:34.218046+01:00)
2025-08-12 06:48:34,219 - apscheduler.executors.default - INFO - Job "Process Datametastore Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:49:34 WAT)" executed successfully
2025-08-12 06:48:34,219 - apscheduler.executors.default - INFO - Running job "Process Simulation Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:49:34 WAT)" (scheduled at 2025-08-12 06:48:34.219702+01:00)
2025-08-12 06:48:34,220 - apscheduler.executors.default - INFO - Job "Process Simulation Queue (trigger: interval[0:01:00], next run at: 2025-08-12 06:49:34 WAT)" executed successfully
2025-08-12 06:48:34,222 - apscheduler.executors.default - INFO - Running job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:49:04 WAT)" (scheduled at 2025-08-12 06:48:34.220689+01:00)
2025-08-12 06:48:34,222 - apscheduler.executors.default - INFO - Job "Process Proposal Outline Queue (trigger: interval[0:00:30], next run at: 2025-08-12 06:49:04 WAT)" executed successfully
