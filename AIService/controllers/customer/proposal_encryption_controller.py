from datetime import datetime
from typing import List, Optional

from loguru import logger
from models.customer_models import ProposalEncryption
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession


class ProposalEncryptionController:
    """Controller for proposal encryption operations"""
    
    @staticmethod
    async def get_by_tenant_id(db: AsyncSession, tenant_id: str) -> Optional[ProposalEncryption]:
        """Get proposal encryption by tenant ID"""
        try:
            query = select(ProposalEncryption).where(
                ProposalEncryption.tenant_id == tenant_id
            )
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting proposal encryption for tenant {tenant_id}: {e}")
            return None
    
    @staticmethod
    async def get_all(db: AsyncSession, limit: int = 100) -> List[ProposalEncryption]:
        """Get all proposal encryption records"""
        try:
            query = select(ProposalEncryption).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting all proposal encryption records: {e}")
            return []
    
    @staticmethod
    async def add(
        db: AsyncSession,
        tenant_id: str,
        public_key: bytes,
        encrypted_private_key: bytes,
        salt: bytes,
        passphrase_hash: str
    ) -> Optional[ProposalEncryption]:
        """Add new proposal encryption record"""
        try:
            new_record = ProposalEncryption(
                tenant_id=tenant_id,
                public_key=public_key,
                encrypted_private_key=encrypted_private_key,
                salt=salt,
                passphrase_hash=passphrase_hash,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.add(new_record)
            await db.commit()
            await db.refresh(new_record)
            
            logger.info(f"Created new proposal encryption record for tenant {tenant_id}")
            return new_record
        except Exception as e:
            logger.error(f"Error creating proposal encryption record: {e}")
            await db.rollback()
            return None
    
    @staticmethod
    async def update(
        db: AsyncSession,
        tenant_id: str,
        public_key: Optional[bytes] = None,
        encrypted_private_key: Optional[bytes] = None,
        salt: Optional[bytes] = None,
        passphrase_hash: Optional[str] = None
    ) -> bool:
        """Update proposal encryption record"""
        try:
            record = await ProposalEncryptionController.get_by_tenant_id(db, tenant_id)
            if not record:
                return False

            update_fields = {
                "public_key": public_key,
                "encrypted_private_key": encrypted_private_key,
                "salt": salt,
                "passphrase_hash": passphrase_hash
            }
            for field, value in update_fields.items():
                if value is not None:
                    setattr(record, field, value)

            setattr(record, "updated_at", datetime.utcnow())

            await db.commit()
            logger.info(f"Updated proposal encryption record for tenant {tenant_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating proposal encryption record: {e}")
            await db.rollback()
            return False 
        
    @staticmethod
    async def exists_by_tenant_id(db: AsyncSession, tenant_id: str) -> bool:
        """Check if proposal encryption exists for tenant ID"""
        try:
            query = select(ProposalEncryption).where(
                ProposalEncryption.tenant_id == tenant_id
            )
            result = await db.execute(query)
            return result.scalar_one_or_none() is not None
        except Exception as e:
            logger.error(f"Error checking existence for tenant {tenant_id}: {e}")
            return False
    
    @staticmethod
    async def delete(db: AsyncSession, tenant_id: str) -> bool:
        """Delete proposal encryption record by tenant ID"""
        try:
            record = await ProposalEncryptionController.get_by_tenant_id(db, tenant_id)
            if not record:
                return False
            await db.delete(record)
            await db.commit()
            logger.info(f"Deleted proposal encryption record for tenant {tenant_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting proposal encryption record: {e}")
            await db.rollback()
            return False