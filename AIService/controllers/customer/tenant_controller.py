from typing import Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from models.customer_models import AESTenant
from loguru import logger

class TenantController:
    
    @staticmethod
    async def get_by_tenant_id(
        db: AsyncSession,
        tenant_id: str
    ) -> Optional[Any]:
        """
        Get an AESTenant record by tenant_id, returning only selected columns.
        """
        try:
            query = select(
                AESTenant.tenant_name,
                AESTenant.tenant_primary_contact_firstname,
                AESTenant.tenant_primary_contact_lastname,
                AESTenant.tenant_primary_contact_email,
                AESTenant.tenant_primary_address_1,
                AESTenant.tenant_primary_address_2,
                AESTenant.tenant_primary_city,
                AESTenant.tenant_primary_state,
                AESTenant.tenant_primary_country,
                AESTenant.tenant_primary_zipcode,
                AESTenant.tenant_domain,
                AESTenant.duns_number,
                AESTenant.cage_code,
                AESTenant.sam_entity_id
            ).where(AESTenant.tenant_id == tenant_id)
            result = await db.execute(query)
            #print(f"tenant result: {result.mappings().one_or_none()}")
            return result.mappings().one_or_none()
        except Exception as e:
            logger.error(f"Error fetching AESTenant record by tenant_id={tenant_id}: {e}")
            return None