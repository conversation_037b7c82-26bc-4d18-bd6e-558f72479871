 # AIService/controllers/customer/key_personnel_uploads_controller.py

from typing import Any, Dict, Optional, List
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from models.customer_models import KeyPersonnelUploads

class KeyPersonnelUploadsController:
    """Controller for KeyPersonnelUploads operations"""
    
    @staticmethod
    async def get_all_by_tenant_and_opportunity(
        db: AsyncSession,
        tenant_id: str,
        opportunity_id: str
    ) -> List[Dict[str, Any]]:
        """
        Get all job_title, resume_file, and resume_text combinations by tenant_id and opportunity_id.
        Returns a list of dictionaries containing the requested fields.
        """
        try:
            query = select(
                KeyPersonnelUploads.job_title,
                KeyPersonnelUploads.resume_file,
                KeyPersonnelUploads.resume_text
            ).where(
                KeyPersonnelUploads.tenant_id == tenant_id,
                KeyPersonnelUploads.opportunity_id == opportunity_id
            )
            result = await db.execute(query)
            rows = result.mappings().all()
            
            return [
                {
                    "job_title": row.job_title,
                    "resume_file": row.resume_file,
                    "resume_text": row.resume_text
                }
                for row in rows
            ]
        except Exception as e:
            logger.error(f"Error fetching KeyPersonnelUploads by tenant_id={tenant_id} and opportunity_id={opportunity_id}: {e}")
            return []

    @staticmethod
    async def get_by_id(
        db: AsyncSession,
        record_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get a KeyPersonnelUploads record by id.
        Returns a dictionary with all fields or None if not found.
        """
        try:
            query = select(KeyPersonnelUploads).where(KeyPersonnelUploads.id == record_id)
            result = await db.execute(query)
            record = result.scalar_one_or_none()
            
            if record:
                return {
                    "id": record.id,
                    "job_title": record.job_title,
                    "resume_file": record.resume_file,
                    "resume_text": record.resume_text,
                    "tenant_id": record.tenant_id,
                    "opportunity_id": record.opportunity_id,
                    "created_date": record.created_date,
                    "last_updated_date": record.last_updated_date
                }
            return None
        except Exception as e:
            logger.error(f"Error fetching KeyPersonnelUploads by id={record_id}: {e}")
            return None

    @staticmethod
    async def create_record(
        db: AsyncSession,
        record_data: Dict[str, Any]
    ) -> Optional[KeyPersonnelUploads]:
        """
        Create a new KeyPersonnelUploads record.
        Returns the created record or None if creation fails.
        """
        try:
            # Get valid columns from the model
            valid_columns = {col.name for col in KeyPersonnelUploads.__table__.columns}
            # Filter record_data to only valid columns
            safe_data = {k: v for k, v in record_data.items() if k in valid_columns}
            
            if not safe_data:
                logger.warning("No valid data provided for KeyPersonnelUploads creation.")
                return None

            new_record = KeyPersonnelUploads(**safe_data)
            db.add(new_record)
            await db.commit()
            await db.refresh(new_record)
            
            logger.info(f"Created new KeyPersonnelUploads record with id={new_record.id}")
            return new_record
        except Exception as e:
            logger.error(f"Error creating KeyPersonnelUploads record: {e}")
            await db.rollback()
            return None

    @staticmethod
    async def update_by_id(
        db: AsyncSession,
        record_id: str,
        update_fields: Dict[str, Any]
    ) -> Optional[KeyPersonnelUploads]:
        """
        Update fields of a KeyPersonnelUploads record by id.
        Only allows updating valid fields.
        Returns the updated record or None if not found.
        """
        # Get valid columns from the model
        valid_columns = {col.name for col in KeyPersonnelUploads.__table__.columns}
        # Filter update_fields to only valid columns
        safe_fields = {k: v for k, v in update_fields.items() if k in valid_columns and k != "id"}

        if not safe_fields:
            logger.warning("No valid fields to update for KeyPersonnelUploads.")
            return None

        try:
            # Fetch the record
            query = select(KeyPersonnelUploads).where(KeyPersonnelUploads.id == record_id)
            result = await db.execute(query)
            record = result.scalar_one_or_none()
            if not record:
                logger.warning(f"No KeyPersonnelUploads record found for id={record_id}")
                return None

            # Update fields
            for field, value in safe_fields.items():
                setattr(record, field, value)

            await db.commit()
            await db.refresh(record)
            logger.info(f"Updated KeyPersonnelUploads record with id={record_id}")
            return record
        except Exception as e:
            logger.error(f"Error updating KeyPersonnelUploads record: {e}")
            await db.rollback()
            return None

    @staticmethod
    async def delete_by_id(
        db: AsyncSession,
        record_id: str
    ) -> bool:
        """
        Delete a KeyPersonnelUploads record by id.
        Returns True if successful, False otherwise.
        """
        try:
            query = select(KeyPersonnelUploads).where(KeyPersonnelUploads.id == record_id)
            result = await db.execute(query)
            record = result.scalar_one_or_none()
            
            if not record:
                logger.warning(f"No KeyPersonnelUploads record found for id={record_id}")
                return False

            await db.delete(record)
            await db.commit()
            logger.info(f"Deleted KeyPersonnelUploads record with id={record_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting KeyPersonnelUploads record: {e}")
            await db.rollback()
            return False

    @staticmethod
    async def get_all_by_tenant_id(
        db: AsyncSession,
        tenant_id: str
    ) -> List[Dict[str, Any]]:
        """
        Get all KeyPersonnelUploads records by tenant_id.
        Returns a list of dictionaries containing all fields.
        """
        try:
            query = select(KeyPersonnelUploads).where(KeyPersonnelUploads.tenant_id == tenant_id)
            result = await db.execute(query)
            records = result.scalars().all()
            
            return [
                {
                    "id": record.id,
                    "job_title": record.job_title,
                    "resume_file": record.resume_file,
                    "resume_text": record.resume_text,
                    "tenant_id": record.tenant_id,
                    "opportunity_id": record.opportunity_id,
                    "created_date": record.created_date,
                    "last_updated_date": record.last_updated_date
                }
                for record in records
            ]
        except Exception as e:
            logger.error(f"Error fetching KeyPersonnelUploads by tenant_id={tenant_id}: {e}")
            return []