from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from models.customer_models import ClientProcessQueue
from loguru import logger
from datetime import datetime

class ClientProcessQueueController:
    @staticmethod
    async def get_new_queue_items(db: AsyncSession, limit: int = 10) -> List[ClientProcessQueue]:
        try:
            query = select(ClientProcessQueue).where(
                ClientProcessQueue.status == "NEW"
            ).order_by(ClientProcessQueue.creation_date.asc()).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting new client process queue items: {e}")
            return []

    @staticmethod
    async def update_queue_status(db: AsyncSession, job_id: str, status: str) -> bool:
        try:
            query = update(ClientProcessQueue).where(
                ClientProcessQueue.job_id == job_id
            ).values(
                status=status,
                last_updated_date=datetime.utcnow()
            )
            result = await db.execute(query)
            await db.commit()
            logger.info(f"Updated client process queue status for job_id {job_id} to {status}")
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating client process queue status: {e}")
            await db.rollback()
            return False