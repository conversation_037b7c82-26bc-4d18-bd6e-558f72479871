"""
Proposal Criticism Controller

Controller for managing proposal criticism results in the database.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from models.customer_models import ProposalCriticismResults
from loguru import logger


class ProposalCriticismController:
    """Controller for proposal criticism results operations"""
    
    @staticmethod
    async def create_criticism_result(
        db: AsyncSession,
        opportunity_id: str,
        tenant_id: str,
        criticism_data: Dict[str, Any]
    ) -> ProposalCriticismResults:
        """
        Create a new proposal criticism result record.
        
        Args:
            db: Database session
            opportunity_id: Opportunity identifier
            tenant_id: Tenant identifier
            criticism_data: Criticism analysis results
            
        Returns:
            Created ProposalCriticismResults record
        """
        try:
            # Extract scores from criticism data
            structure_score = criticism_data.get('structure_criticism', {}).get('score', 0)
            content_score = criticism_data.get('content_criticism', {}).get('score', 0)
            formatting_score = criticism_data.get('formatting_criticism', {}).get('score', 0)
            technical_score = criticism_data.get('technical_criticism', {}).get('score', 0)
            
            # Calculate overall score
            scores = [s for s in [structure_score, content_score, formatting_score, technical_score] if s > 0]
            overall_score = sum(scores) // len(scores) if scores else 0
            
            # Count issues and strengths
            total_issues = 0
            total_strengths = 0
            
            for criticism_type in ['structure_criticism', 'content_criticism', 'formatting_criticism', 'technical_criticism']:
                criticism = criticism_data.get(criticism_type, {})
                total_issues += len(criticism.get('issues', []))
                total_strengths += len(criticism.get('strengths', []))
            
            # Create the record
            criticism_result = ProposalCriticismResults(
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                client_short_name=criticism_data.get('client_short_name', ''),
                generation_method=criticism_data.get('generation_method', 'unknown'),
                structure_score=structure_score,
                content_score=content_score,
                formatting_score=formatting_score,
                technical_score=technical_score,
                overall_score=overall_score,
                structure_criticism=str(criticism_data.get('structure_criticism', {})),
                content_criticism=str(criticism_data.get('content_criticism', {})),
                formatting_criticism=str(criticism_data.get('formatting_criticism', {})),
                technical_criticism=str(criticism_data.get('technical_criticism', {})),
                total_issues=total_issues,
                total_strengths=total_strengths,
                improvement_suggestions=criticism_data.get('improvement_suggestions', ''),
                analyzed_sections_count=criticism_data.get('analyzed_sections_count', 0),
                processing_time_seconds=criticism_data.get('processing_time_seconds', 0.0)
            )
            
            db.add(criticism_result)
            await db.commit()
            await db.refresh(criticism_result)
            
            logger.info(f"Created criticism result for opportunity {opportunity_id} with overall score {overall_score}")
            return criticism_result
            
        except Exception as e:
            await db.rollback()
            logger.error(f"Error creating criticism result: {e}")
            raise
    
    @staticmethod
    async def get_criticism_by_opportunity(
        db: AsyncSession,
        opportunity_id: str,
        tenant_id: str
    ) -> Optional[ProposalCriticismResults]:
        """
        Get the latest criticism result for an opportunity.
        
        Args:
            db: Database session
            opportunity_id: Opportunity identifier
            tenant_id: Tenant identifier
            
        Returns:
            Latest ProposalCriticismResults record or None
        """
        try:
            query = select(ProposalCriticismResults).where(
                and_(
                    ProposalCriticismResults.opportunity_id == opportunity_id,
                    ProposalCriticismResults.tenant_id == tenant_id
                )
            ).order_by(desc(ProposalCriticismResults.analysis_timestamp))
            
            result = await db.execute(query)
            return result.scalars().first()
            
        except Exception as e:
            logger.error(f"Error getting criticism for opportunity {opportunity_id}: {e}")
            return None
    
    @staticmethod
    async def get_recent_criticisms(
        db: AsyncSession,
        tenant_id: str,
        hours: int = 24,
        limit: int = 100
    ) -> List[ProposalCriticismResults]:
        """
        Get recent criticism results for a tenant.
        
        Args:
            db: Database session
            tenant_id: Tenant identifier
            hours: Number of hours to look back
            limit: Maximum number of results
            
        Returns:
            List of recent ProposalCriticismResults
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            query = select(ProposalCriticismResults).where(
                and_(
                    ProposalCriticismResults.tenant_id == tenant_id,
                    ProposalCriticismResults.analysis_timestamp >= cutoff_time
                )
            ).order_by(desc(ProposalCriticismResults.analysis_timestamp)).limit(limit)
            
            result = await db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Error getting recent criticisms for tenant {tenant_id}: {e}")
            return []
    
    @staticmethod
    async def get_criticism_statistics(
        db: AsyncSession,
        tenant_id: str,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get criticism statistics for a tenant.
        
        Args:
            db: Database session
            tenant_id: Tenant identifier
            days: Number of days to analyze
            
        Returns:
            Dictionary with criticism statistics
        """
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            
            # Get basic statistics
            query = select(
                func.count(ProposalCriticismResults.id).label('total_analyses'),
                func.avg(ProposalCriticismResults.overall_score).label('avg_overall_score'),
                func.avg(ProposalCriticismResults.structure_score).label('avg_structure_score'),
                func.avg(ProposalCriticismResults.content_score).label('avg_content_score'),
                func.avg(ProposalCriticismResults.formatting_score).label('avg_formatting_score'),
                func.avg(ProposalCriticismResults.technical_score).label('avg_technical_score'),
                func.sum(ProposalCriticismResults.total_issues).label('total_issues'),
                func.sum(ProposalCriticismResults.total_strengths).label('total_strengths')
            ).where(
                and_(
                    ProposalCriticismResults.tenant_id == tenant_id,
                    ProposalCriticismResults.analysis_timestamp >= cutoff_time
                )
            )
            
            result = await db.execute(query)
            stats = result.first()
            
            if not stats or stats.total_analyses == 0:
                return {
                    'total_analyses': 0,
                    'avg_overall_score': 0,
                    'avg_structure_score': 0,
                    'avg_content_score': 0,
                    'avg_formatting_score': 0,
                    'avg_technical_score': 0,
                    'total_issues': 0,
                    'total_strengths': 0,
                    'improvement_trend': 'no_data'
                }
            
            # Calculate improvement trend (compare first half vs second half of period)
            mid_time = cutoff_time + timedelta(days=days//2)
            
            # First half average
            first_half_query = select(
                func.avg(ProposalCriticismResults.overall_score)
            ).where(
                and_(
                    ProposalCriticismResults.tenant_id == tenant_id,
                    ProposalCriticismResults.analysis_timestamp >= cutoff_time,
                    ProposalCriticismResults.analysis_timestamp < mid_time
                )
            )
            
            # Second half average
            second_half_query = select(
                func.avg(ProposalCriticismResults.overall_score)
            ).where(
                and_(
                    ProposalCriticismResults.tenant_id == tenant_id,
                    ProposalCriticismResults.analysis_timestamp >= mid_time
                )
            )
            
            first_half_result = await db.execute(first_half_query)
            second_half_result = await db.execute(second_half_query)
            
            first_half_avg = first_half_result.scalar() or 0
            second_half_avg = second_half_result.scalar() or 0
            
            if first_half_avg > 0 and second_half_avg > 0:
                if second_half_avg > first_half_avg + 5:
                    improvement_trend = 'improving'
                elif second_half_avg < first_half_avg - 5:
                    improvement_trend = 'declining'
                else:
                    improvement_trend = 'stable'
            else:
                improvement_trend = 'insufficient_data'
            
            return {
                'total_analyses': stats.total_analyses,
                'avg_overall_score': round(float(stats.avg_overall_score or 0), 1),
                'avg_structure_score': round(float(stats.avg_structure_score or 0), 1),
                'avg_content_score': round(float(stats.avg_content_score or 0), 1),
                'avg_formatting_score': round(float(stats.avg_formatting_score or 0), 1),
                'avg_technical_score': round(float(stats.avg_technical_score or 0), 1),
                'total_issues': stats.total_issues or 0,
                'total_strengths': stats.total_strengths or 0,
                'improvement_trend': improvement_trend
            }
            
        except Exception as e:
            logger.error(f"Error getting criticism statistics for tenant {tenant_id}: {e}")
            return {}
    
    @staticmethod
    async def check_if_analyzed(
        db: AsyncSession,
        opportunity_id: str,
        tenant_id: str,
        hours: int = 24
    ) -> bool:
        """
        Check if an opportunity has been analyzed recently.
        
        Args:
            db: Database session
            opportunity_id: Opportunity identifier
            tenant_id: Tenant identifier
            hours: Hours to look back
            
        Returns:
            True if already analyzed recently
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            query = select(func.count(ProposalCriticismResults.id)).where(
                and_(
                    ProposalCriticismResults.opportunity_id == opportunity_id,
                    ProposalCriticismResults.tenant_id == tenant_id,
                    ProposalCriticismResults.analysis_timestamp >= cutoff_time
                )
            )
            
            result = await db.execute(query)
            count = result.scalar()
            
            return count > 0
            
        except Exception as e:
            logger.error(f"Error checking if opportunity {opportunity_id} was analyzed: {e}")
            return False
