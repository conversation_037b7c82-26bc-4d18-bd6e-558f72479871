from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from models.customer_models import ClientProfile
from loguru import logger

class ClientProfileController:
    @staticmethod
    async def get_by_id(db: AsyncSession, client_profile_id: int) -> Optional[ClientProfile]:
        """
        Get a client profile record by ID
        
        Args:
            db: AsyncSession - Database session
            client_profile_id: int - The ID of the client profile to retrieve
            
        Returns:
            Optional[ClientProfile]: Client profile record or None if not found
        """
        try:
            query = select(ClientProfile).where(ClientProfile.id == client_profile_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting client profile by ID {client_profile_id}: {e}")
            return None

    @staticmethod
    async def get_by_tenant_id(db: AsyncSession, tenant_id: str, limit: int = 20) -> List[ClientProfile]:
        """
        Get client profiles by tenant ID
        
        Args:
            db: AsyncSession - Database session
            tenant_id: str - The tenant ID to filter by
            limit: int - Maximum number of records to return
            
        Returns:
            List[ClientProfile]: List of client profile records
        """
        try:
            query = select(ClientProfile).where(ClientProfile.tenant_id == tenant_id).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting client profiles by tenant ID {tenant_id}: {e}")
            return []

    @staticmethod
    async def get_default_profiles(db: AsyncSession, limit: int = 20) -> List[ClientProfile]:
        """
        Get default client profiles
        
        Args:
            db: AsyncSession - Database session
            limit: int - Maximum number of records to return
            
        Returns:
            List[ClientProfile]: List of default client profile records
        """
        try:
            query = select(ClientProfile).where(ClientProfile.is_default == True).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting default client profiles: {e}")
            return []

    @staticmethod
    async def get_by_client_short_name(db: AsyncSession, client_short_name: str) -> List[ClientProfile]:
        """
        Get client profiles by client short name
        
        Args:
            db: AsyncSession - Database session
            client_short_name: str - The client short name to filter by
            
        Returns:
            List[ClientProfile]: List of client profile records
        """
        try:
            query = select(ClientProfile).where(ClientProfile.client_short_name == client_short_name)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting client profiles by short name {client_short_name}: {e}")
            return []

    @staticmethod
    async def update_by_id(
        db: AsyncSession,
        client_profile_id: int,
        client_short_name: Optional[str] = None,
        profile_id: Optional[str] = None,
        tenant_id: Optional[str] = None,
        title: Optional[str] = None,
        is_default: Optional[bool] = None
    ) -> bool:
        """
        Update a client profile by ID
        
        Args:
            db: AsyncSession - Database session
            client_profile_id: int - The ID of the client profile to update
            client_short_name: Optional[str] - New client short name
            profile_id: Optional[str] - New profile ID
            tenant_id: Optional[str] - New tenant ID
            title: Optional[str] - New title
            is_default: Optional[bool] - New default status
            
        Returns:
            bool: True if update was successful, False otherwise
        """
        try:
            update_values = {}
            if client_short_name is not None:
                update_values["client_short_name"] = client_short_name
            if profile_id is not None:
                update_values["profile_id"] = profile_id
            if tenant_id is not None:
                update_values["tenant_id"] = tenant_id
            if title is not None:
                update_values["title"] = title
            if is_default is not None:
                update_values["is_default"] = is_default
                
            if not update_values:
                return False
                
            query = update(ClientProfile).where(ClientProfile.id == client_profile_id).values(**update_values)
            result = await db.execute(query)
            await db.commit()
            logger.info(f"Updated client profile {client_profile_id} with {update_values}")
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating client profile {client_profile_id}: {e}")
            await db.rollback()
            return False

    @staticmethod
    async def get_all(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[ClientProfile]:
        """
        Get all client profiles with pagination
        
        Args:
            db: AsyncSession - Database session
            skip: int - Number of records to skip
            limit: int - Maximum number of records to return
            
        Returns:
            List[ClientProfile]: List of client profile records
        """
        try:
            query = select(ClientProfile).offset(skip).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting all client profiles: {e}")
            return [] 