from datetime import datetime
from typing import List
from loguru import logger
from models.customer_models import ProposalOutlineQueue, NotificationQueue, Users
from models.kontratar_models import OppsTable
from sqlalchemy import select, update, insert
from sqlalchemy.ext.asyncio import AsyncSession
import json

class ProposalOutlineQueueController:
    @staticmethod
    async def claim_new_queue_items(db: AsyncSession, limit: int = 6) -> List[ProposalOutlineQueue]:
        """
        Atomically claim up to `limit` NEW items and mark them as CLAIMED.
        Ensures multiple workers don’t process the same items.
        """
        try:
            cte = (
                select(ProposalOutlineQueue)
                .where(ProposalOutlineQueue.status == "NEW")
                .order_by(ProposalOutlineQueue.created_date.desc())
                .limit(limit)
                .with_for_update(skip_locked=True)
            ).cte("to_claim")

            update_stmt = (
                update(ProposalOutlineQueue)
                .where(ProposalOutlineQueue.id.in_(select(cte.c.id)))
                .values(status="CLAIMED", last_updated_date=datetime.utcnow())
                .returning(ProposalOutlineQueue)
            )

            result = await db.execute(update_stmt)
            await db.commit()

            claimed_items = result.scalars().all()
            logger.info(f"Claimed {len(claimed_items)} outline items for processing")
            return list(claimed_items)
        except Exception as e:
            logger.error(f"Error claiming Proposal Outline queue items: {e}")
            await db.rollback()
            return []
    
    @staticmethod
    async def complete_and_notify(db: AsyncSession, item_id: int):
        # 1. Mark the completed item as COMPLETED
        update_stmt = (
            update(ProposalOutlineQueue)
            .where(ProposalOutlineQueue.id == item_id)
            .values(status="COMPLETED", last_updated_date=datetime.utcnow())
        )
        await db.execute(update_stmt)
        await db.commit()

        # 2. Get the item to check first_request and opps_id
        item_query = select(ProposalOutlineQueue).where(ProposalOutlineQueue.id == item_id)
        item_result = await db.execute(item_query)
        item = item_result.scalar_one_or_none()
        if not item or not item.first_request:
            return  # Only proceed if first_request is True

        opps_id = item.opps_id

        # 3. Mark all queue items with this opps_id as COMPLETED
        update_all_stmt = (
            update(ProposalOutlineQueue)
            .where(ProposalOutlineQueue.opps_id == opps_id)
            .values(status="COMPLETED", last_updated_date=datetime.utcnow())
        )
        await db.execute(update_all_stmt)
        await db.commit()

        # 4. Get all tenant_ids for these items
        tenants_query = select(ProposalOutlineQueue.tenant_id).where(ProposalOutlineQueue.opps_id == opps_id)
        tenants_result = await db.execute(tenants_query)
        tenant_ids = set(row[0] for row in tenants_result.fetchall())

        # 5. Get the opportunity title
        opp_query = select(OppsTable.title).where(OppsTable.notice_id == opps_id)
        opp_result = await db.execute(opp_query)
        opp_title_row = opp_result.first()
        opp_title = opp_title_row[0] if opp_title_row else opps_id

        # 6. For each tenant, get admin users (group_id 0 or 1)
        for tenant_id in tenant_ids:
            users_query = select(Users.id).where(
                Users.tenant_id == tenant_id,
                Users.group_id.in_([0, 1])
            )
            users_result = await db.execute(users_query)
            user_ids = [row[0] for row in users_result.fetchall()]

            # 7. Create notification for each admin user
            for user_id in user_ids:
                notif = NotificationQueue(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    job_info=json.dumps({"link": f"sam/{opps_id}"}),
                    status="NEW",
                    title="Proposal Outline Ready",
                    message=f"Outline generation completed successfully for {opp_title}",
                    created_at=datetime.utcnow()
                )
                db.add(notif)
        await db.commit()
        
        
    @staticmethod
    async def update_queue_status(db: AsyncSession, item_id: int, status: str, error_message: str = None):
        """
        Update the status (and optionally error_message) of a ProposalOutlineQueue item.
        """
        try:
            update_fields = {
                "status": status,
                "last_updated_date": datetime.utcnow()
            }
            if error_message is not None:
                update_fields["error_message"] = error_message

            update_stmt = (
                update(ProposalOutlineQueue)
                .where(ProposalOutlineQueue.id == item_id)
                .values(**update_fields)
            )
            await db.execute(update_stmt)
            await db.commit()
            logger.info(f"Updated ProposalOutlineQueue item {item_id} to status '{status}'")
        except Exception as e:
            logger.error(f"Error updating ProposalOutlineQueue item {item_id} status: {e}")
            await db.rollback()