from datetime import datetime
from typing import List, Optional

from loguru import logger
from models.customer_models import NotificationQueue
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession


class NotificationQueueController:
    """Controller for notification queue operations"""
    
    @staticmethod
    async def get_new_items(db: AsyncSession, limit: int = 10) -> List[NotificationQueue]:
        """Get new notification queue items with status NEW"""
        try:
            query = select(NotificationQueue).where(
                NotificationQueue.status == "NEW"
            ).order_by(NotificationQueue.created_at.asc()).limit(limit)
            
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting new notification queue items: {e}")
            return []
    
    @staticmethod
    async def update_status(db: AsyncSession, queue_id: int, status: str) -> bool:
        """Update notification queue status"""
        try:
            query = update(NotificationQueue).where(
                NotificationQueue.id == queue_id
            ).values(
                status=status,
                sent_at=datetime.utcnow() if status == "SENT" else None
            )
            
            result = await db.execute(query)
            await db.commit()
            
            logger.info(f"Updated notification queue status for id {queue_id} to {status}")
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating notification queue status: {e}")
            await db.rollback()
            return False
    
    @staticmethod
    async def get_by_id(db: AsyncSession, queue_id: int) -> Optional[NotificationQueue]:
        """Get notification queue item by ID"""
        try:
            query = select(NotificationQueue).where(NotificationQueue.id == queue_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting notification queue item {queue_id}: {e}")
            return None
    
    @staticmethod
    async def get_by_user_id(db: AsyncSession, user_id: int, limit: int = 100) -> List[NotificationQueue]:
        """Get notification queue items by user ID"""
        try:
            query = select(NotificationQueue).where(
                NotificationQueue.user_id == user_id
            ).order_by(NotificationQueue.created_at.desc()).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting notification queue items for user {user_id}: {e}")
            return []
    
    @staticmethod
    async def get_by_tenant_id(db: AsyncSession, tenant_id: str, limit: int = 100) -> List[NotificationQueue]:
        """Get notification queue items by tenant ID"""
        try:
            query = select(NotificationQueue).where(
                NotificationQueue.tenant_id == tenant_id
            ).order_by(NotificationQueue.created_at.desc()).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting notification queue items for tenant {tenant_id}: {e}")
            return []
    
    @staticmethod
    async def get_all(db: AsyncSession, limit: int = 100) -> List[NotificationQueue]:
        """Get all notification queue items"""
        try:
            query = select(NotificationQueue).order_by(NotificationQueue.created_at.desc()).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting all notification queue items: {e}")
            return []
    
    @staticmethod
    async def add(
        db: AsyncSession,
        user_id: int,
        tenant_id: str,
        status: str,
        title: str = None,
        message: str = None,
        job_info: str = None
    ) -> Optional[NotificationQueue]:
        """Add new notification queue item"""
        try:
            new_item = NotificationQueue(
                user_id=user_id,
                tenant_id=tenant_id,
                job_info=job_info,
                status=status,
                title=title,
                message=message,
                created_at=datetime.utcnow()
            )
            
            db.add(new_item)
            await db.commit()
            await db.refresh(new_item)
            
            logger.info(f"Created new notification queue item {new_item.id}")
            return new_item
        except Exception as e:
            logger.error(f"Error creating notification queue item: {e}")
            await db.rollback()
            return None
    
    @staticmethod
    async def update(
        db: AsyncSession,
        queue_id: int,
        status: str = None,
        title: str = None,
        message: str = None,
        job_info: str = None
    ) -> bool:
        """Update notification queue item"""
        try:
            item = await NotificationQueueController.get_by_id(db, queue_id)
            if not item:
                return False
            
            if status:
                item.status = status
                if status == "SENT":
                    item.sent_at = datetime.utcnow()
            if title:
                item.title = title
            if message:
                item.message = message
            if job_info:
                item.job_info = job_info
            
            await db.commit()
            logger.info(f"Updated notification queue item {queue_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating notification queue item: {e}")
            await db.rollback()
            return False 