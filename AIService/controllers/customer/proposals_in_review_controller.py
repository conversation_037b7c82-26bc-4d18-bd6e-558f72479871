from datetime import datetime
from typing import List, Optional

from loguru import logger
from models.customer_models import ProposalsInReview
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func



class ProposalsInReviewController:
    """Controller for proposals in review operations"""
    
    @staticmethod
    async def get_by_id(db: AsyncSession, review_id: int) -> Optional[ProposalsInReview]:
        """Get proposal in review by ID"""
        try:
            query = select(ProposalsInReview).where(ProposalsInReview.id == review_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting proposal in review {review_id}: {e}")
            return None
    
    @staticmethod
    async def get_by_opps_id(db: AsyncSession, opps_id: str, limit: int = 100) -> List[ProposalsInReview]:
        """Get proposals in review by opportunity ID"""
        try:
            query = select(ProposalsInReview).where(
                ProposalsInReview.opps_id == opps_id
            ).order_by(ProposalsInReview.created_date.desc()).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting proposals in review for opps {opps_id}: {e}")
            return []
    
    @staticmethod
    async def get_by_tenant_id(db: AsyncSession, tenant_id: str, limit: int = 100) -> List[ProposalsInReview]:
        """Get proposals in review by tenant ID"""
        try:
            query = select(ProposalsInReview).where(
                ProposalsInReview.tenant_id == tenant_id
            ).order_by(ProposalsInReview.created_date.desc()).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting proposals in review for tenant {tenant_id}: {e}")
            return []
    
    @staticmethod
    async def get_current_version(db: AsyncSession, tenant_id: str, opps_id: str) -> Optional[int]:
        """
        Get the maximum version number for a proposal in review for a given tenant_id and opps_id.
        """
        try:
            query = select(func.max(ProposalsInReview.version)).where(
                ProposalsInReview.tenant_id == tenant_id,
                ProposalsInReview.opps_id == opps_id
            )
            result = await db.execute(query)
            max_version = result.scalar_one()
            return max_version if max_version else 0
        except Exception as e:
            logger.error(f"Error getting max version for tenant {tenant_id} and opps {opps_id}: {e}")
            return None
    
    @staticmethod
    async def get_by_status(db: AsyncSession, status: str, limit: int = 100) -> List[ProposalsInReview]:
        """Get proposals in review by status"""
        try:
            query = select(ProposalsInReview).where(
                ProposalsInReview.status == status
            ).order_by(ProposalsInReview.created_date.desc()).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting proposals in review with status {status}: {e}")
            return []
    
    @staticmethod
    async def get_all(db: AsyncSession, limit: int = 100) -> List[ProposalsInReview]:
        """Get all proposals in review"""
        try:
            query = select(ProposalsInReview).order_by(ProposalsInReview.created_date.desc()).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting all proposals in review: {e}")
            return []
    
    @staticmethod
    async def add(
        db: AsyncSession,
        client_short_name: str,
        tenant_id: str,
        section_number: str,
        opps_id: str,
        volume_number: int,
        version: int,
        opps_type: str,
        proposal_data: bytes,
        job_instruction: Optional[str] = None
    ) -> Optional[ProposalsInReview]:
        """Add new proposal in review"""
        try:
            new_review = ProposalsInReview(
                client_short_name=client_short_name,
                tenant_id=tenant_id,
                section_number=section_number,
                opps_id=opps_id,
                created_date=datetime.utcnow(),
                last_mod_date=datetime.utcnow(),
                proposal_data=proposal_data,
                version=version,
                due_date=None,
                status="CREATED",
                opps_type=opps_type.upper(),
                finalized=None,
                volume_number=volume_number,
                job_instruction=job_instruction
            )
            
            db.add(new_review)
            await db.commit()
            await db.refresh(new_review)
            
            logger.info(f"Created new proposal in review {new_review.id}")
            return new_review
        except Exception as e:
            logger.error(f"Error creating proposal in review: {e}")
            await db.rollback()
            return None