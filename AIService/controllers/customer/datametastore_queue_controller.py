from datetime import datetime
from typing import List, Optional

from loguru import logger
from models.customer_models import DataMetastoreQueue
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession


class DataMetastoreQueueController:
    """Controller for datametastore queue operations"""
    
    @staticmethod
    async def get_new_items(db: AsyncSession, limit: int = 10) -> List[DataMetastoreQueue]:
        """Get new datametastore queue items with status NOT STARTED"""
        try:
            query = select(DataMetastoreQueue).where(
                DataMetastoreQueue.status == "NOT STARTED"
            ).order_by(DataMetastoreQueue.creation_date.asc()).limit(limit)
            
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting new datametastore queue items: {e}")
            return []
    
    @staticmethod
    async def update_status(db: AsyncSession, job_id: str, status: str) -> bool:
        """Update datametastore queue status"""
        try:
            query = update(DataMetastoreQueue).where(
                DataMetastoreQueue.job_id == job_id
            ).values(
                status=status,
                last_updated_date=datetime.utcnow()
            )
            
            result = await db.execute(query)
            await db.commit()
            
            logger.info(f"Updated datametastore queue status for job_id {job_id} to {status}")
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating datametastore queue status: {e}")
            await db.rollback()
            return False
    
    @staticmethod
    async def get_by_id(db: AsyncSession, queue_id: int) -> Optional[DataMetastoreQueue]:
        """Get datametastore queue item by ID"""
        try:
            query = select(DataMetastoreQueue).where(DataMetastoreQueue.id == queue_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting datametastore queue item {queue_id}: {e}")
            return None
    
    @staticmethod
    async def get_by_job_id(db: AsyncSession, job_id: str) -> Optional[DataMetastoreQueue]:
        """Get datametastore queue item by job ID"""
        try:
            query = select(DataMetastoreQueue).where(DataMetastoreQueue.job_id == job_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting datametastore queue item by job_id {job_id}: {e}")
            return None
    
    @staticmethod
    async def get_by_tenant_id(db: AsyncSession, tenant_id: str, limit: int = 100) -> List[DataMetastoreQueue]:
        """Get datametastore queue items by tenant ID"""
        try:
            query = select(DataMetastoreQueue).where(
                DataMetastoreQueue.tenant_id == tenant_id
            ).order_by(DataMetastoreQueue.creation_date.desc()).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting datametastore queue items for tenant {tenant_id}: {e}")
            return []
    
    @staticmethod
    async def get_by_job_submitted_by(db: AsyncSession, job_submitted_by: str, limit: int = 100) -> List[DataMetastoreQueue]:
        """Get datametastore queue items by job submitted by"""
        try:
            query = select(DataMetastoreQueue).where(
                DataMetastoreQueue.job_submitted_by == job_submitted_by
            ).order_by(DataMetastoreQueue.creation_date.desc()).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting datametastore queue items for user {job_submitted_by}: {e}")
            return []
    
    @staticmethod
    async def get_by_status(db: AsyncSession, status: str, limit: int = 100) -> List[DataMetastoreQueue]:
        """Get datametastore queue items by status"""
        try:
            query = select(DataMetastoreQueue).where(
                DataMetastoreQueue.status == status
            ).order_by(DataMetastoreQueue.creation_date.desc()).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting datametastore queue items with status {status}: {e}")
            return []
    
    @staticmethod
    async def get_by_source(db: AsyncSession, source: str, limit: int = 100) -> List[DataMetastoreQueue]:
        """Get datametastore queue items by source"""
        try:
            query = select(DataMetastoreQueue).where(
                DataMetastoreQueue.source == source
            ).order_by(DataMetastoreQueue.creation_date.desc()).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting datametastore queue items with source {source}: {e}")
            return []
    
    @staticmethod
    async def get_all(db: AsyncSession, limit: int = 100) -> List[DataMetastoreQueue]:
        """Get all datametastore queue items"""
        try:
            query = select(DataMetastoreQueue).order_by(DataMetastoreQueue.creation_date.desc()).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting all datametastore queue items: {e}")
            return []
    
    @staticmethod
    async def add(
        db: AsyncSession,
        job_id: str,
        job_instruction: str,
        status: str,
        tenant_id: Optional[str] = None,
        job_submitted_by: Optional[str] = None,
        original_document_file_name: Optional[str] = None,
        source: str = "LOCAL"
    ) -> Optional[DataMetastoreQueue]:
        """Add new datametastore queue item"""
        try:
            new_item = DataMetastoreQueue(
                job_id=job_id,
                job_instruction=job_instruction,
                creation_date=datetime.utcnow(),
                job_submitted_by=job_submitted_by,
                status=status,
                tenant_id=tenant_id,
                last_updated_date=datetime.utcnow(),
                original_document_file_name=original_document_file_name,
                source=source
            )
            
            db.add(new_item)
            await db.commit()
            await db.refresh(new_item)
            
            logger.info(f"Created new datametastore queue item {new_item.id}")
            return new_item
        except Exception as e:
            logger.error(f"Error creating datametastore queue item: {e}")
            await db.rollback()
            return None
    
    @staticmethod
    async def update(
        db: AsyncSession,
        queue_id: int,
        status: Optional[str] = None,
        job_instruction: Optional[str] = None,
        job_submitted_by: Optional[str] = None,
        original_document_file_name: Optional[str] = None,
        source: Optional[str] = None
    ) -> bool:
        """Update datametastore queue item"""
        try:
            item = await DataMetastoreQueueController.get_by_id(db, queue_id)
            if not item:
                return False

            update_fields = {
                "status": status,
                "job_instruction": job_instruction,
                "job_submitted_by": job_submitted_by,
                "original_document_file_name": original_document_file_name,
                "source": source,
            }
            for field, value in update_fields.items():
                if value is not None:
                    setattr(item, field, value)

            setattr(item, "last_updated_date", datetime.utcnow())

            await db.commit()
            logger.info(f"Updated datametastore queue item {queue_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating datametastore queue item: {e}")
            await db.rollback()
            return False 