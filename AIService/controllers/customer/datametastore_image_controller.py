from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from models.customer_models import DatametastoreImages
from loguru import logger
from datetime import datetime

class DatametastoreImagesController:
    """Controller for datametastore_images operations"""
    
    @staticmethod
    async def add(
        db: AsyncSession,
        document_id: str,
        raw_image: bytes,
        description: Optional[str] = None,
        tenant_id: Optional[str] = None,
        record_type: Optional[str] = None,
        last_processed_date: Optional[datetime] = None
    ) -> Optional[DatametastoreImages]:
        """
        Add a new datametastore image record
        
        Args:
            db: AsyncSession - Database session
            document_id: str - Unique identifier for the document
            raw_image: bytes - Raw image data 
            description: Optional[str] - Description of the image
            tenant_id: Optional[str] - Tenant ID associated with the image
            record_type: Optional[str] - Type of record
            last_processed_date: Optional[datetime] - Date of last processing
            
        Returns:
            Optional[DatametastoreImages]: Created image record or None if failed
        """
        try:
            new_image = DatametastoreImages(
                document_id=document_id,
                raw_image=raw_image,
                description=description,
                tenant_id=tenant_id,
                record_type=record_type,
                last_processed_date=last_processed_date
            )
            
            db.add(new_image)
            await db.commit()
            await db.refresh(new_image)
            
            logger.info(f"Added new datametastore image with document_id: {document_id}")
            return new_image
        
        except Exception as e:
            logger.error(f"Error adding datametastore image: {e}")
            await db.rollback()
            return None
    
    @staticmethod
    async def get_by_document_id(
        db: AsyncSession, 
        document_id: str
    ) -> List[DatametastoreImages]:
        """
        Retrieve images by document ID
        
        Args:
            db: AsyncSession - Database session
            document_id: str - Document identifier to search for
            
        Returns:
            List[DatametastoreImages]: List of matching image records
        """
        try:
            query = select(DatametastoreImages).where(
                DatametastoreImages.document_id == document_id
            )
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error retrieving images for document_id {document_id}: {e}")
            return []
