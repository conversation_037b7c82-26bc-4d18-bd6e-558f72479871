from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from models.customer_models import CustomOpportunityTableInfo
from loguru import logger

class CustomOpportunityTableInfoController:
    @staticmethod
    async def get_opportunity_document(opps_id: str, tenant_id: str, db: AsyncSession) -> Optional[CustomOpportunityTableInfo]:
        """Get a CustomOpportunityTableInfo record by opps_id and tenant_id using an isolated transaction."""
        try:
            query = select(CustomOpportunityTableInfo).where(
                CustomOpportunityTableInfo.opps_id == opps_id,
                CustomOpportunityTableInfo.tenant_id == tenant_id
            )
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting opportunity document for opps_id={opps_id}, tenant_id={tenant_id}: {e}")
            return None 
