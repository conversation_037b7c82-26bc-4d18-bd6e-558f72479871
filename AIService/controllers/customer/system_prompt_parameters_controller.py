from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from models.customer_models import SystemPromptParameters
from loguru import logger

class SystemPromptParametersController:
    """Controller for SystemPromptParameters operations"""

    @staticmethod
    async def get_by_id(db: AsyncSession, record_id: int) -> Optional[SystemPromptParameters]:
        """Get SystemPromptParameters record by ID"""
        try:
            query = select(SystemPromptParameters).where(SystemPromptParameters.id == record_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting SystemPromptParameters record {record_id}: {e}")
            return None 