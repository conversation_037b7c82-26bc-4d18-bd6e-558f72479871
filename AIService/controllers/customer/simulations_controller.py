from datetime import datetime
from typing import List, Optional

from loguru import logger
from models.customer_models import Simulations
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession



class SimulationsController:
    """Controller for simulations operations"""
    
    @staticmethod
    async def get_by_id(db: AsyncSession, simulation_id: int) -> Optional[Simulations]:
        """Get simulation by ID"""
        try:
            query = select(Simulations).where(Simulations.id == simulation_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting simulation {simulation_id}: {e}")
            return None
    
    @staticmethod
    async def get_by_job_id(db: AsyncSession, job_id: str) -> Optional[Simulations]:
        """Get simulation by job ID"""
        try:
            query = select(Simulations).where(Simulations.job_id == job_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting simulation by job_id {job_id}: {e}")
            return None
    
    @staticmethod
    async def get_by_tenant_id(db: AsyncSession, tenant_id: str, limit: int = 100) -> List[Simulations]:
        """Get simulations by tenant ID"""
        try:
            query = select(Simulations).where(
                Simulations.tenant_id == tenant_id
            ).order_by(Simulations.created_at.desc()).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting simulations for tenant {tenant_id}: {e}")
            return []
    
    @staticmethod
    async def get_all(db: AsyncSession, limit: int = 100) -> List[Simulations]:
        """Get all simulations"""
        try:
            query = select(Simulations).order_by(Simulations.created_at.desc()).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting all simulations: {e}")
            return []
    
    @staticmethod
    async def add(
        db: AsyncSession,
        tenant_id: str,
        job_id: str,
        title: str = None,
        simulation_result: str = None,
        simulation_summary: str = None
    ) -> Optional[Simulations]:
        """Add new simulation"""
        try:
            new_simulation = Simulations(
                title=title,
                created_at=datetime.utcnow(),
                tenant_id=tenant_id,
                job_id=job_id,
                simulation_result=simulation_result,
                simulation_summary=simulation_summary
            )
            
            db.add(new_simulation)
            await db.commit()
            await db.refresh(new_simulation)
            
            logger.info(f"Created new simulation {new_simulation.id}")
            return new_simulation
        except Exception as e:
            logger.error(f"Error creating simulation: {e}")
            await db.rollback()
            return None
    
    @staticmethod
    async def update(
        db: AsyncSession,
        simulation_id: int,
        title: str = None,
        simulation_result: str = None,
        simulation_summary: str = None
    ) -> bool:
        """Update simulation"""
        try:
            simulation = await SimulationsController.get_by_id(db, simulation_id)
            if not simulation:
                return False
            
            if title:
                simulation.title = title
            if simulation_result:
                simulation.simulation_result = simulation_result
            if simulation_summary:
                simulation.simulation_summary = simulation_summary
            
            await db.commit()
            logger.info(f"Updated simulation {simulation_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating simulation: {e}")
            await db.rollback()
            return False 