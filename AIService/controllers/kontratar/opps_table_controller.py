from typing import Optional, Dict, Any
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from models.kontratar_models import OppsTable

class OppsTableController:
    """Controller for OppsTable operations"""
    @staticmethod
    async def get_all_table_of_contents(
        db: AsyncSession
    ) -> list:
        """
        Get all table_of_contents values from OppsTable, ordered by id.
        """
        try:
            query = select(OppsTable.table_of_contents).order_by(OppsTable.id)
            result = await db.execute(query)
            rows = result.scalars().all()
            # Ensure None for empty values
            return [row if row is not None else None for row in rows]
        except Exception as e:
            logger.error(f"Error fetching all table_of_contents from OppsTable: {e}")
            return []

    @staticmethod
    async def get_all_proposal_outlines(
        db: AsyncSession
    ) -> list:
        """
        Get all proposal_outline values from OppsTable, ordered by id.
        """
        try:
            query = select(OppsTable.proposal_outline).order_by(OppsTable.id)
            result = await db.execute(query)
            rows = result.scalars().all()
            return [row if row is not None else None for row in rows]
        except Exception as e:
            logger.error(f"Error fetching all proposal_outline from OppsTable: {e}")
            return []

    @staticmethod
    async def get_by_notice_id(
        db: AsyncSession,
        notice_id: str
    ) -> Optional[Any]:
        """
        Get an OppsTable record by notice_id.
        Returns the record or None if not found.
        """
        try:
            query = select(
                OppsTable.title,
                OppsTable.description,
                OppsTable.posted_date,
                OppsTable.archive_date,
                OppsTable.naics_code,
                OppsTable.naics_codes,
                OppsTable.type_of_set_aside,
                OppsTable.type_of_set_aside_description,
                OppsTable.notice_id,
                OppsTable.solicitation_number,
                OppsTable.full_parent_path_name,
                OppsTable.full_parent_path_code,
                OppsTable.type_op,
                OppsTable.base_type_op,
                OppsTable.archive_type,
                OppsTable.classification_code,
                OppsTable.point_of_contact_name,
                OppsTable.point_of_contact_email,
                OppsTable.point_of_contact_phone,
                OppsTable.place_of_performance_city_name,
                OppsTable.place_of_performance_state_name,
                OppsTable.place_of_performance_zip,
                OppsTable.place_of_performance_country_name
            ).where(OppsTable.notice_id == notice_id)
            result = await db.execute(query)
            return result.mappings().one_or_none()
        except Exception as e:
            logger.error(f"Error fetching OppsTable record by notice_id={notice_id}: {e}")
            return None

    @staticmethod
    async def get_keywords_by_notice_id(
        db: AsyncSession,
        notice_id: str
    ) -> Optional[str]:
        """
        Get the keywords column for an OppsTable record by notice_id.
        Returns the keywords string or None if not found.
        """
        try:
            query = select(OppsTable.keywords).where(OppsTable.notice_id == notice_id)
            result = await db.execute(query)
            row = result.first()
            return row[0] if row else None
        except Exception as e:
            logger.error(f"Error fetching keywords for notice_id={notice_id}: {e}")
            return None

    @staticmethod
    async def update_by_notice_id(
        db: AsyncSession,
        notice_id: str,
        update_fields: Dict[str, Any]
    ) -> Optional[OppsTable]:
        """
        Update fields of an OppsTable record by notice_id.
        Only allows updating valid fields (not id).
        Returns the updated record or None if not found.
        """
        valid_columns = {col.name for col in OppsTable.__table__.columns}
        safe_fields = {k: v for k, v in update_fields.items() if k in valid_columns and k != "id"}
        if not safe_fields:
            logger.warning("No valid fields to update for OppsTable.")
            return None
        try:
            query = select(OppsTable).where(OppsTable.notice_id == notice_id)
            result = await db.execute(query)
            record = result.scalar_one_or_none()
            if not record:
                logger.warning(f"No OppsTable record found for notice_id={notice_id}")
                return None
            for field, value in safe_fields.items():
                setattr(record, field, value)
            await db.commit()
            await db.refresh(record)
            logger.info(f"Updated OppsTable record with notice_id={notice_id}")
            return record
        except Exception as e:
            logger.error(f"Error updating OppsTable record: {e}")
            await db.rollback()
            return None 