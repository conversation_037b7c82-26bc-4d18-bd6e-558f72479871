from typing import Any, Dict, Optional
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from models.kontratar_models import OppsTable

class OppsController:
    """Controller for OppsTable operations"""

    @staticmethod
    async def update_by_opportunity_id(
        db: AsyncSession,
        opportunity_id: str,
        update_fields: Dict[str, Any]
    ) -> Optional[OppsTable]:
        """
        Update fields of an OppsTable record by opportunity_id.
        Only allows updating valid fields.
        Returns the updated record or None if not found.
        """
        valid_columns = {col.name for col in OppsTable.__table__.columns}
        safe_fields = {k: v for k, v in update_fields.items() if k in valid_columns and k != "id"}

        if not safe_fields:
            logger.warning("No valid fields to update for OppsTable.")
            return None

        try:
            query = select(OppsTable).where(OppsTable.notice_id == opportunity_id)
            result = await db.execute(query)
            record = result.scalar_one_or_none()
            if not record:
                logger.warning(f"No OppsTable record found for opportunity_id={opportunity_id}")
                return None

            for field, value in safe_fields.items():
                setattr(record, field, value)

            await db.commit()
            await db.refresh(record)
            logger.info(f"Updated OppsTable record with opportunity_id={opportunity_id}")
            return record
        except Exception as e:
            logger.error(f"Error updating OppsTable record: {e}")
            await db.rollback()
            return None