from typing import Optional, Any
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from models.kontratar_models import EBUYOppsTable

class EBUYOppsController:
    """Controller for EBUYOppsTable operations"""
    @staticmethod
    async def get_all_table_of_contents(
        db: AsyncSession
    ) -> list:
        """
        Get all table_of_contents values from EBUYOppsTable, ordered by id.
        """
        try:
            query = select(EBUYOppsTable.table_of_contents).order_by(EBUYOppsTable.id)
            result = await db.execute(query)
            rows = result.scalars().all()
            return [row if row is not None else None for row in rows]
        except Exception as e:
            logger.error(f"Error fetching all table_of_contents from EBUYOppsTable: {e}")
            return []

    @staticmethod
    async def get_all_proposal_outlines(
        db: AsyncSession
    ) -> list:
        """
        Get all proposal_outline values from EBUYOppsTable, ordered by id.
        """
        try:
            query = select(EBUYOppsTable.proposal_outline).order_by(EBUYOppsTable.id)
            result = await db.execute(query)
            rows = result.scalars().all()
            return [row if row is not None else None for row in rows]
        except Exception as e:
            logger.error(f"Error fetching all proposal_outline from EBUYOppsTable: {e}")
            return []

    @staticmethod
    async def get_by_rfq_id(
        db: AsyncSession,
        rfq_id: str
    ) -> Optional[Any]:
        """
        Get an EBUYOppsTable record by rfq_id, returning only selected columns.
        """
        try:
            query = select(
                EBUYOppsTable.rfq_id,
                EBUYOppsTable.title,
                EBUYOppsTable.description,
                EBUYOppsTable.reference_id,
                EBUYOppsTable.issue_date,
                EBUYOppsTable.close_date,
                EBUYOppsTable.delivery_info,
                EBUYOppsTable.contact_info,
                EBUYOppsTable.shipping_address,
                EBUYOppsTable.description_text,
                EBUYOppsTable.posted_date,
                EBUYOppsTable.naics_code,
                EBUYOppsTable.created_date,
                EBUYOppsTable.buyer
            ).where(EBUYOppsTable.rfq_id == rfq_id)
            result = await db.execute(query)
            return result.mappings().one_or_none()
        except Exception as e:
            logger.error(f"Error fetching EBUYOppsTable record by rfq_id={rfq_id}: {e}")
            return None

    @staticmethod
    async def get_keywords_by_rfq_id(
        db: AsyncSession,
        rfq_id: str
    ) -> Optional[str]:
        """
        Get the keywords column for an EBUYOppsTable record by rfq_id.
        Returns the keywords string or None if not found.
        """
        try:
            query = select(EBUYOppsTable.keywords).where(EBUYOppsTable.rfq_id == rfq_id)
            result = await db.execute(query)
            row = result.first()
            return row[0] if row else None
        except Exception as e:
            logger.error(f"Error fetching keywords for rfq_id={rfq_id}: {e}")
            return None

    @staticmethod
    async def update_by_rfq_id(
        db: AsyncSession,
        rfq_id: str,
        update_fields: dict
    ) -> Optional[EBUYOppsTable]:
        """
        Update fields of an EBUYOppsTable record by rfq_id.
        Only allows updating valid fields (not id).
        Returns the updated record or None if not found.
        """
        valid_columns = {col.name for col in EBUYOppsTable.__table__.columns}
        safe_fields = {k: v for k, v in update_fields.items() if k in valid_columns and k != "id"}
        if not safe_fields:
            logger.warning("No valid fields to update for EBUYOppsTable.")
            return None
        try:
            query = select(EBUYOppsTable).where(EBUYOppsTable.rfq_id == rfq_id)
            result = await db.execute(query)
            record = result.scalar_one_or_none()
            if not record:
                logger.warning(f"No EBUYOppsTable record found for rfq_id={rfq_id}")
                return None
            for field, value in safe_fields.items():
                setattr(record, field, value)
            await db.commit()
            await db.refresh(record)
            logger.info(f"Updated EBUYOppsTable record with rfq_id={rfq_id}")
            return record
        except Exception as e:
            logger.error(f"Error updating EBUYOppsTable record: {e}")
            await db.rollback()
            return None 