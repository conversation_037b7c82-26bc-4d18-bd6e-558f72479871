from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from models.kontratar_models import GSAContractors, GSAContractDetails
from loguru import logger

class GSAContractorsController:
    @staticmethod
    async def get_by_id(gsa_contractor_id: int, db: AsyncSession) -> Optional[GSAContractors]:
        """Get a single GSA Contractor record by ID."""
        try:
            query = select(GSAContractors).where(GSAContractors.id == gsa_contractor_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting GSA Contractor for id={gsa_contractor_id}: {e}")
            return None

    @staticmethod
    async def get_all(db: AsyncSession, limit: int = 20, offset: int = 0) -> List[GSAContractors]:
        """Get all GSA Contractors with optional pagination."""
        try:
            query = select(GSAContractors).offset(offset).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting all GSA Contractors: {e}")
            return []
       
    @staticmethod
    async def search(
        db: AsyncSession, 
        email: Optional[str] = None, 
        naics: Optional[str] = None, 
        govt_poc_name: Optional[str] = None, 
        govt_poc_email: Optional[str] = None, 
        website: Optional[str] = None,
        socio_economic: Optional[str] = None,
        limit: int = 20, 
        offset: int = 0
    ) -> List[GSAContractors]:
        """
        Search GSA Contractors with multiple optional filters.
        
        Args:
            db: AsyncSession for database connection
            email: Contractor's email to filter by
            naics: NAICS code to filter by
            govt_poc_name: Government Point of Contact name to filter by
            govt_poc_email: Government Point of Contact email to filter by
            website: Contractor's website to filter by
            socio_economic: Socio-economic category to filter by
            limit: Maximum number of results to return
            offset: Number of results to skip for pagination
        
        Returns:
            List of GSAContractors matching the search criteria
        """
        try:
            query = select(GSAContractors)
            
            # Apply filters conditionally
            if email:
                query = query.where(GSAContractors.email.ilike(f"%{email}%"))
            
            if naics:
                query = query.where(GSAContractors.naics.ilike(f"%{naics}%"))
            
            if govt_poc_name:
                query = query.where(GSAContractors.govt_poc_name.ilike(f"%{govt_poc_name}%"))
            
            if govt_poc_email:
                query = query.where(GSAContractors.govt_poc_email.ilike(f"%{govt_poc_email}%"))
            
            if website:
                query = query.where(GSAContractors.website.ilike(f"%{website}%"))
            
            if socio_economic:
                query = query.where(GSAContractors.socio_economic.ilike(f"%{socio_economic}%"))
            
            # Apply pagination
            query = query.offset(offset).limit(limit)
            
            result = await db.execute(query)
            return result.scalars().all()
        
        except Exception as e:
            logger.error(f"Error searching GSA Contractors: {e}")
            return []

