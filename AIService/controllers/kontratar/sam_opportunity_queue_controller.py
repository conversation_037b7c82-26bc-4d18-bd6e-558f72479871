from datetime import datetime
from typing import List, Optional

from loguru import logger
from models.kontratar_models import SamOpportunityQueue
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

class SamOpportunityQueueController:
    @staticmethod
    async def get_new_queue_items(db: AsyncSession, limit: int = 10) -> List[SamOpportunityQueue]:
        """Get new SAM opportunity queue items (status = 'NEW')."""
        try:
            query = select(SamOpportunityQueue).where(
                SamOpportunityQueue.status == "NEW"
            ).order_by(SamOpportunityQueue.created_date.desc()).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting new SAM opportunity queue items: {e}")
            return []

    @staticmethod
    async def update_queue_status(db: AsyncSession, opps_id: str, status: str) -> bool:
        """Update SAM opportunity queue status by opps_id."""
        try:
            query = update(SamOpportunityQueue).where(
                SamOpportunityQueue.opps_id == opps_id
            ).values(
                status=status,
                updated_date=datetime.utcnow()
            )
            result = await db.execute(query)
            await db.commit()
            logger.info(f"Updated SAM opportunity queue status for opps_id {opps_id} to {status}")
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating SAM opportunity queue status: {e}")
            await db.rollback()
            return False 

    
    @staticmethod
    async def update_queue_status_batch(
        db: AsyncSession, 
        opps_ids: List[str], 
        status: str
    ) -> bool:
        """Update SAM opportunity queue status for multiple opps_ids in batch."""
        try:
            query = update(SamOpportunityQueue).where(
                SamOpportunityQueue.opps_id.in_(opps_ids)
            ).values(
                status=status,
                updated_date=datetime.utcnow()
            )
            result = await db.execute(query)
            await db.commit()
            logger.info(f"Updated SAM opportunity queue status for {len(opps_ids)} opps_ids to {status}")
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating SAM opportunity queue status in batch: {e}")
            await db.rollback()
            return False
        
        
    @staticmethod
    async def claim_new_queue_items(db: AsyncSession, limit: int = 6) -> List[SamOpportunityQueue]:
        """
        Atomically claim up to `limit` NEW items and mark them as PROCESSING.
        Ensures multiple workers don’t process the same items.
        """
        try:
            # Build a CTE (Common Table Expression) to select and lock the rows
            cte = (
                select(SamOpportunityQueue)
                .where(SamOpportunityQueue.status == "NEW")
                .order_by(SamOpportunityQueue.created_date.desc())
                .limit(limit)
                .with_for_update(skip_locked=True)  # Prevent race conditions
            ).cte("to_claim")

            # Perform the update and return updated rows
            update_stmt = (
                update(SamOpportunityQueue)
                .where(SamOpportunityQueue.opps_id.in_(select(cte.c.opps_id)))
                .values(status="CLAIMED", updated_date=datetime.utcnow())
                .returning(SamOpportunityQueue)
            )

            result = await db.execute(update_stmt)
            await db.commit()

            claimed_items = result.scalars().all()
            logger.info(f"Claimed {len(claimed_items)} items for processing")
            return list(claimed_items)

        except Exception as e:
            logger.error(f"Error claiming SAM opportunity queue items: {e}")
            await db.rollback()
            return []