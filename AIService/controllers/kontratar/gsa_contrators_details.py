from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from models.kontratar_models import GSAContractors, GSAContractDetails
from loguru import logger

class GSAContractDetailsController:
    @staticmethod
    async def get_by_id(gsa_contract_detail_id: int, db: AsyncSession) -> Optional[GSAContractDetails]:
        """Get a single GSA Contract Details record by ID."""
        try:
            query = select(GSAContractDetails).where(GSAContractDetails.id == gsa_contract_detail_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting GSA Contract Details for id={gsa_contract_detail_id}: {e}")
            return None

    @staticmethod
    async def get_by_contractor_id(contractor_id: int, db: AsyncSession) -> List[GSAContractDetails]:
        """Get all GSA Contract Details for a specific contractor."""
        try:
            query = select(GSAContractDetails).where(GSAContractDetails.contractor_id == contractor_id)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting GSA Contract Details for contractor_id={contractor_id}: {e}")
            return []

    @staticmethod
    async def get_all(db: AsyncSession, limit: int = 20, offset: int = 0) -> List[GSAContractDetails]:
        """Get all GSA Contract Details with optional pagination."""
        try:
            query = select(GSAContractDetails).offset(offset).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting all GSA Contract Details: {e}")
            return []

