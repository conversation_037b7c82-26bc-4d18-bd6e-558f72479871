from datetime import datetime
from typing import List, Optional

from loguru import logger
from models.kontratar_models import ChromaDBInstanceMapping
from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type

class ChromaDBMappingController:
    """Controller for ChromaDB instance mapping operations"""
    
    
    @staticmethod
    @retry(
        stop=stop_after_attempt(3),  # Retry up to 3 times
        wait=wait_fixed(1),          # Wait 1 second between retries
        retry=retry_if_exception_type(Exception),  # Retry on any Exception
        reraise=True                 # Reraise the last exception if all retries fail
    )
    async def get_by_unique_id_and_tenant(
        db: AsyncSession, 
        unique_id: str, 
        tenant_id: str
    ) -> Optional[ChromaDBInstanceMapping]:
        """Get ChromaDB instance mapping by unique ID and tenant ID combination"""

        # TODO: Add caching to this using Redis
        try:
            query = select(ChromaDBInstanceMapping).where(
                and_(
                    ChromaDBInstanceMapping.unique_id == unique_id,
                    ChromaDBInstanceMapping.tenant_id == tenant_id
                )
            )
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting ChromaDB instance mapping for unique_id {unique_id} and tenant_id {tenant_id}: {e}")
            return None

    @staticmethod
    @retry(
        stop=stop_after_attempt(3),  # Retry up to 3 times
        wait=wait_fixed(1),          # Wait 1 second between retries
        retry=retry_if_exception_type(Exception),  # Retry on any Exception
        reraise=True                 # Reraise the last exception if all retries fail
    )
    async def get_by_unique_id(
        db: AsyncSession, 
        unique_id: str
    ) -> Optional[ChromaDBInstanceMapping]:
        """Get ChromaDB instance mapping by unique ID and tenant ID combination"""

        # TODO: Add caching to this using Redis
        try:
            query = select(ChromaDBInstanceMapping).where(
                ChromaDBInstanceMapping.unique_id == unique_id
            )
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting ChromaDB instance mapping for unique_id {unique_id}: {e}")
            return None
    
    @staticmethod
    @retry(
        stop=stop_after_attempt(3),  # Retry up to 3 times
        wait=wait_fixed(1),          # Wait 1 second between retries
        retry=retry_if_exception_type(Exception),  # Retry on any Exception
        reraise=True                 # Reraise the last exception if all retries fail
    )
    
    async def get_chromadb_url(
        db: AsyncSession, 
        unique_id: str, 
        tenant_id: str
    ) -> Optional[str]:
        """Get ChromaDB URL for a combination of unique ID and tenant ID"""
        try:
            mapping = await ChromaDBMappingController.get_by_unique_id_and_tenant(db, unique_id, tenant_id)
            if not mapping:
                return None

            # Update last accessed date
            setattr(mapping, "last_accessed_date", datetime.utcnow())
            await db.commit()

            if mapping.chroma_instance_url is None:
                return None

            return str(mapping.chroma_instance_url)
        except Exception as e:
            logger.error(f"Error getting ChromaDB URL for unique_id {unique_id} and tenant_id {tenant_id}: {e}")
            return None
    
    
    
    
    
    @staticmethod
    async def add(
        db: AsyncSession,
        unique_id: str,
        tenant_id: str,
        chroma_instance_url: str,
        collection_name: str,
        status: str = "ACTIVE"
    ) -> Optional[ChromaDBInstanceMapping]:
        """Add new ChromaDB instance mapping record"""
        try:
            new_mapping = ChromaDBInstanceMapping(
                unique_id=unique_id,
                tenant_id=tenant_id,
                chroma_instance_url=chroma_instance_url,
                collection_name=collection_name,
                created_date=datetime.utcnow(),
                status=status
            )
            
            db.add(new_mapping)
            await db.commit()
            await db.refresh(new_mapping)
            
            logger.info(f"Created new ChromaDB instance mapping record {new_mapping.id}")
            return new_mapping
        except Exception as e:
            logger.error(f"Error creating ChromaDB instance mapping record: {e}")
            await db.rollback()
            return None
    
    @staticmethod
    async def update_by_unique_id_and_tenant(
        db: AsyncSession,
        unique_id: str,
        tenant_id: str,
        chroma_instance_url: Optional[str] = None,
        collection_name: Optional[str] = None,
        version: Optional[int] = None,
        status: Optional[str] = None
    ) -> bool:
        """Update ChromaDB instance mapping record by unique ID and tenant ID"""
        try:
            record = await ChromaDBMappingController.get_by_unique_id_and_tenant(db, unique_id, tenant_id)
            if not record:
                return False

            if chroma_instance_url:
                setattr(record, "chroma_instance_url", chroma_instance_url)
            if collection_name:
                setattr(record, "collection_name", collection_name)
            if status:
                setattr(record, "status", status)
            if version:
                setattr(record, "version", version)

            setattr(record, "last_accessed_date", datetime.utcnow())
            await db.commit()
            logger.info(f"Updated ChromaDB instance mapping record for unique_id {unique_id} and tenant_id {tenant_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating ChromaDB instance mapping record: {e}")
            await db.rollback()
            return False
    
    @staticmethod
    async def delete(db: AsyncSession, unique_id: str, tenant_id: str) -> bool:
        """Delete ChromaDB instance mapping record by unique ID and tenant ID"""
        try:
            record = await ChromaDBMappingController.get_by_unique_id_and_tenant(db, unique_id, tenant_id)
            if not record:
                return False

            await db.delete(record)
            await db.commit()
            logger.info(f"Deleted ChromaDB instance mapping record for unique_id {unique_id} and tenant_id {tenant_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting ChromaDB instance mapping record: {e}")
            await db.rollback()
            return False
    
    @staticmethod
    async def delete_by_unique_id_and_tenant(db: AsyncSession, unique_id: str, tenant_id: str) -> bool:
        """Delete ChromaDB instance mapping record by unique ID and tenant ID"""
        try:
            record = await ChromaDBMappingController.get_by_unique_id_and_tenant(db, unique_id, tenant_id)
            if not record:
                return False
            
            await db.delete(record)
            await db.commit()
            logger.info(f"Deleted ChromaDB instance mapping record for unique_id {unique_id} and tenant_id {tenant_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting ChromaDB instance mapping record: {e}")
            await db.rollback()
            return False
    
    @staticmethod
    async def update_version(
        db: AsyncSession,
        unique_id: str,
        tenant_id: str,
        new_version: int
    ) -> bool:
        """Update the version of a ChromaDB instance mapping record to a specific value using unique_id and tenant_id."""
        try:
            record = await ChromaDBMappingController.get_by_unique_id_and_tenant(db, unique_id, tenant_id)
            if not record:
                return False
            setattr(record, "version", new_version)
            setattr(record, "last_accessed_date", datetime.utcnow())
            await db.commit()
            logger.info(f"Updated version for ChromaDB instance mapping record ({unique_id}, {tenant_id}) to {new_version}")
            return True
        except Exception as e:
            logger.error(f"Error updating version for ChromaDB instance mapping record: {e}")
            await db.rollback()
            return False

    @staticmethod
    async def increment_version(
        db: AsyncSession,
        unique_id: str,
        tenant_id: str
    ) -> Optional[int]:
        """Increment the version of a ChromaDB instance mapping record by 1 using unique_id and tenant_id, and return the new version."""
        try:
            record = await ChromaDBMappingController.get_by_unique_id_and_tenant(db, unique_id, tenant_id)
            if not record:
                return None
            current_version = getattr(record, "version", None)
            if current_version is None:
                new_version = 1
            else:
                new_version = current_version + 1
            setattr(record, "version", new_version)
            setattr(record, "last_accessed_date", datetime.utcnow())
            await db.commit()
            logger.info(f"Incremented version for ChromaDB instance mapping record ({unique_id}, {tenant_id}) to {new_version}")
            return new_version
        except Exception as e:
            logger.error(f"Error incrementing version for ChromaDB instance mapping record: {e}")
            await db.rollback()
            return None 