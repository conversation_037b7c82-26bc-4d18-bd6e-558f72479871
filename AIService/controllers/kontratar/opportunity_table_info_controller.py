from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from models.kontratar_models import OpportunityTableInfo
from loguru import logger

class OpportunityTableInfoController:
    @staticmethod
    async def get_by_opps_id(opps_id: str, db: AsyncSession) -> Optional[OpportunityTableInfo]:
        """Get a single OpportunityTableInfo record by opps_id (for backward compatibility)."""
        try:
            query = select(OpportunityTableInfo).where(OpportunityTableInfo.opps_id == opps_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting OpportunityTableInfo for opps_id={opps_id}: {e}")
            return None

    @staticmethod
    async def get_all_by_opps_id(opps_id: str, db: AsyncSession) -> List[OpportunityTableInfo]:
        """Get all OpportunityTableInfo records by opps_id."""
        try:
            query = select(OpportunityTableInfo).where(OpportunityTableInfo.opps_id == opps_id)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting all OpportunityTableInfo for opps_id={opps_id}: {e}")
            return [] 