from loguru import logger
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from langchain_core.messages import BaseMessageChunk
from pydantic import BaseModel
from typing import Iterator, Optional, List
from services.proposal.chat_service import ChatService
from loguru import logger

from models.kontratar_models import GSAContractors
from models.kontratar_models import GSAContractDetails

# Create router with /chats prefix
router = APIRouter(prefix="/contractors", tags=["contractors"])

from controllers.kontratar.gsa_contractors import GSAContractorsController
from controllers.kontratar.gsa_contrators_details import GSAContractDetailsController
from sqlalchemy.ext.asyncio import AsyncSession
from database import get_kontratar_db
from sqlalchemy.ext.asyncio import AsyncSession

class ContractorSearchRequest(BaseModel):
    email: Optional[str] = None
    naics: Optional[str] = None
    govt_poc_name: Optional[str] = None
    govt_poc_email: Optional[str] = None
    website: Optional[str] = None
    socio_economic: Optional[str] = None
    limit: int = 20
    offset: int = 0

@router.get("/gsa")
async def get_all_gsa_contractors(
    limit: int = 20,
    offset: int = 0
):
    """
    Get all GSA Contractors with optional pagination.
    
    Args:
        limit: Maximum number of results to return
        offset: Number of results to skip for pagination
        
    Returns:
        List of GSA Contractors
    """
    try:
        logger.info(f"Received request to get all GSA Contractors (limit={limit}, offset={offset})")
        
        async for db in get_kontratar_db():
            results = await GSAContractorsController.get_all(
                db=db,
                limit=limit,
                offset=offset
            )
            break
        
        return {
            "contractors": results,
            "count": len(results)
        }
        
    except Exception as e:
        logger.error(f"Error in get all GSA Contractors endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/gsa/search")
async def search_gsa_contractors(
    request: ContractorSearchRequest
):
    """
    Search GSA Contractors with multiple optional filters.
    
    Args:
        request: Search parameters for GSA Contractors
        
    Returns:
        List of GSA Contractors matching the search criteria
    """
    try:
        logger.info(f"Received GSA Contractors search request: {request}")
        
        async for db in get_kontratar_db():
            results = await GSAContractorsController.search(
                db=db,
                email=request.email,
                naics=request.naics,
                govt_poc_name=request.govt_poc_name,
                govt_poc_email=request.govt_poc_email,
                website=request.website,
                socio_economic=request.socio_economic,
                limit=request.limit,
                offset=request.offset
            )
            break
        
        return {
            "contractors": results,
            "count": len(results)
        }
        
    except Exception as e:
        logger.error(f"Error in GSA Contractors search endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/gsa/{gsa_contractor_id}")
async def get_gsa_contractor_by_id(
    gsa_contractor_id: int
):
    """
    Get a single GSA Contractor by ID.
    
    Args:
        gsa_contractor_id: ID of the GSA Contractor
        
    Returns:
        GSA Contractor details
    """
    try:
        logger.info(f"Received request for GSA Contractor ID: {gsa_contractor_id}")
        
        async for db in get_kontratar_db():
            contractor = await GSAContractorsController.get_by_id(
                gsa_contractor_id=gsa_contractor_id, 
                db=db
            )
            break
        
        if not contractor:
            raise HTTPException(status_code=404, detail="GSA Contractor not found")
        
        return contractor
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting GSA Contractor: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/gsa/{gsa_contractor_id}/contract-details")
async def get_gsa_contractor_details(
    gsa_contractor_id: int
):
    """
    Get all Contract Details for a specific GSA Contractor.
    
    Args:
        gsa_contractor_id: ID of the GSA Contractor
        
    Returns:
        List of Contract Details for the Contractor
    """
    try:
        logger.info(f"Received request for Contract Details for Contractor ID: {gsa_contractor_id}")
        
        async for db in get_kontratar_db():
            contract_details = await GSAContractDetailsController.get_by_contractor_id(
                contractor_id=gsa_contractor_id, 
                db=db
            )
            break
        
        return {
            "contract_details": contract_details,
            "count": len(contract_details)
        }
        
    except Exception as e:
        logger.error(f"Error getting Contract Details: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/gsa/contract-details/{contract_detail_id}")
async def get_gsa_contract_detail_by_id(
    contract_detail_id: int
):
    """
    Get a single GSA Contract Detail by ID.
    
    Args:
        contract_detail_id: ID of the Contract Detail
        
    Returns:
        GSA Contract Detail
    """
    try:
        logger.info(f"Received request for Contract Detail ID: {contract_detail_id}")
        
        async for db in get_kontratar_db():
            contract_detail = await GSAContractDetailsController.get_by_id(
                gsa_contract_detail_id=contract_detail_id, 
                db=db
            )
            break
        
        if not contract_detail:
            raise HTTPException(status_code=404, detail="GSA Contract Detail not found")
        
        return contract_detail
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Contract Detail: {e}")
        raise HTTPException(status_code=500, detail=str(e))
