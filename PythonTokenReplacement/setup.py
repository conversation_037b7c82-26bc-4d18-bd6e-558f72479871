from setuptools import setup, find_packages

setup(
    name='PythonTokenReplacement',
    version='0.1.0',
    author='Kontratar',
    author_email='<EMAIL>',
    description='A utility for replacing tokens in .env files using values from .env.ini files.',
    packages=find_packages(where='src'),
    package_dir={'': 'src'},
    install_requires=[
        # List your project dependencies here
    ],
    classifiers=[
        'Programming Language :: Python :: 3',
        'License :: OSI Approved :: MIT License',
        'Operating System :: OS Independent',
    ],
    python_requires='>=3.6',
)