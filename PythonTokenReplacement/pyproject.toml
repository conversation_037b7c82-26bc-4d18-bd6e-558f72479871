[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "python-token-replacement"
version = "1.0.0"
description = "Python utility for replacing parameterized tokens in template files"
authors = [{name = "Your Name", email = "<EMAIL>"}]
license = {text = "MIT"}
requires-python = ">=3.7"
dependencies = [
    "configparser>=5.3.0"
]

[project.scripts]
token-replace = "token_replacement.runner:main"

[tool.setuptools.packages.find]
where = ["src"]
include = ["token_replacement*"]