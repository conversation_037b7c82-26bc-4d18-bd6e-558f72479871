"""Command line runner for token replacement utility"""

import argparse
import os
import sys
from pathlib import Path
from typing import Dict, List
from .service import TokenReplacementService
from .exceptions import TokenReplacementError

class TokenReplacementRunner:
    """Command line interface for token replacement
    
    Supports:
    - Single file processing
    - Directory batch processing
    - Environment variable overrides
    - Flexible configuration options
    """
    
    def __init__(self):
        self.service = None
    
    def create_parser(self) -> argparse.ArgumentParser:
        """Create command line argument parser"""
        parser = argparse.ArgumentParser(
            description="Python Token Replacement Utility - Replace parameterized tokens in template files",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""Examples:
  Single file processing:
    python main.py -c config.env.ini -t template.env.template -o output.env
    
  Directory processing:
    python main.py -c config.env.ini -d /path/to/templates
    
  With environment overrides:
    python main.py -c config.env.ini -t template.env.template -o output.env -E OS=Linux ENV=production
            """
        )
        
        # Configuration
        parser.add_argument(
            '-c', '--config',
            required=True,
            help='Path to .env.ini configuration file'
        )
        
        # Processing mode: single file or directory
        processing_group = parser.add_mutually_exclusive_group(required=True)
        
        processing_group.add_argument(
            '-t', '--template',
            help='Path to single template file to process'
        )
        
        processing_group.add_argument(
            '-d', '--directory',
            help='Directory to process (finds all .template files)'
        )
        
        # Output options
        parser.add_argument(
            '-o', '--output',
            help='Path to output file (required for single file processing)'
        )
        
        parser.add_argument(
            '--extension',
            default='.template',
            help='Template file extension for directory processing (default: .template)'
        )
        
        # Service configuration
        parser.add_argument(
            '-s', '--service',
            default='ai-service',
            help='Service name (default: ai-service)'
        )
        
        # Environment overrides
        parser.add_argument(
            '-E', '--env-override',
            action='append',
            dest='env_overrides',
            help='Environment variable override in format name=value (can be used multiple times)'
        )
        
        # Backup options
        parser.add_argument(
            '--no-backup',
            action='store_true',
            help='Skip creating backup of existing output files'
        )
        
        # Verbose output
        parser.add_argument(
            '-v', '--verbose',
            action='store_true',
            help='Enable verbose output'
        )
        
        return parser
    
    def parse_env_overrides(self, env_override_list: List[str]) -> Dict[str, str]:
        """Parse environment override arguments"""
        env_overrides = {}
        
        if not env_override_list:
            return env_overrides
        
        for override in env_override_list:
            if '=' not in override:
                print(f"Warning: Invalid environment override format: {override} (expected name=value)")
                continue
            
            name, value = override.split('=', 1)
            env_overrides[name.strip()] = value.strip()
            
        return env_overrides
    
    def run(self, args=None) -> int:
        """Run the token replacement process"""
        parser = self.create_parser()
        parsed_args = parser.parse_args(args)
        
        try:
            # Validate arguments
            if parsed_args.template and not parsed_args.output:
                print("Error: Output file (-o) is required when processing a single template file", file=sys.stderr)
                return 1
            
            # Parse environment overrides
            env_overrides = self.parse_env_overrides(parsed_args.env_overrides or [])
            
            # Initialize service with environment overrides
            self.service = TokenReplacementService(parsed_args.service, env_overrides)
            
            print(f"Starting token replacement for service: {parsed_args.service}")
            print(f"Config file: {parsed_args.config}")
            
            if env_overrides:
                print(f"Environment overrides: {len(env_overrides)} variable(s)")
                if parsed_args.verbose:
                    for name, value in env_overrides.items():
                        print(f"  {name} = {value}")
            
            print("-" * 50)
            
            # Load configuration
            self.service.load_config(parsed_args.config)
            
            # Determine processing mode
            create_backup = not parsed_args.no_backup
            
            if parsed_args.template:
                # Single file processing
                print(f"Template file: {parsed_args.template}")
                print(f"Output file: {parsed_args.output}")
                print(f"Create backup: {create_backup}")
                print("")
                
                self.service.process_template(
                    parsed_args.template,
                    parsed_args.output,
                    create_backup
                )
                
                print("-" * 50)
                print("Token replacement completed successfully!")
                
            elif parsed_args.directory:
                # Directory processing
                print(f"Processing directory: {parsed_args.directory}")
                print(f"Template extension: {parsed_args.extension}")
                print(f"Create backups: {create_backup}")
                print("")
                
                # Temporarily set backup preference for directory processing
                original_process_template = self.service.process_template
                def backup_aware_process_template(template_path, output_path, backup=create_backup):
                    return original_process_template(template_path, output_path, backup)
                self.service.process_template = backup_aware_process_template
                
                output_files = self.service.process_directory(
                    parsed_args.directory,
                    parsed_args.extension
                )
                
                print("-" * 50)
                print(f"Directory processing completed! Generated {len(output_files)} file(s):")
                for output_file in output_files:
                    print(f"  📄 {output_file}")
            
            return 0
            
        except TokenReplacementError as e:
            print(f"Error: {e}", file=sys.stderr)
            return 1
        except Exception as e:
            print(f"Unexpected error: {e}", file=sys.stderr)
            if parsed_args.verbose:
                import traceback
                traceback.print_exc()
            return 1

def main():
    """Entry point for command line usage"""
    runner = TokenReplacementRunner()
    sys.exit(runner.run())