"""Custom exceptions for token replacement utility"""

class TokenReplacementError(Exception):
    """Base exception for token replacement errors"""
    pass

class ConfigFileNotFoundError(TokenReplacementError):
    """Raised when configuration file is not found"""
    pass

class TemplateFileNotFoundError(TokenReplacementError):
    """Raised when template file is not found"""
    pass

class TokenNotFoundError(TokenReplacementError):
    """Raised when a required token is not found in config"""
    pass

class DirectoryNotFoundError(TokenReplacementError):
    """Raised when a specified directory is not found"""
    pass

class ConditionalProcessingError(TokenReplacementError):
    """Raised when there's an error in conditional directive processing"""
    pass