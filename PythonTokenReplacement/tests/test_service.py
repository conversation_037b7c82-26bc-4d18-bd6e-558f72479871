import unittest
import tempfile
import os
from token_replacement.service import TokenReplacementService
from token_replacement.exceptions import ConfigFileNotFoundError, TemplateFileNotFoundError, TokenNotFoundError

class TestTokenReplacementService(unittest.TestCase):

    def setUp(self):
        self.service = TokenReplacementService("test-service")
        self.temp_dir = tempfile.mkdtemp()

    def test_add_config_value(self):
        # Test adding configuration values
        self.service.add_config_value('TEST_TOKEN', 'test_value')
        self.assertEqual(self.service.get_config_value('TEST_TOKEN'), 'test_value')
        self.assertTrue(self.service.has_config_value('TEST_TOKEN'))
    
    def test_environment_overrides(self):
        # Test environment variable overrides
        self.service.add_environment_override('OS', 'Linux')
        self.assertEqual(self.service.get_environment_value('OS'), 'Linux')

    def test_replace_tokens_basic(self):
        # Test basic token replacement with &token; syntax
        self.service.add_config_value('DB_HOST', 'localhost')
        self.service.add_config_value('DB_PORT', '5432')
        template = "DB_HOST=&DB_HOST;\nDB_PORT=&DB_PORT;\nDB_CONNECTION_STRING=******************************************"
        expected_output = "DB_HOST=localhost\nDB_PORT=5432\nDB_CONNECTION_STRING=*************************************"
        processed = self.service.replace_tokens(template)
        self.assertEqual(processed, expected_output)

    def test_missing_token_commenting(self):
        # Test that lines with missing tokens are commented out
        template = "DB_HOST=&DB_HOST;\nDB_PASSWORD=&DB_PASSWORD;"
        self.service.add_config_value('DB_HOST', 'localhost')
        processed = self.service.replace_tokens(template)
        expected_output = "DB_HOST=localhost\n# DB_PASSWORD=&DB_PASSWORD;"
        self.assertEqual(processed, expected_output)
    
    def test_conditional_processing(self):
        # Test #if/#else/#endif conditional processing
        self.service.add_environment_override('ENVIRONMENT', 'development')
        template = "#if ENVIRONMENT=development\nDEBUG=true\n#else\nDEBUG=false\n#endif\nAPP_NAME=test"
        processed = self.service.replace_tokens(template)
        expected_output = "DEBUG=true\nAPP_NAME=test"
        self.assertEqual(processed, expected_output)
    
    def test_conditional_processing_else(self):
        # Test #if/#else/#endif with else branch
        self.service.add_environment_override('ENVIRONMENT', 'production')
        template = "#if ENVIRONMENT=development\nDEBUG=true\n#else\nDEBUG=false\n#endif\nAPP_NAME=test"
        processed = self.service.replace_tokens(template)
        expected_output = "DEBUG=false\nAPP_NAME=test"
        self.assertEqual(processed, expected_output)

    def test_backup_functionality(self):
        # Test backup creation
        test_file = os.path.join(self.temp_dir, 'test.env')
        with open(test_file, 'w') as f:
            f.write('OLD_CONTENT=value')
        
        backup_path = self.service.create_backup(test_file)
        self.assertTrue(os.path.exists(backup_path))
        self.assertTrue(backup_path.endswith('.env'))
        
        with open(backup_path, 'r') as f:
            content = f.read()
            self.assertEqual(content, 'OLD_CONTENT=value')
    
    def tearDown(self):
        # Clean up temporary directory
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

if __name__ == '__main__':
    unittest.main()