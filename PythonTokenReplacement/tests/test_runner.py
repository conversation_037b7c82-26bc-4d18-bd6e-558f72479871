import unittest
import tempfile
import os
from token_replacement.runner import TokenReplacementRunner

class TestTokenReplacementRunner(unittest.TestCase):

    def setUp(self):
        self.runner = TokenReplacementRunner()
        self.temp_dir = tempfile.mkdtemp()

    def test_parse_env_overrides(self):
        # Test parsing environment overrides
        overrides = self.runner.parse_env_overrides(['OS=Linux', 'ENVIRONMENT=development'])
        expected = {'OS': 'Linux', 'ENVIRONMENT': 'development'}
        self.assertEqual(overrides, expected)
    
    def test_parse_env_overrides_invalid(self):
        # Test parsing invalid environment overrides
        overrides = self.runner.parse_env_overrides(['INVALID_FORMAT', 'VALID=value'])
        expected = {'VALID': 'value'}
        self.assertEqual(overrides, expected)
    
    def test_create_parser(self):
        # Test that parser is created correctly
        parser = self.runner.create_parser()
        self.assertIsNotNone(parser)
        
        # Test that required arguments are properly configured
        args = parser.parse_args(['-c', 'config.ini', '-t', 'template.env', '-o', 'output.env'])
        self.assertEqual(args.config, 'config.ini')
        self.assertEqual(args.template, 'template.env')
        self.assertEqual(args.output, 'output.env')
    
    def tearDown(self):
        # Clean up temporary directory
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

if __name__ == '__main__':
    unittest.main()